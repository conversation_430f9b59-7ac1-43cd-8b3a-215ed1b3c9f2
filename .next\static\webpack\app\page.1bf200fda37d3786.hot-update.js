"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/documents/document-upload.tsx":
/*!******************************************************!*\
  !*** ./src/components/documents/document-upload.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentUpload: () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ DocumentUpload auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DocumentUpload() {\n    _s();\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fetch document status on component mount and periodically\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentUpload.useEffect\": ()=>{\n            fetchDocumentStatus();\n            const interval = setInterval(fetchDocumentStatus, 5000); // Check every 5 seconds\n            return ({\n                \"DocumentUpload.useEffect\": ()=>clearInterval(interval)\n            })[\"DocumentUpload.useEffect\"];\n        }\n    }[\"DocumentUpload.useEffect\"], []);\n    const fetchDocumentStatus = async ()=>{\n        try {\n            const response = await fetch('/api/documents/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUploadedFiles(data.documents.map((doc)=>({\n                        id: doc.id,\n                        name: doc.originalName || doc.filename,\n                        size: doc.size,\n                        type: '',\n                        status: doc.status,\n                        errorMessage: doc.errorMessage\n                    })));\n            }\n        } catch (error) {\n            console.error('Failed to fetch document status:', error);\n        }\n    };\n    const handleUpload = async (files)=>{\n        try {\n            for (const file of files){\n                // Create FormData for upload\n                const formData = new FormData();\n                formData.append('file', file);\n                // Upload file to API\n                const response = await fetch('/api/documents/upload', {\n                    method: 'POST',\n                    body: formData\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to upload \".concat(file.name));\n                }\n                const result = await response.json();\n                console.log(\"Successfully uploaded: \".concat(file.name));\n            }\n            // Refresh document status after upload\n            await fetchDocumentStatus();\n        } catch (error) {\n            console.error('Upload error:', error);\n            throw error; // Let the enhanced file upload component handle the error\n        }\n    };\n    const handleRemoveFile = async (fileId)=>{\n        try {\n            const response = await fetch(\"/api/documents/\".concat(fileId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                setUploadedFiles((prev)=>prev.filter((file)=>file.id !== fileId));\n            } else {\n                alert('Failed to remove file');\n            }\n        } catch (error) {\n            console.error('Remove file error:', error);\n            alert('Failed to remove file');\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'uploading':\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 16\n                }, this);\n            case 'indexed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'uploading':\n                return 'Uploading...';\n            case 'processing':\n                return 'Processing...';\n            case 'indexed':\n                return 'Ready';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Ready';\n        }\n    };\n    const handleFileSelect = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        setIsUploading(true);\n        try {\n            await handleUpload(Array.from(files));\n        } catch (error) {\n            console.error('Upload failed:', error);\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            setIsUploading(true);\n            try {\n                await handleUpload(Array.from(files));\n            } catch (error) {\n                console.error('Upload failed:', error);\n            } finally{\n                setIsUploading(false);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-2 border-dashed transition-all duration-300 cursor-pointer group \".concat(isDragOver ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg' : 'border-border hover:border-primary/50 hover:bg-muted/30 hover:shadow-md'),\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-300 \".concat(isDragOver ? 'bg-primary text-primary-foreground scale-110' : 'bg-gradient-to-br from-muted to-muted/50 text-muted-foreground group-hover:scale-105 group-hover:from-primary/20 group-hover:to-primary/10'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 transition-transform duration-300 \".concat(isDragOver ? 'scale-110' : 'group-hover:scale-110')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: isDragOver ? 'Drop files here' : 'Upload your documents'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-4\",\n                                children: \"Drag and drop files here, or click to browse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2 text-sm text-muted-foreground mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Max file size: 10 MB\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Max files: 10\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                disabled: isUploading,\n                                children: isUploading ? 'Uploading...' : 'Choose Files'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground mt-2\",\n                                children: \"Supported formats: .txt, .md\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        className: \"hidden\",\n                        accept: \".txt,.md\",\n                        onChange: handleFileSelect\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            uploadedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium\",\n                        children: \"Uploaded Documents\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    uploadedFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatFileSize(file.size)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    getStatusIcon(file.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: getStatusText(file.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    file.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-red-500 mt-1\",\n                                                        children: file.errorMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleRemoveFile(file.id),\n                                        disabled: file.status === 'processing',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this)\n                        }, file.id, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this),\n            uploadedFiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No files uploaded yet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"z9LA/xowQ39FNkw5vGmX3b76ZvI=\");\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/documents/document-upload.tsx\n"));

/***/ })

});