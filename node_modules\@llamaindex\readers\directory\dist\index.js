import { <PERSON>VReader } from '../../csv/dist/index.js';
import { DocxReader } from '../../docx/dist/index.js';
import { HTMLReader } from '../../html/dist/index.js';
import { ImageReader } from '../../image/dist/index.js';
import { MarkdownReader } from '../../markdown/dist/index.js';
import { PDFReader } from '../../pdf/dist/index.js';
import { TextFileReader } from '../../text/dist/index.js';
import { XMLReader } from '../../xml/dist/index.js';
import { fs, path } from '@llamaindex/env';

/*
How it works:
`this.#head` is an instance of `Node` which keeps track of its current value and nests another instance of `Node` that keeps the value that comes after it. When a value is provided to `.enqueue()`, the code needs to iterate through `this.#head`, going deeper and deeper to find the last value. However, iterating through every single item is slow. This problem is solved by saving a reference to the last value as `this.#tail` so that it can reference it to add a new value.
*/ var _computedKey;
class Node {
    constructor(value){
        this.value = value;
    }
}
_computedKey = Symbol.iterator;
class Queue {
    #head;
    #tail;
    #size;
    constructor(){
        this.clear();
    }
    enqueue(value) {
        const node = new Node(value);
        if (this.#head) {
            this.#tail.next = node;
            this.#tail = node;
        } else {
            this.#head = node;
            this.#tail = node;
        }
        this.#size++;
    }
    dequeue() {
        const current = this.#head;
        if (!current) {
            return;
        }
        this.#head = this.#head.next;
        this.#size--;
        return current.value;
    }
    peek() {
        if (!this.#head) {
            return;
        }
        return this.#head.value;
    // TODO: Node.js 18.
    // return this.#head?.value;
    }
    clear() {
        this.#head = undefined;
        this.#tail = undefined;
        this.#size = 0;
    }
    get size() {
        return this.#size;
    }
    *[_computedKey]() {
        let current = this.#head;
        while(current){
            yield current.value;
            current = current.next;
        }
    }
}

function pLimit(concurrency) {
    validateConcurrency(concurrency);
    const queue = new Queue();
    let activeCount = 0;
    const resumeNext = ()=>{
        if (activeCount < concurrency && queue.size > 0) {
            queue.dequeue()();
            // Since `pendingCount` has been decreased by one, increase `activeCount` by one.
            activeCount++;
        }
    };
    const next = ()=>{
        activeCount--;
        resumeNext();
    };
    const run = async (function_, resolve, arguments_)=>{
        const result = (async ()=>function_(...arguments_))();
        resolve(result);
        try {
            await result;
        } catch  {}
        next();
    };
    const enqueue = (function_, resolve, arguments_)=>{
        // Queue `internalResolve` instead of the `run` function
        // to preserve asynchronous context.
        new Promise((internalResolve)=>{
            queue.enqueue(internalResolve);
        }).then(run.bind(undefined, function_, resolve, arguments_));
        (async ()=>{
            // This function needs to wait until the next microtask before comparing
            // `activeCount` to `concurrency`, because `activeCount` is updated asynchronously
            // after the `internalResolve` function is dequeued and called. The comparison in the if-statement
            // needs to happen asynchronously as well to get an up-to-date value for `activeCount`.
            await Promise.resolve();
            if (activeCount < concurrency) {
                resumeNext();
            }
        })();
    };
    const generator = (function_, ...arguments_)=>new Promise((resolve)=>{
            enqueue(function_, resolve, arguments_);
        });
    Object.defineProperties(generator, {
        activeCount: {
            get: ()=>activeCount
        },
        pendingCount: {
            get: ()=>queue.size
        },
        clearQueue: {
            value () {
                queue.clear();
            }
        },
        concurrency: {
            get: ()=>concurrency,
            set (newConcurrency) {
                validateConcurrency(newConcurrency);
                concurrency = newConcurrency;
                queueMicrotask(()=>{
                    // eslint-disable-next-line no-unmodified-loop-condition
                    while(activeCount < concurrency && queue.size > 0){
                        resumeNext();
                    }
                });
            }
        }
    });
    return generator;
}
function validateConcurrency(concurrency) {
    if (!((Number.isInteger(concurrency) || concurrency === Number.POSITIVE_INFINITY) && concurrency > 0)) {
        throw new TypeError('Expected `concurrency` to be a number from 1 and up');
    }
}

/**
 * Recursively traverses a directory and yields all the paths to the files in it.
 * @param dirPath The path to the directory to traverse.
 */ async function* walk(dirPath) {
    const entries = await fs.readdir(dirPath);
    for (const entry of entries){
        const fullPath = `${dirPath}/${entry}`;
        const stats = await fs.stat(fullPath);
        if (stats.isDirectory()) {
            yield* walk(fullPath);
        } else {
            yield fullPath;
        }
    }
}

class AbstractSimpleDirectoryReader {
    constructor(observer){
        this.observer = observer;
    }
    async loadData(params) {
        if (typeof params === "string") {
            params = {
                directoryPath: params
            };
        }
        const { directoryPath, defaultReader = new TextFileReader(), fileExtToReader, numWorkers = 1, overrideReader } = params;
        if (numWorkers < 1 || numWorkers > 9) {
            throw new Error("The number of workers must be between 1 - 9.");
        }
        // Observer can decide to skip the directory
        if (!this.doObserverCheck("directory", directoryPath, 0)) {
            return [];
        }
        // Crates a queue of file paths each worker accesses individually
        const filePathQueue = [];
        for await (const filePath of walk(directoryPath)){
            filePathQueue.push(filePath);
        }
        const processFileParams = {
            defaultReader,
            fileExtToReader,
            overrideReader
        };
        // Uses pLimit to control number of parallel requests
        const limit = pLimit(numWorkers);
        const workerPromises = filePathQueue.map((filePath)=>limit(()=>this.processFile(filePath, processFileParams)));
        const results = await Promise.all(workerPromises);
        // After successful import of all files, directory completion
        // is only a notification for observer, cannot be cancelled.
        this.doObserverCheck("directory", directoryPath, 1);
        return results.flat();
    }
    async processFile(filePath, params) {
        const docs = [];
        try {
            const fileExt = path.extname(filePath).slice(1).toLowerCase();
            // Observer can decide to skip each file
            if (!this.doObserverCheck("file", filePath, 0)) {
                // Skip this file
                return [];
            }
            let reader;
            if (params.overrideReader) {
                reader = params.overrideReader;
            } else if (params.fileExtToReader && fileExt in params.fileExtToReader) {
                reader = params.fileExtToReader[fileExt];
            } else if (params.defaultReader != null) {
                reader = params.defaultReader;
            } else {
                const msg = `No reader for file extension of ${filePath}`;
                console.warn(msg);
                // In an error condition, observer's false cancels the whole process.
                if (!this.doObserverCheck("file", filePath, 2, msg)) {
                    return [];
                }
                return [];
            }
            const fileDocs = await reader.loadData(filePath);
            // Observer can still cancel addition of the resulting docs from this file
            if (this.doObserverCheck("file", filePath, 1)) {
                docs.push(...fileDocs);
            }
        } catch (e) {
            const msg = `Error reading file ${filePath}: ${e}`;
            console.error(msg);
            // In an error condition, observer's false cancels the whole process.
            if (!this.doObserverCheck("file", filePath, 2, msg)) {
                return [];
            }
        }
        return docs;
    }
    doObserverCheck(category, name, status, message) {
        if (this.observer) {
            return this.observer(category, name, status, message);
        }
        return true;
    }
}

const FILE_EXT_TO_READER = {
    txt: new TextFileReader(),
    pdf: new PDFReader(),
    csv: new CSVReader(),
    md: new MarkdownReader(),
    docx: new DocxReader(),
    htm: new HTMLReader(),
    html: new HTMLReader(),
    jpg: new ImageReader(),
    jpeg: new ImageReader(),
    png: new ImageReader(),
    gif: new ImageReader(),
    xml: new XMLReader()
};
/**
 * Read all the documents in a directory.
 * By default, supports the list of file types
 * in the FILE_EXT_TO_READER map.
 */ class SimpleDirectoryReader extends AbstractSimpleDirectoryReader {
    async loadData(params) {
        if (typeof params === "string") {
            params = {
                directoryPath: params
            };
        }
        params.fileExtToReader = params.fileExtToReader ?? FILE_EXT_TO_READER;
        return super.loadData(params);
    }
}

export { FILE_EXT_TO_READER, SimpleDirectoryReader };
