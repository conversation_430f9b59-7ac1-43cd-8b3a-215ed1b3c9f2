import { BaseReader, FileReader, Document } from '@llamaindex/core/schema';

type ReaderCallback = (category: "file" | "directory", name: string, status: ReaderStatus, message?: string) => boolean;
declare enum ReaderStatus {
    STARTED = 0,
    COMPLETE = 1,
    ERROR = 2
}
type SimpleDirectoryReaderLoadDataParams = {
    directoryPath: string;
    defaultReader?: FileReader | null;
    fileExtToReader?: Record<string, FileReader>;
    numWorkers?: number;
    overrideReader?: FileReader;
};
declare class AbstractSimpleDirectoryReader implements BaseReader {
    private observer?;
    constructor(observer?: ReaderCallback | undefined);
    loadData(params: SimpleDirectoryReaderLoadDataParams): Promise<Document[]>;
    loadData(directoryPath: string): Promise<Document[]>;
    private processFile;
    private doObserverCheck;
}

declare class SimpleDirectoryReader extends AbstractSimpleDirectoryReader {
}

export { SimpleDirectoryReader };
