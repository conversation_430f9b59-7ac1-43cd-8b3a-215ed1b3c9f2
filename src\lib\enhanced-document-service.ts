// Enhanced document service with DeepSeek + Doubao integration
import path from "path";
import documentManager, { type DocumentMetadata } from "./document-manager";
import { DeepSeekService } from "./deepseek-llm";
import { DoubaoEmbeddingService } from "./doubao-embedding";

// Create service instances
const deepSeekService = new DeepSeekService({
  apiKey: process.env.DEEPSEEK_API_KEY || "",
  baseURL: process.env.DEEPSEEK_BASE_URL || "https://ark.cn-beijing.volces.com/api/v3",
  model: process.env.DEEPSEEK_CHAT_MODEL || "deepseek-chat",
  temperature: 0.7,
  maxTokens: 4000,
});

const doubaoEmbeddingService = new DoubaoEmbeddingService({
  apiKey: process.env.DOUBAO_API_KEY || "",
  baseURL: process.env.DOUBAO_BASE_URL || "https://ark.cn-beijing.volces.com/api/v3",
  model: process.env.EMBEDDING_MODEL || "doubao-embedding-text-240515",
});

interface DocumentChunk {
  id: string;
  content: string;
  embedding: number[];
  metadata: {
    documentId: string;
    filename: string;
    chunkIndex: number;
    startChar: number;
    endChar: number;
  };
}

interface QueryResult {
  response: string;
  sources: Array<{
    document: string;
    chunk: string;
    relevance: number;
  }>;
}

class EnhancedDocumentService {
  private documents: Map<string, { content: string; metadata: DocumentMetadata }> = new Map();
  private documentChunks: Map<string, DocumentChunk[]> = new Map();
  private readonly chunksDir = path.join(process.cwd(), 'document_chunks');

  async initialize() {
    try {
      // Initialize document manager
      await documentManager.initialize();

      // Ensure chunks directory exists
      await this.ensureChunksDirectory();

      // Reload existing indexed documents into memory
      await this.reloadExistingDocuments();

      console.log(`Enhanced document service initialized with DeepSeek + Doubao integration. Loaded ${this.documents.size} documents.`);
    } catch (error) {
      console.error("Failed to initialize enhanced document service:", error);
      throw error;
    }
  }

  private async ensureChunksDirectory() {
    try {
      const fs = await import('fs/promises');
      await fs.mkdir(this.chunksDir, { recursive: true });
    } catch (error) {
      console.error("Failed to create chunks directory:", error);
    }
  }

  private async saveDocumentChunks(documentId: string, chunks: DocumentChunk[]) {
    try {
      const fs = await import('fs/promises');
      const chunksFilePath = path.join(this.chunksDir, `${documentId}.json`);
      await fs.writeFile(chunksFilePath, JSON.stringify(chunks, null, 2));
      console.log(`Saved ${chunks.length} chunks for document ${documentId}`);
    } catch (error) {
      console.error(`Failed to save chunks for document ${documentId}:`, error);
    }
  }

  private async loadDocumentChunks(documentId: string): Promise<DocumentChunk[] | null> {
    try {
      const fs = await import('fs/promises');
      const chunksFilePath = path.join(this.chunksDir, `${documentId}.json`);

      try {
        await fs.access(chunksFilePath);
      } catch {
        return null; // File doesn't exist
      }

      const chunksData = await fs.readFile(chunksFilePath, 'utf-8');
      const chunks = JSON.parse(chunksData) as DocumentChunk[];
      console.log(`Loaded ${chunks.length} chunks for document ${documentId}`);
      return chunks;
    } catch (error) {
      console.error(`Failed to load chunks for document ${documentId}:`, error);
      return null;
    }
  }

  private async removeDocumentChunks(documentId: string) {
    try {
      const fs = await import('fs/promises');
      const chunksFilePath = path.join(this.chunksDir, `${documentId}.json`);

      try {
        await fs.unlink(chunksFilePath);
        console.log(`Removed chunks file for document ${documentId}`);
      } catch (error) {
        // File might not exist, which is fine
        console.log(`Chunks file for document ${documentId} not found (already removed)`);
      }
    } catch (error) {
      console.error(`Failed to remove chunks for document ${documentId}:`, error);
    }
  }

  private async reloadExistingDocuments() {
    try {
      // Get all indexed documents from document manager
      const indexedDocuments = documentManager.getDocumentsByStatus('indexed');
      console.log(`Found ${indexedDocuments.length} indexed documents to reload`);

      for (const docMetadata of indexedDocuments) {
        try {
          // Construct file path
          const filePath = path.join(process.cwd(), 'uploads', docMetadata.filename);

          // Check if file exists
          const fs = await import('fs/promises');
          try {
            await fs.access(filePath);
          } catch {
            console.warn(`File not found for document ${docMetadata.id}: ${filePath}`);
            // Update status to error since file is missing
            await documentManager.updateDocumentStatus(docMetadata.id, 'error', 'File not found on disk');
            continue;
          }

          // Read document content
          const content = await fs.readFile(filePath, 'utf-8');

          // Store document in memory
          this.documents.set(docMetadata.id, {
            content,
            metadata: docMetadata,
          });

          // Try to load existing chunks first
          const existingChunks = await this.loadDocumentChunks(docMetadata.id);
          if (existingChunks && existingChunks.length > 0) {
            // Use existing chunks
            this.documentChunks.set(docMetadata.id, existingChunks);
            console.log(`Loaded existing ${existingChunks.length} chunks for document: ${docMetadata.originalName}`);
          } else {
            // Regenerate chunks and embeddings
            await this.regenerateDocumentEmbeddings(docMetadata.id, content, docMetadata.filename);
          }

          console.log(`Reloaded document: ${docMetadata.originalName} (${docMetadata.id})`);
        } catch (docError) {
          console.error(`Failed to reload document ${docMetadata.id}:`, docError);
          // Update status to error
          await documentManager.updateDocumentStatus(
            docMetadata.id,
            'error',
            `Failed to reload: ${docError instanceof Error ? docError.message : 'Unknown error'}`
          );
        }
      }
    } catch (error) {
      console.error("Failed to reload existing documents:", error);
      // Don't throw here - we want the service to still initialize even if some documents fail to reload
    }
  }

  private async regenerateDocumentEmbeddings(documentId: string, content: string, filename: string) {
    try {
      // Chunk the document
      const textChunks = this.chunkText(content);

      // Generate embeddings for each chunk
      const chunks: DocumentChunk[] = [];
      for (let i = 0; i < textChunks.length; i++) {
        const chunkContent = textChunks[i];

        try {
          const embedding = await doubaoEmbeddingService.getEmbedding(chunkContent);

          chunks.push({
            id: `${documentId}_chunk_${i}`,
            content: chunkContent,
            embedding,
            metadata: {
              documentId,
              filename,
              chunkIndex: i,
              startChar: i * 800, // Approximate start position
              endChar: i * 800 + chunkContent.length,
            },
          });
        } catch (embeddingError) {
          console.error(`Failed to generate embedding for chunk ${i} of document ${documentId}:`, embeddingError);
          // Continue with other chunks even if one fails
        }
      }

      // Store chunks in memory and save to disk
      this.documentChunks.set(documentId, chunks);
      await this.saveDocumentChunks(documentId, chunks);
      console.log(`Generated ${chunks.length} chunks with embeddings for document ${filename}`);
    } catch (error) {
      console.error(`Failed to regenerate embeddings for document ${documentId}:`, error);
      throw error;
    }
  }

  private chunkText(text: string, chunkSize: number = 1000, overlap: number = 200): string[] {
    const chunks: string[] = [];
    let start = 0;

    while (start < text.length) {
      const end = Math.min(start + chunkSize, text.length);
      const chunk = text.slice(start, end);
      chunks.push(chunk);
      
      if (end === text.length) break;
      start = end - overlap;
    }

    return chunks;
  }

  async addDocument(filePath: string, documentId: string, filename: string, fileSize: number): Promise<void> {
    try {
      await this.initialize();

      // Update document status to processing
      await documentManager.updateDocumentStatus(documentId, 'processing');

      // Read the document content
      const fs = await import('fs/promises');
      const content = await fs.readFile(filePath, 'utf-8');

      // Store document
      this.documents.set(documentId, {
        content,
        metadata: {
          id: documentId,
          filename,
          originalName: filename,
          size: fileSize,
          uploadDate: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          type: path.extname(filename),
          status: 'processing',
        },
      });

      // Chunk the document
      const textChunks = this.chunkText(content);
      
      // Generate embeddings for each chunk
      const chunks: DocumentChunk[] = [];
      for (let i = 0; i < textChunks.length; i++) {
        const chunkContent = textChunks[i];
        
        try {
          const embedding = await doubaoEmbeddingService.getEmbedding(chunkContent);
          
          chunks.push({
            id: `${documentId}_chunk_${i}`,
            content: chunkContent,
            embedding,
            metadata: {
              documentId,
              filename,
              chunkIndex: i,
              startChar: i * 800, // Approximate start position
              endChar: i * 800 + chunkContent.length,
            },
          });
        } catch (embeddingError) {
          console.error(`Failed to generate embedding for chunk ${i}:`, embeddingError);
          // Continue with other chunks even if one fails
        }
      }

      // Store chunks in memory and save to disk
      this.documentChunks.set(documentId, chunks);
      await this.saveDocumentChunks(documentId, chunks);

      // Update document status to indexed
      await documentManager.updateDocumentStatus(documentId, 'indexed');

      console.log(`Document ${filename} processed with ${chunks.length} chunks and embeddings`);
    } catch (error) {
      console.error("Failed to add document:", error);
      await documentManager.updateDocumentStatus(documentId, 'error', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  async removeDocument(documentId: string): Promise<void> {
    try {
      await this.initialize();

      // Remove from document manager
      await documentManager.removeDocument(documentId);

      // Remove from our stores
      this.documents.delete(documentId);
      this.documentChunks.delete(documentId);

      // Remove chunks file from disk
      await this.removeDocumentChunks(documentId);

      console.log(`Document ${documentId} removed from enhanced document store`);
    } catch (error) {
      console.error("Failed to remove document:", error);
      throw error;
    }
  }

  async query(message: string, _chatHistory: Array<{ role: string; content: string }> = []): Promise<QueryResult> {
    try {
      await this.initialize();

      console.log(`Query received: "${message}"`);
      console.log(`Documents in memory: ${this.documents.size}`);
      console.log(`Document chunks available: ${Array.from(this.documentChunks.values()).flat().length}`);

      // Check if we have any documents
      if (this.documents.size === 0) {
        console.log("No documents found in memory - returning empty response");
        return {
          response: "I don't have any documents to search through. Please upload some documents first and wait for them to be processed.",
          sources: [],
        };
      }

      // Generate embedding for the query
      console.log("Generating embedding for query...");
      const queryEmbedding = await doubaoEmbeddingService.getEmbedding(message);
      console.log(`Query embedding generated with ${queryEmbedding.length} dimensions`);

      // Get all document chunks with embeddings
      const allChunks: Array<{ embedding: number[]; metadata: any; content: string }> = [];
      for (const [documentId, chunks] of this.documentChunks.entries()) {
        console.log(`Processing ${chunks.length} chunks from document ${documentId}`);
        for (const chunk of chunks) {
          allChunks.push({
            embedding: chunk.embedding,
            metadata: {
              documentId,
              filename: chunk.metadata.filename,
              chunkIndex: chunk.metadata.chunkIndex,
            },
            content: chunk.content,
          });
        }
      }

      console.log(`Total chunks available for similarity search: ${allChunks.length}`);

      // Find most similar chunks
      console.log("Finding most similar chunks...");
      const similarChunks = await doubaoEmbeddingService.findMostSimilar(
        queryEmbedding,
        allChunks,
        5 // Top 5 most relevant chunks
      );
      console.log(`Found ${similarChunks.length} similar chunks`);

      // Create context from relevant chunks
      const context = similarChunks
        .map((result, index) => {
          const chunk = allChunks.find(c => 
            c.metadata.documentId === result.metadata.documentId && 
            c.metadata.chunkIndex === result.metadata.chunkIndex
          );
          return `[Chunk ${index + 1} from ${result.metadata.filename}]:\n${chunk?.content || 'Content not found'}`;
        })
        .join('\n\n---\n\n');

      // Use DeepSeek to answer the question with context
      const response = await deepSeekService.queryWithContext(message, context);

      // Create sources from similar chunks
      const sources = similarChunks.map((result) => {
        const chunk = allChunks.find(c => 
          c.metadata.documentId === result.metadata.documentId && 
          c.metadata.chunkIndex === result.metadata.chunkIndex
        );
        return {
          document: result.metadata.filename,
          chunk: chunk?.content.substring(0, 200) + "..." || "Content not available",
          relevance: Math.round(result.similarity * 100) / 100,
        };
      });

      return {
        response,
        sources,
      };
    } catch (error) {
      console.error("Failed to query with DeepSeek + Doubao:", error);
      throw error;
    }
  }

  async getDocuments(): Promise<DocumentMetadata[]> {
    return Array.from(this.documents.values()).map(doc => doc.metadata);
  }

  async getDocumentStats() {
    const documents = Array.from(this.documents.values());
    const chunks = Array.from(this.documentChunks.values()).flat();
    
    return {
      totalDocuments: documents.length,
      totalChunks: chunks.length,
      totalEmbeddings: chunks.filter(c => c.embedding.length > 0).length,
      averageChunksPerDocument: documents.length > 0 ? Math.round(chunks.length / documents.length) : 0,
    };
  }
}

// Singleton instance
const enhancedDocumentService = new EnhancedDocumentService();

export default enhancedDocumentService;
