import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Upload, X, File, Image, FileText, AlertCircle, CheckCircle, RefreshCw, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface FileUploadItem {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  preview?: string;
}

interface EnhancedFileUploadProps {
  maxFileSize?: number;
  maxFiles?: number;
  acceptedTypes?: string[];
  onUpload?: (files: File[]) => Promise<void>;
  onFileRemove?: (fileId: string) => void;
  className?: string;
}

const EnhancedFileUpload: React.FC<EnhancedFileUploadProps> = ({
  maxFileSize = 10 * 1024 * 1024, // 10MB
  maxFiles = 10,
  acceptedTypes = ['.txt', '.md'],
  onUpload,
  onFileRemove,
  className = ''
}) => {
  const [files, setFiles] = useState<FileUploadItem[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [globalError, setGlobalError] = useState<string>('');
  const [previewFile, setPreviewFile] = useState<FileUploadItem | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return Image;
    if (file.type === 'application/pdf') return FileText;
    if (file.type.includes('document') || file.type.includes('text')) return FileText;
    return File;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    if (file.size > maxFileSize) {
      return `File size exceeds ${formatFileSize(maxFileSize)}`;
    }
    
    const isValidType = acceptedTypes.some(type => {
      if (type.includes('*')) {
        return file.type.startsWith(type.replace('*', ''));
      }
      return file.type === type || file.name.toLowerCase().endsWith(type);
    });
    
    if (!isValidType) {
      return 'File type not supported';
    }
    
    return null;
  };

  const createFilePreview = (file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = () => resolve(undefined);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  };

  const addFiles = useCallback(async (newFiles: File[]) => {
    setGlobalError('');
    
    if (files.length + newFiles.length > maxFiles) {
      setGlobalError(`Maximum ${maxFiles} files allowed`);
      return;
    }

    const validFiles: FileUploadItem[] = [];
    
    for (const file of newFiles) {
      const error = validateFile(file);
      const preview = await createFilePreview(file);
      
      validFiles.push({
        id: Math.random().toString(36).substr(2, 9),
        file,
        status: error ? 'error' : 'pending',
        progress: 0,
        error,
        preview
      });
    }

    setFiles(prev => [...prev, ...validFiles]);

    // Auto-upload valid files
    const filesToUpload = validFiles.filter(f => f.status === 'pending');
    if (filesToUpload.length > 0 && onUpload) {
      uploadFiles(filesToUpload);
    }
  }, [files.length, maxFiles, onUpload]);

  const uploadFiles = async (filesToUpload: FileUploadItem[]) => {
    for (const fileItem of filesToUpload) {
      setFiles(prev => prev.map(f => 
        f.id === fileItem.id ? { ...f, status: 'uploading' } : f
      ));

      try {
        // Simulate upload progress
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          setFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { ...f, progress } : f
          ));
        }

        if (onUpload) {
          await onUpload([fileItem.file]);
        }

        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { ...f, status: 'success', progress: 100 } : f
        ));
      } catch (error) {
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { 
            ...f, 
            status: 'error', 
            error: 'Upload failed. Please try again.' 
          } : f
        ));
      }
    }
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
    onFileRemove?.(fileId);
  };

  const retryUpload = (fileId: string) => {
    const fileToRetry = files.find(f => f.id === fileId);
    if (fileToRetry && onUpload) {
      setFiles(prev => prev.map(f => 
        f.id === fileId ? { ...f, status: 'pending', error: undefined, progress: 0 } : f
      ));
      uploadFiles([fileToRetry]);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!dropZoneRef.current?.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    addFiles(droppedFiles);
  }, [addFiles]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    addFiles(selectedFiles);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      fileInputRef.current?.click();
    }
  };

  useEffect(() => {
    const handleGlobalDragOver = (e: DragEvent) => {
      e.preventDefault();
    };

    const handleGlobalDrop = (e: DragEvent) => {
      e.preventDefault();
    };

    document.addEventListener('dragover', handleGlobalDragOver);
    document.addEventListener('drop', handleGlobalDrop);

    return () => {
      document.removeEventListener('dragover', handleGlobalDragOver);
      document.removeEventListener('drop', handleGlobalDrop);
    };
  }, []);

  const successCount = files.filter(f => f.status === 'success').length;
  const errorCount = files.filter(f => f.status === 'error').length;
  const uploadingCount = files.filter(f => f.status === 'uploading').length;

  return (
    <div className={`w-full space-y-6 ${className}`}>
      {/* Drop Zone */}
      <Card
        ref={dropZoneRef}
        className={`
          relative border-2 border-dashed transition-all duration-200 cursor-pointer
          ${isDragOver 
            ? 'border-primary bg-primary/5 scale-[1.02]' 
            : 'border-border hover:border-primary/50 hover:bg-muted/50'
          }
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="button"
        aria-label="Upload files"
      >
        <div className="p-8 text-center">
          <div className={`
            mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-colors
            ${isDragOver ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}
          `}>
            <Upload className="w-8 h-8" />
          </div>
          
          <h3 className="text-lg font-semibold mb-2">
            {isDragOver ? 'Drop files here' : 'Upload your documents'}
          </h3>
          
          <p className="text-muted-foreground mb-4">
            Drag and drop files here, or click to browse
          </p>
          
          <div className="flex flex-wrap justify-center gap-2 text-sm text-muted-foreground">
            <span>Max file size: {formatFileSize(maxFileSize)}</span>
            <span>•</span>
            <span>Max files: {maxFiles}</span>
          </div>
          
          <Button variant="outline" className="mt-4" type="button">
            Choose Files
          </Button>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          aria-hidden="true"
        />
      </Card>

      {/* Global Error */}
      {globalError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{globalError}</AlertDescription>
        </Alert>
      )}

      {/* Upload Summary */}
      {files.length > 0 && (
        <div className="flex flex-wrap gap-4 text-sm">
          <Badge variant="secondary">
            Total: {files.length}
          </Badge>
          {successCount > 0 && (
            <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
              Success: {successCount}
            </Badge>
          )}
          {uploadingCount > 0 && (
            <Badge variant="default" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
              Uploading: {uploadingCount}
            </Badge>
          )}
          {errorCount > 0 && (
            <Badge variant="destructive">
              Errors: {errorCount}
            </Badge>
          )}
        </div>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-3">
          {files.map((fileItem) => {
            const IconComponent = getFileIcon(fileItem.file);
            
            return (
              <Card key={fileItem.id} className="p-4">
                <div className="flex items-center gap-4">
                  {/* File Preview/Icon */}
                  <div className="flex-shrink-0">
                    {fileItem.preview ? (
                      <div className="w-12 h-12 rounded-lg overflow-hidden bg-muted">
                        <img
                          src={fileItem.preview}
                          alt={fileItem.file.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center">
                        <IconComponent className="w-6 h-6 text-muted-foreground" />
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium truncate">{fileItem.file.name}</h4>
                      {fileItem.status === 'success' && (
                        <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                      )}
                      {fileItem.status === 'error' && (
                        <AlertCircle className="w-4 h-4 text-red-600 flex-shrink-0" />
                      )}
                      {fileItem.status === 'uploading' && (
                        <RefreshCw className="w-4 h-4 text-blue-600 animate-spin flex-shrink-0" />
                      )}
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-2">
                      {formatFileSize(fileItem.file.size)}
                    </p>

                    {/* Progress Bar */}
                    {fileItem.status === 'uploading' && (
                      <div className="space-y-1">
                        <Progress value={fileItem.progress} className="h-2" />
                        <p className="text-xs text-muted-foreground">
                          {fileItem.progress}% uploaded
                        </p>
                      </div>
                    )}

                    {/* Error Message */}
                    {fileItem.error && (
                      <p className="text-sm text-red-600">{fileItem.error}</p>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {fileItem.status === 'error' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          retryUpload(fileItem.id);
                        }}
                      >
                        <RefreshCw className="w-4 h-4" />
                      </Button>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(fileItem.id);
                      }}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      )}

      {/* Empty State */}
      {files.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No files uploaded yet</p>
        </div>
      )}
    </div>
  );
};

export default EnhancedFileUpload;
