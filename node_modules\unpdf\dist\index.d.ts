import * as canvas from 'canvas';
import { Canvas } from 'canvas';
import { DocumentInitParameters, PDFDocumentProxy } from './types/src/display/api';
import * as _PDFJS from './types/src/pdf';

interface ExtractedImageObject {
    data: Uint8ClampedArray;
    width: number;
    height: number;
    channels: 1 | 3 | 4;
    key: string;
}
/**
 * Extracts images from a specific page of a PDF document, including necessary metadata,
 * such as width, height, and calculated color channels.
 *
 * This version calculates channels based on image data length, width, and height,
 * as the `kind` property provided by PDF.js might not reliably indicate the actual
 * channel count of the raw pixel data (e.g., returning RGBA data even when kind is 3).
 *
 * @example
 * const imagesData = await extractImages(pdf, pageNum)
 *
 * for (const imgData of imagesData) {
 *   const imageIndex = totalImagesProcessed + 1;
 *   await sharp(imgData.data, {
 *     raw: { width: imgData.width, height: imgData.height, channels: imgData.channels }
 *   })
 *     .png()
 *     .toFile(`${imageIndex}.png`);
 * }
 */
declare function extractImages$1(data: DocumentInitParameters['data'] | PDFDocumentProxy, pageNumber: number): Promise<ExtractedImageObject[]>;
declare function renderPageAsImage$1(data: DocumentInitParameters['data'] | PDFDocumentProxy, pageNumber: number, options?: {
    canvas?: () => Promise<typeof canvas>;
    /** @default 1 */
    scale?: number;
    width?: number;
    height?: number;
}): Promise<ArrayBuffer>;
declare function createIsomorphicCanvasFactory(canvas?: () => Promise<typeof canvas>): Promise<{
    _createCanvas(width: number, height: number): Canvas | HTMLCanvasElement;
    create(width: number, height: number): {
        canvas: Canvas | HTMLCanvasElement;
        context: CanvasRenderingContext2D;
    };
    reset(ctx: {
        canvas?: Canvas;
        context?: CanvasRenderingContext2D;
    }, width: number, height: number): void;
    destroy(ctx: {
        canvas?: Canvas;
        context?: CanvasRenderingContext2D;
    }): void;
}>;

declare function getMeta$1(data: DocumentInitParameters['data'] | PDFDocumentProxy): Promise<{
    info: Record<string, any>;
    metadata: Record<string, any>;
}>;

declare function extractText$1(data: DocumentInitParameters['data'] | PDFDocumentProxy, options?: {
    mergePages?: false;
}): Promise<{
    totalPages: number;
    text: string[];
}>;
declare function extractText$1(data: DocumentInitParameters['data'] | PDFDocumentProxy, options: {
    mergePages: true;
}): Promise<{
    totalPages: number;
    text: string;
}>;

type PDFJS = typeof _PDFJS;
interface UnPDFConfiguration {
    /**
     * By default, UnPDF will use the latest version of PDF.js compiled for
     * serverless environments. If you want to use a different version, you can
     * provide a custom resolver function.
     *
     * @example
     * // Use the official PDF.js build (make sure to install it first)
     * () => import('pdfjs-dist')
     */
    pdfjs?: () => Promise<any>;
}

declare function configureUnPDF(options: UnPDFConfiguration): Promise<void>;

/**
 * Returns a PDFDocumentProxy instance from a given binary data.
 *
 * Applies the following defaults:
 * - `isEvalSupported: false`
 * - `useSystemFonts: true`
 */
declare function getDocumentProxy(data: DocumentInitParameters['data'], options?: DocumentInitParameters): Promise<PDFDocumentProxy>;
declare function getResolvedPDFJS(): Promise<PDFJS>;
declare function resolvePDFJSImports(pdfjsResolver?: () => Promise<any>, { force }?: {
    force?: boolean | undefined;
}): Promise<void>;

declare const getMeta: typeof getMeta$1;
declare const extractText: typeof extractText$1;
declare const extractImages: typeof extractImages$1;
declare const renderPageAsImage: typeof renderPageAsImage$1;

export { configureUnPDF, createIsomorphicCanvasFactory, extractImages, extractText, getDocumentProxy, getMeta, getResolvedPDFJS, renderPageAsImage, resolvePDFJSImports };
