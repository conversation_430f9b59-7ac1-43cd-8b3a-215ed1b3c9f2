interface DeepSeekConfig {
  apiKey: string;
  baseURL: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

interface ChatMessage {
  role: string;
  content: string;
}

interface ChatResponse {
  message: {
    role: string;
    content: string;
  };
  raw?: any;
}

export class DeepSeekService {
  private config: Required<DeepSeekConfig>;

  constructor(config: DeepSeekConfig) {
    this.config = {
      temperature: 0.7,
      maxTokens: 4000,
      ...config,
    };
  }

  async chat(messages: ChatMessage[]): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          model: this.config.model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          temperature: this.config.temperature,
          max_tokens: this.config.maxTokens,
          stream: false,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response from DeepSeek API');
      }

      const choice = data.choices[0];
      return {
        message: {
          role: choice.message.role,
          content: choice.message.content,
        },
        raw: data,
      };
    } catch (error) {
      console.error('DeepSeek LLM error:', error);
      throw error;
    }
  }

  async queryWithContext(message: string, context: string): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are a helpful AI assistant that answers questions based on the provided document context. Use the context to provide accurate and relevant answers. If the context doesn't contain enough information to answer the question, say so clearly.

Context:
${context}`
      },
      {
        role: 'user',
        content: message
      }
    ];

    const response = await this.chat(messages);
    return response.message.content;
  }
}
