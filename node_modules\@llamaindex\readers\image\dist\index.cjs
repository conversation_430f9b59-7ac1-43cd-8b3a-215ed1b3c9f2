Object.defineProperty(exports, '__esModule', { value: true });

var schema = require('@llamaindex/core/schema');

/**
 * Reads the content of an image file into a Document object (which stores the image file as a Blob).
 */ class ImageReader extends schema.FileReader {
    /**
   * Public method for this reader.
   * Required by BaseReader interface.
   * @param fileContent - The content of the file.
   * @returns `Promise<Document[]>` A Promise object, eventually yielding zero or one ImageDocument of the specified file.
   */ async loadDataAsContent(fileContent) {
        const blob = new Blob([
            fileContent
        ]);
        return [
            new schema.ImageDocument({
                image: blob
            })
        ];
    }
}

exports.ImageReader = ImageReader;
