import { ChatInterface } from "@/components/chat/chat-interface";
import { DocumentUpload } from "@/components/documents/document-upload";
import { UrlIngestion } from "@/components/documents/url-ingestion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="mb-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            ChatDoc
          </h1>
          <p className="text-muted-foreground text-lg md:text-xl max-w-2xl mx-auto">
            Upload documents and chat with them using AI
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Document Upload Section */}
          <div className="lg:col-span-1 space-y-6">
            <Card className="shadow-lg border-0 bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                    📄
                  </div>
                  Upload Documents
                </CardTitle>
                <CardDescription>
                  Upload .txt or .md files to start chatting
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DocumentUpload />
              </CardContent>
            </Card>

            {/* URL Ingestion Section */}
            <UrlIngestion />
          </div>

          {/* Chat Interface Section */}
          <div className="lg:col-span-2">
            <Card className="h-[600px] shadow-lg border-0 bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                    💬
                  </div>
                  Chat with Documents
                </CardTitle>
                <CardDescription>
                  Ask questions about your uploaded documents
                </CardDescription>
              </CardHeader>
              <CardContent className="h-full p-0">
                <div className="h-full rounded-lg overflow-hidden">
                  <ChatInterface />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
