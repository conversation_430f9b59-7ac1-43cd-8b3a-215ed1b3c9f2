import { <PERSON><PERSON>gentParams, LLMAgentWorker, LLMAgent } from '@llamaindex/core/agent';
import { LiveLLMSession, MessageSender, AudioConfig, BaseTool, LiveLLM, LiveConnectConfig, ToolCallLLM, LLMMetadata, MessageType, ChatMessage, ToolCallLLMMessageOptions, LLMChatParamsStreaming, ChatResponseChunk, LLMChatParamsNonStreaming, ChatResponse } from '@llamaindex/core/llms';
import { OpenAI as OpenAI$1, ClientOptions } from 'openai';
import { ChatModel as ChatModel$1 } from 'openai/resources/chat/chat';
import { ChatCompletionRole, ChatCompletionTool } from 'openai/resources/chat/completions';
import { ChatCompletionMessageParam } from 'openai/resources/index.js';
import { ChatModel } from 'openai/resources.mjs';
import { BaseEmbedding } from '@llamaindex/core/embeddings';
import { Tokenizers } from '@llamaindex/env/tokenizers';
import { StoredValue } from '@llamaindex/core/schema';

type ServerEvent = Record<string, any>;
declare class OpenAILiveSession extends LiveLLMSession {
    closed: boolean;
    audioStream: MediaStream | undefined;
    peerConnection: RTCPeerConnection | undefined;
    dataChannel: RTCDataChannel | undefined;
    get messageSender(): MessageSender;
    private isResponseDoneEvent;
    private isTextEvent;
    private isFunctionCallEvent;
    private isAudioDeltaEvent;
    private isInterruptedEvent;
    setupAudioTracks(config?: AudioConfig): void;
    handleEvents(event: ServerEvent, toolCalls: BaseTool[]): Promise<void>;
    private sendToolCallResponses;
    sendResponseCreateEvent(): Promise<void>;
    private sendToolCallResponse;
    private handleFunctionCall;
    disconnect(): Promise<void>;
    setPeerConnection(pc: RTCPeerConnection): void;
    setDataChannel(dc: RTCDataChannel): void;
}

type OpenAIAdditionalMetadata = object;
type OpenAIAdditionalChatOptions = Omit<Partial<OpenAI$1.Chat.ChatCompletionCreateParams>, "max_tokens" | "messages" | "model" | "temperature" | "reasoning_effort" | "top_p" | "stream" | "tools" | "toolChoice">;
type LLMInstance$1 = Pick<OpenAI$1, "chat" | "apiKey" | "baseURL" | "responses" | "embeddings">;
type OpenAIResponsesChatOptions = Omit<Partial<OpenAI$1.Responses.ResponseCreateParams>, "model" | "input" | "stream" | "tools" | "toolChoice" | "temperature" | "reasoning_effort" | "top_p" | "max_output_tokens" | "include">;
type OpenAIResponsesRole = "user" | "assistant" | "system" | "developer";
interface OpenAILiveConfig {
    apiKey?: string | undefined;
    model?: ChatModel | undefined;
    voiceName?: OpenAIVoiceNames | undefined;
}
type OpenAIVoiceNames = "alloy" | "ash" | "echo" | "fable" | "onyx" | "nova" | "shimmer";

declare class OpenAILive extends LiveLLM {
    private apiKey;
    private model;
    voiceName?: OpenAIVoiceNames | undefined;
    private baseURL;
    constructor(init?: OpenAILiveConfig);
    getEphemeralKey(): Promise<any>;
    private getSDPResponse;
    private establishSDPConnection;
    private initializeRTCPeerConnectionAndDataChannel;
    private setupWebRTC;
    connect(config?: LiveConnectConfig): Promise<OpenAILiveSession>;
    private setupEventListeners;
    private messageEventListener;
    private openEventListener;
    private errorEventListener;
}

declare class OpenAI extends ToolCallLLM<OpenAIAdditionalChatOptions> {
    #private;
    model: ChatModel$1 | (string & {});
    temperature: number;
    reasoningEffort?: "low" | "medium" | "high" | undefined;
    topP: number;
    maxTokens?: number | undefined;
    additionalChatOptions?: OpenAIAdditionalChatOptions | undefined;
    apiKey?: string | undefined;
    baseURL?: string | undefined;
    maxRetries: number;
    timeout?: number;
    additionalSessionOptions?: undefined | Omit<Partial<ClientOptions>, "apiKey" | "maxRetries" | "timeout">;
    private voiceName?;
    private _live;
    lazySession: () => Promise<LLMInstance$1>;
    get session(): Promise<LLMInstance$1>;
    constructor(init?: Omit<Partial<OpenAI>, "session"> & {
        session?: LLMInstance$1 | undefined;
        voiceName?: OpenAIVoiceNames | undefined;
    });
    get supportToolCall(): boolean;
    get live(): OpenAILive;
    get metadata(): LLMMetadata & OpenAIAdditionalMetadata;
    static toOpenAIRole(messageType: MessageType): ChatCompletionRole;
    static toOpenAIMessage(messages: ChatMessage<ToolCallLLMMessageOptions>[]): ChatCompletionMessageParam[];
    chat(params: LLMChatParamsStreaming<OpenAIAdditionalChatOptions, ToolCallLLMMessageOptions>): Promise<AsyncIterable<ChatResponseChunk<ToolCallLLMMessageOptions>>>;
    chat(params: LLMChatParamsNonStreaming<OpenAIAdditionalChatOptions, ToolCallLLMMessageOptions>): Promise<ChatResponse<ToolCallLLMMessageOptions>>;
    protected streamChat(baseRequestParams: OpenAI$1.Chat.ChatCompletionCreateParams): AsyncIterable<ChatResponseChunk<ToolCallLLMMessageOptions>>;
    static toTool(tool: BaseTool): ChatCompletionTool;
}
/**
 * Convenience function to create a new OpenAI instance.
 * @param init - Optional initialization parameters for the OpenAI instance.
 * @returns A new OpenAI instance.
 */
declare const openai: (init?: ConstructorParameters<typeof OpenAI>[0]) => OpenAI;

type OpenAIAgentParams = LLMAgentParams<OpenAI, ToolCallLLMMessageOptions, OpenAIAdditionalChatOptions>;
declare class OpenAIAgentWorker extends LLMAgentWorker {
}
declare class OpenAIAgent extends LLMAgent {
    constructor(params: OpenAIAgentParams);
}

declare const ALL_OPENAI_EMBEDDING_MODELS: {
    "text-embedding-ada-002": {
        dimensions: number;
        maxTokens: number;
        tokenizer: Tokenizers;
    };
    "text-embedding-3-small": {
        dimensions: number;
        dimensionOptions: number[];
        maxTokens: number;
        tokenizer: Tokenizers;
    };
    "text-embedding-3-large": {
        dimensions: number;
        dimensionOptions: number[];
        maxTokens: number;
        tokenizer: Tokenizers;
    };
};
type LLMInstance = Pick<OpenAI$1, "embeddings" | "apiKey" | "baseURL">;
declare class OpenAIEmbedding extends BaseEmbedding {
    #private;
    /** embeddding model. defaults to "text-embedding-ada-002" */
    model: string;
    /** number of dimensions of the resulting vector, for models that support choosing fewer dimensions. undefined will default to model default */
    dimensions?: number | undefined;
    /** api key */
    apiKey?: string | undefined;
    /** base url */
    baseURL?: string | undefined;
    /** maximum number of retries, default 10 */
    maxRetries: number;
    /** timeout in ms, default 60 seconds  */
    timeout?: number | undefined;
    /** other session options for OpenAI */
    additionalSessionOptions?: Omit<Partial<ClientOptions>, "apiKey" | "maxRetries" | "timeout"> | undefined;
    lazySession: () => Promise<LLMInstance>;
    get session(): Promise<LLMInstance>;
    /**
     * OpenAI Embedding
     * @param init - initial parameters
     */
    constructor(init?: Omit<Partial<OpenAIEmbedding>, "session"> & {
        session?: LLMInstance | undefined;
    });
    /**
     * Get embeddings for a batch of texts
     * @param texts
     * @param options
     */
    private getOpenAIEmbedding;
    /**
     * Get embeddings for a batch of texts
     * @param texts
     */
    getTextEmbeddings: (texts: string[]) => Promise<number[][]>;
    /**
     * Get embeddings for a single text
     * @param text
     */
    getTextEmbedding(text: string): Promise<number[]>;
}

declare class OpenAIResponses extends ToolCallLLM<OpenAIResponsesChatOptions> {
    #private;
    model: string;
    temperature: number;
    topP: number;
    maxOutputTokens?: number | undefined;
    additionalChatOptions?: OpenAIResponsesChatOptions | undefined;
    reasoningEffort?: "low" | "medium" | "high" | undefined;
    apiKey?: string | undefined;
    baseURL?: string | undefined;
    maxRetries: number;
    timeout?: number;
    additionalSessionOptions?: undefined | Omit<Partial<ClientOptions>, "apiKey" | "maxRetries" | "timeout">;
    lazySession: () => Promise<LLMInstance$1>;
    trackPreviousResponses: boolean;
    store: boolean;
    user: string;
    callMetadata: StoredValue;
    builtInTools: OpenAI$1.Responses.Tool[] | null;
    strict: boolean;
    include: OpenAI$1.Responses.ResponseIncludable[] | null;
    instructions: string;
    previousResponseId: string | null;
    truncation: "auto" | "disabled" | null;
    constructor(init?: Omit<Partial<OpenAIResponses>, "session"> & {
        session?: LLMInstance$1 | undefined;
    });
    get session(): Promise<LLMInstance$1>;
    get supportToolCall(): boolean;
    get metadata(): LLMMetadata & OpenAIAdditionalMetadata;
    private createInitialMessage;
    private createInitialOptions;
    private isBuiltInToolCall;
    private isReasoning;
    private isMessageBlock;
    private isFunctionCall;
    private isImageGenerationCall;
    private isResponseCreatedEvent;
    private isResponseOutputItemAddedEvent;
    private isToolCallEvent;
    private isResponseTextDeltaEvent;
    private isResponseFunctionCallArgumentsDeltaEvent;
    private isResponseFunctionCallDoneEvent;
    private isResponseOutputTextAnnotationAddedEvent;
    private isResponseFileSearchCallCompletedEvent;
    private isResponseWebSearchCallCompletedEvent;
    private isResponseCompletedEvent;
    private isTextPresent;
    private isRefusalPresent;
    private isAnnotationPresent;
    private handleResponseOutputMessage;
    private extractToolCalls;
    private parseResponseOutput;
    private processStreamEvent;
    private handleResponseCreatedEvent;
    private handleOutputItemAddedEvent;
    private handleTextDeltaEvent;
    private handleFunctionCallArgumentsDeltaEvent;
    private handleFunctionCallArgumentsDoneEvent;
    private handleOutputTextAnnotationAddedEvent;
    private handleBuiltInToolCallCompletedEvent;
    private handleCompletedEvent;
    private createBaseRequestParams;
    chat(params: LLMChatParamsStreaming<OpenAIResponsesChatOptions, ToolCallLLMMessageOptions>): Promise<AsyncIterable<ChatResponseChunk<ToolCallLLMMessageOptions>>>;
    chat(params: LLMChatParamsNonStreaming<OpenAIResponsesChatOptions, ToolCallLLMMessageOptions>): Promise<ChatResponse<ToolCallLLMMessageOptions>>;
    private initalizeStreamState;
    private createResponseChunk;
    protected streamChat(baseRequestParams: OpenAI$1.Responses.ResponseCreateParams): AsyncIterable<ChatResponseChunk<ToolCallLLMMessageOptions>>;
    toOpenAIResponsesRole(messageType: MessageType): OpenAIResponsesRole;
    private isToolResultPresent;
    private isToolCallPresent;
    private isUserMessage;
    private handlePreviousResponseId;
    private convertToOpenAIToolCallResult;
    private convertToOpenAIToolCalls;
    private processMessageContent;
    private convertToOpenAIUserMessage;
    private defaultOpenAIResponseMessage;
    toOpenAIResponseMessage(message: ChatMessage<ToolCallLLMMessageOptions>): OpenAI$1.Responses.ResponseInputItem | OpenAI$1.Responses.ResponseInputItem[];
    toOpenAIResponseMessages(messages: ChatMessage<ToolCallLLMMessageOptions>[]): OpenAI$1.Responses.ResponseInput;
    toResponsesTool(tool: BaseTool): OpenAI$1.Responses.Tool;
}
/**
 * Convenience function to create a new OpenAI instance.
 * @param init - Optional initialization parameters for the OpenAI instance.
 * @returns A new OpenAI instance.
 */
declare const openaiResponses: (init?: ConstructorParameters<typeof OpenAIResponses>[0]) => OpenAIResponses;

export { ALL_OPENAI_EMBEDDING_MODELS, type LLMInstance$1 as LLMInstance, OpenAI, OpenAIAgent, type OpenAIAgentParams, OpenAIAgentWorker, OpenAIEmbedding, OpenAIResponses, openai, openaiResponses };
