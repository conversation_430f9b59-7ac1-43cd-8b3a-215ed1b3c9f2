import { NextResponse } from 'next/server';
import documentManager from '@/lib/document-manager';
import enhancedDocumentService from '@/lib/enhanced-document-service';

export async function GET() {
  try {
    await documentManager.initialize();
    const basicStats = await documentManager.getStats();
    const documents = documentManager.getAllDocuments();
    const enhancedStats = await enhancedDocumentService.getDocumentStats();

    return NextResponse.json({
      basicStats,
      enhancedStats,
      documents: documents.map(doc => ({
        id: doc.id,
        filename: doc.filename,
        originalName: doc.originalName,
        size: doc.size,
        uploadDate: doc.uploadDate,
        status: doc.status,
        errorMessage: doc.errorMessage,
      })),
      integration: {
        deepseek: {
          configured: !!(process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here'),
          model: process.env.DEEPSEEK_CHAT_MODEL || 'deepseek-chat',
          baseURL: process.env.DEEPSEEK_BASE_URL || 'https://ark.cn-beijing.volces.com/api/v3',
        },
        doubao: {
          configured: !!(process.env.DOUBAO_API_KEY && process.env.DOUBAO_API_KEY !== 'your_doubao_api_key_here'),
          model: process.env.EMBEDDING_MODEL || 'doubao-embedding-text-240515',
          baseURL: process.env.DOUBAO_BASE_URL || 'https://ark.cn-beijing.volces.com/api/v3',
        },
      },
    });
  } catch (error) {
    console.error('Failed to get enhanced document stats:', error);
    return NextResponse.json(
      { error: 'Failed to get enhanced document statistics' },
      { status: 500 }
    );
  }
}
