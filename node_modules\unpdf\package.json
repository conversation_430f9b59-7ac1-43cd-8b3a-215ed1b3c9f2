{"name": "unpdf", "type": "module", "version": "0.12.2", "packageManager": "pnpm@9.12.1", "description": "Utilities to work with PDFs in Node.js, browser and workers", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/unjs/unpdf#readme", "repository": {"type": "git", "url": "git+https://github.com/unjs/unpdf.git"}, "bugs": {"url": "https://github.com/unjs/unpdf/issues"}, "keywords": ["parse", "pdfjs-dist", "pdf"], "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./pdfjs": {"types": "./dist/pdfjs.d.mts", "import": "./dist/pdfjs.mjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["*", "dist/*", "dist/*.d.ts"]}}, "files": ["dist"], "scripts": {"build": "unbuild && node scripts/post-build.mjs", "build:pdfjs": "rollup --config pdfjs.rollup.config.ts --configPlugin typescript && cp -R node_modules/pdfjs-dist/types dist", "dev": "unbuild --stub", "lint": "eslint .", "lint:fix": "eslint . --fix", "release": "bumpp", "test": "vitest", "test:types": "tsc --noEmit"}, "optionalDependencies": {"canvas": "^2.11.2"}, "devDependencies": {"@antfu/eslint-config": "^3.7.3", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^6.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.0", "@types/node": "^20.16.11", "bumpp": "^9.6.1", "eslint": "^9.12.0", "fast-glob": "^3.3.2", "pdfjs-dist": "~4.6.82", "rollup": "^4.24.0", "tslib": "^2.7.0", "typescript": "^5.5.4", "unbuild": "^3.0.0-rc.11", "unenv": "^1.10.0", "vitest": "^2.1.2"}, "pnpm": {"patchedDependencies": {"pdfjs-dist@4.6.82": "patches/<EMAIL>"}}}