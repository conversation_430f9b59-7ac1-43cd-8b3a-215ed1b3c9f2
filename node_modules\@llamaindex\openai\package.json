{"name": "@llamaindex/openai", "description": "OpenAI Adapter for LlamaIndex", "version": "0.4.7", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "exports": {".": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/run-llama/LlamaIndexTS.git", "directory": "packages/providers/openai"}, "devDependencies": {"zod": "^3.25.67", "@llamaindex/core": "0.6.13", "@llamaindex/env": "0.1.30"}, "peerDependencies": {"@llamaindex/core": "0.6.13", "@llamaindex/env": "0.1.30"}, "dependencies": {"openai": "^4.102.0"}, "scripts": {"build": "bunchee", "dev": "bunchee --watch", "test": "vitest"}}