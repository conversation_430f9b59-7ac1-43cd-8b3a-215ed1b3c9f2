"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nth-check";
exports.ids = ["vendor-chunks/nth-check"];
exports.modules = {

/***/ "(rsc)/./node_modules/nth-check/lib/esm/compile.js":
/*!***************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/compile.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compile: () => (/* binding */ compile),\n/* harmony export */   generate: () => (/* binding */ generate)\n/* harmony export */ });\n/* harmony import */ var boolbase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! boolbase */ \"(rsc)/./node_modules/boolbase/index.js\");\n\n/**\n * Returns a function that checks if an elements index matches the given rule\n * highly optimized to return the fastest solution.\n *\n * @param parsed A tuple [a, b], as returned by `parse`.\n * @returns A highly optimized function that returns whether an index matches the nth-check.\n * @example\n *\n * ```js\n * const check = nthCheck.compile([2, 3]);\n *\n * check(0); // `false`\n * check(1); // `false`\n * check(2); // `true`\n * check(3); // `false`\n * check(4); // `true`\n * check(5); // `false`\n * check(6); // `true`\n * ```\n */\nfunction compile(parsed) {\n    const a = parsed[0];\n    // Subtract 1 from `b`, to convert from one- to zero-indexed.\n    const b = parsed[1] - 1;\n    /*\n     * When `b <= 0`, `a * n` won't be lead to any matches for `a < 0`.\n     * Besides, the specification states that no elements are\n     * matched when `a` and `b` are 0.\n     *\n     * `b < 0` here as we subtracted 1 from `b` above.\n     */\n    if (b < 0 && a <= 0)\n        return boolbase__WEBPACK_IMPORTED_MODULE_0__.falseFunc;\n    // When `a` is in the range -1..1, it matches any element (so only `b` is checked).\n    if (a === -1)\n        return (index) => index <= b;\n    if (a === 0)\n        return (index) => index === b;\n    // When `b <= 0` and `a === 1`, they match any element.\n    if (a === 1)\n        return b < 0 ? boolbase__WEBPACK_IMPORTED_MODULE_0__.trueFunc : (index) => index >= b;\n    /*\n     * Otherwise, modulo can be used to check if there is a match.\n     *\n     * Modulo doesn't care about the sign, so let's use `a`s absolute value.\n     */\n    const absA = Math.abs(a);\n    // Get `b mod a`, + a if this is negative.\n    const bMod = ((b % absA) + absA) % absA;\n    return a > 1\n        ? (index) => index >= b && index % absA === bMod\n        : (index) => index <= b && index % absA === bMod;\n}\n/**\n * Returns a function that produces a monotonously increasing sequence of indices.\n *\n * If the sequence has an end, the returned function will return `null` after\n * the last index in the sequence.\n *\n * @param parsed A tuple [a, b], as returned by `parse`.\n * @returns A function that produces a sequence of indices.\n * @example <caption>Always increasing (2n+3)</caption>\n *\n * ```js\n * const gen = nthCheck.generate([2, 3])\n *\n * gen() // `1`\n * gen() // `3`\n * gen() // `5`\n * gen() // `8`\n * gen() // `11`\n * ```\n *\n * @example <caption>With end value (-2n+10)</caption>\n *\n * ```js\n *\n * const gen = nthCheck.generate([-2, 5]);\n *\n * gen() // 0\n * gen() // 2\n * gen() // 4\n * gen() // null\n * ```\n */\nfunction generate(parsed) {\n    const a = parsed[0];\n    // Subtract 1 from `b`, to convert from one- to zero-indexed.\n    let b = parsed[1] - 1;\n    let n = 0;\n    // Make sure to always return an increasing sequence\n    if (a < 0) {\n        const aPos = -a;\n        // Get `b mod a`\n        const minValue = ((b % aPos) + aPos) % aPos;\n        return () => {\n            const val = minValue + aPos * n++;\n            return val > b ? null : val;\n        };\n    }\n    if (a === 0)\n        return b < 0\n            ? // There are no result — always return `null`\n                () => null\n            : // Return `b` exactly once\n                () => (n++ === 0 ? b : null);\n    if (b < 0) {\n        b += a * Math.ceil(-b / a);\n    }\n    return () => a * n++ + b;\n}\n//# sourceMappingURL=compile.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/nth-check/lib/esm/compile.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/nth-check/lib/esm/index.js":
/*!*************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compile: () => (/* reexport safe */ _compile_js__WEBPACK_IMPORTED_MODULE_1__.compile),\n/* harmony export */   \"default\": () => (/* binding */ nthCheck),\n/* harmony export */   generate: () => (/* reexport safe */ _compile_js__WEBPACK_IMPORTED_MODULE_1__.generate),\n/* harmony export */   parse: () => (/* reexport safe */ _parse_js__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   sequence: () => (/* binding */ sequence)\n/* harmony export */ });\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse.js */ \"(rsc)/./node_modules/nth-check/lib/esm/parse.js\");\n/* harmony import */ var _compile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./compile.js */ \"(rsc)/./node_modules/nth-check/lib/esm/compile.js\");\n\n\n\n/**\n * Parses and compiles a formula to a highly optimized function.\n * Combination of {@link parse} and {@link compile}.\n *\n * If the formula doesn't match any elements,\n * it returns [`boolbase`](https://github.com/fb55/boolbase)'s `falseFunc`.\n * Otherwise, a function accepting an _index_ is returned, which returns\n * whether or not the passed _index_ matches the formula.\n *\n * Note: The nth-rule starts counting at `1`, the returned function at `0`.\n *\n * @param formula The formula to compile.\n * @example\n * const check = nthCheck(\"2n+3\");\n *\n * check(0); // `false`\n * check(1); // `false`\n * check(2); // `true`\n * check(3); // `false`\n * check(4); // `true`\n * check(5); // `false`\n * check(6); // `true`\n */\nfunction nthCheck(formula) {\n    return (0,_compile_js__WEBPACK_IMPORTED_MODULE_1__.compile)((0,_parse_js__WEBPACK_IMPORTED_MODULE_0__.parse)(formula));\n}\n/**\n * Parses and compiles a formula to a generator that produces a sequence of indices.\n * Combination of {@link parse} and {@link generate}.\n *\n * @param formula The formula to compile.\n * @returns A function that produces a sequence of indices.\n * @example <caption>Always increasing</caption>\n *\n * ```js\n * const gen = nthCheck.sequence('2n+3')\n *\n * gen() // `1`\n * gen() // `3`\n * gen() // `5`\n * gen() // `8`\n * gen() // `11`\n * ```\n *\n * @example <caption>With end value</caption>\n *\n * ```js\n *\n * const gen = nthCheck.sequence('-2n+5');\n *\n * gen() // 0\n * gen() // 2\n * gen() // 4\n * gen() // null\n * ```\n */\nfunction sequence(formula) {\n    return (0,_compile_js__WEBPACK_IMPORTED_MODULE_1__.generate)((0,_parse_js__WEBPACK_IMPORTED_MODULE_0__.parse)(formula));\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/nth-check/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/nth-check/lib/esm/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n// Following http://www.w3.org/TR/css3-selectors/#nth-child-pseudo\n// Whitespace as per https://www.w3.org/TR/selectors-3/#lex is \" \\t\\r\\n\\f\"\nconst whitespace = new Set([9, 10, 12, 13, 32]);\nconst ZERO = \"0\".charCodeAt(0);\nconst NINE = \"9\".charCodeAt(0);\n/**\n * Parses an expression.\n *\n * @throws An `Error` if parsing fails.\n * @returns An array containing the integer step size and the integer offset of the nth rule.\n * @example nthCheck.parse(\"2n+3\"); // returns [2, 3]\n */\nfunction parse(formula) {\n    formula = formula.trim().toLowerCase();\n    if (formula === \"even\") {\n        return [2, 0];\n    }\n    else if (formula === \"odd\") {\n        return [2, 1];\n    }\n    // Parse [ ['-'|'+']? INTEGER? {N} [ S* ['-'|'+'] S* INTEGER ]?\n    let idx = 0;\n    let a = 0;\n    let sign = readSign();\n    let number = readNumber();\n    if (idx < formula.length && formula.charAt(idx) === \"n\") {\n        idx++;\n        a = sign * (number !== null && number !== void 0 ? number : 1);\n        skipWhitespace();\n        if (idx < formula.length) {\n            sign = readSign();\n            skipWhitespace();\n            number = readNumber();\n        }\n        else {\n            sign = number = 0;\n        }\n    }\n    // Throw if there is anything else\n    if (number === null || idx < formula.length) {\n        throw new Error(`n-th rule couldn't be parsed ('${formula}')`);\n    }\n    return [a, sign * number];\n    function readSign() {\n        if (formula.charAt(idx) === \"-\") {\n            idx++;\n            return -1;\n        }\n        if (formula.charAt(idx) === \"+\") {\n            idx++;\n        }\n        return 1;\n    }\n    function readNumber() {\n        const start = idx;\n        let value = 0;\n        while (idx < formula.length &&\n            formula.charCodeAt(idx) >= ZERO &&\n            formula.charCodeAt(idx) <= NINE) {\n            value = value * 10 + (formula.charCodeAt(idx) - ZERO);\n            idx++;\n        }\n        // Return `null` if we didn't read anything.\n        return idx === start ? null : value;\n    }\n    function skipWhitespace() {\n        while (idx < formula.length &&\n            whitespace.has(formula.charCodeAt(idx))) {\n            idx++;\n        }\n    }\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbnRoLWNoZWNrL2xpYi9lc20vcGFyc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DO0FBQ3BDO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxHQUFHO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBELFFBQVE7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXG5vZGVfbW9kdWxlc1xcbnRoLWNoZWNrXFxsaWJcXGVzbVxccGFyc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRm9sbG93aW5nIGh0dHA6Ly93d3cudzMub3JnL1RSL2NzczMtc2VsZWN0b3JzLyNudGgtY2hpbGQtcHNldWRvXG4vLyBXaGl0ZXNwYWNlIGFzIHBlciBodHRwczovL3d3dy53My5vcmcvVFIvc2VsZWN0b3JzLTMvI2xleCBpcyBcIiBcXHRcXHJcXG5cXGZcIlxuY29uc3Qgd2hpdGVzcGFjZSA9IG5ldyBTZXQoWzksIDEwLCAxMiwgMTMsIDMyXSk7XG5jb25zdCBaRVJPID0gXCIwXCIuY2hhckNvZGVBdCgwKTtcbmNvbnN0IE5JTkUgPSBcIjlcIi5jaGFyQ29kZUF0KDApO1xuLyoqXG4gKiBQYXJzZXMgYW4gZXhwcmVzc2lvbi5cbiAqXG4gKiBAdGhyb3dzIEFuIGBFcnJvcmAgaWYgcGFyc2luZyBmYWlscy5cbiAqIEByZXR1cm5zIEFuIGFycmF5IGNvbnRhaW5pbmcgdGhlIGludGVnZXIgc3RlcCBzaXplIGFuZCB0aGUgaW50ZWdlciBvZmZzZXQgb2YgdGhlIG50aCBydWxlLlxuICogQGV4YW1wbGUgbnRoQ2hlY2sucGFyc2UoXCIybiszXCIpOyAvLyByZXR1cm5zIFsyLCAzXVxuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyc2UoZm9ybXVsYSkge1xuICAgIGZvcm11bGEgPSBmb3JtdWxhLnRyaW0oKS50b0xvd2VyQ2FzZSgpO1xuICAgIGlmIChmb3JtdWxhID09PSBcImV2ZW5cIikge1xuICAgICAgICByZXR1cm4gWzIsIDBdO1xuICAgIH1cbiAgICBlbHNlIGlmIChmb3JtdWxhID09PSBcIm9kZFwiKSB7XG4gICAgICAgIHJldHVybiBbMiwgMV07XG4gICAgfVxuICAgIC8vIFBhcnNlIFsgWyctJ3wnKyddPyBJTlRFR0VSPyB7Tn0gWyBTKiBbJy0nfCcrJ10gUyogSU5URUdFUiBdP1xuICAgIGxldCBpZHggPSAwO1xuICAgIGxldCBhID0gMDtcbiAgICBsZXQgc2lnbiA9IHJlYWRTaWduKCk7XG4gICAgbGV0IG51bWJlciA9IHJlYWROdW1iZXIoKTtcbiAgICBpZiAoaWR4IDwgZm9ybXVsYS5sZW5ndGggJiYgZm9ybXVsYS5jaGFyQXQoaWR4KSA9PT0gXCJuXCIpIHtcbiAgICAgICAgaWR4Kys7XG4gICAgICAgIGEgPSBzaWduICogKG51bWJlciAhPT0gbnVsbCAmJiBudW1iZXIgIT09IHZvaWQgMCA/IG51bWJlciA6IDEpO1xuICAgICAgICBza2lwV2hpdGVzcGFjZSgpO1xuICAgICAgICBpZiAoaWR4IDwgZm9ybXVsYS5sZW5ndGgpIHtcbiAgICAgICAgICAgIHNpZ24gPSByZWFkU2lnbigpO1xuICAgICAgICAgICAgc2tpcFdoaXRlc3BhY2UoKTtcbiAgICAgICAgICAgIG51bWJlciA9IHJlYWROdW1iZXIoKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHNpZ24gPSBudW1iZXIgPSAwO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8vIFRocm93IGlmIHRoZXJlIGlzIGFueXRoaW5nIGVsc2VcbiAgICBpZiAobnVtYmVyID09PSBudWxsIHx8IGlkeCA8IGZvcm11bGEubGVuZ3RoKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgbi10aCBydWxlIGNvdWxkbid0IGJlIHBhcnNlZCAoJyR7Zm9ybXVsYX0nKWApO1xuICAgIH1cbiAgICByZXR1cm4gW2EsIHNpZ24gKiBudW1iZXJdO1xuICAgIGZ1bmN0aW9uIHJlYWRTaWduKCkge1xuICAgICAgICBpZiAoZm9ybXVsYS5jaGFyQXQoaWR4KSA9PT0gXCItXCIpIHtcbiAgICAgICAgICAgIGlkeCsrO1xuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICAgIGlmIChmb3JtdWxhLmNoYXJBdChpZHgpID09PSBcIitcIikge1xuICAgICAgICAgICAgaWR4Kys7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIDE7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHJlYWROdW1iZXIoKSB7XG4gICAgICAgIGNvbnN0IHN0YXJ0ID0gaWR4O1xuICAgICAgICBsZXQgdmFsdWUgPSAwO1xuICAgICAgICB3aGlsZSAoaWR4IDwgZm9ybXVsYS5sZW5ndGggJiZcbiAgICAgICAgICAgIGZvcm11bGEuY2hhckNvZGVBdChpZHgpID49IFpFUk8gJiZcbiAgICAgICAgICAgIGZvcm11bGEuY2hhckNvZGVBdChpZHgpIDw9IE5JTkUpIHtcbiAgICAgICAgICAgIHZhbHVlID0gdmFsdWUgKiAxMCArIChmb3JtdWxhLmNoYXJDb2RlQXQoaWR4KSAtIFpFUk8pO1xuICAgICAgICAgICAgaWR4Kys7XG4gICAgICAgIH1cbiAgICAgICAgLy8gUmV0dXJuIGBudWxsYCBpZiB3ZSBkaWRuJ3QgcmVhZCBhbnl0aGluZy5cbiAgICAgICAgcmV0dXJuIGlkeCA9PT0gc3RhcnQgPyBudWxsIDogdmFsdWU7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHNraXBXaGl0ZXNwYWNlKCkge1xuICAgICAgICB3aGlsZSAoaWR4IDwgZm9ybXVsYS5sZW5ndGggJiZcbiAgICAgICAgICAgIHdoaXRlc3BhY2UuaGFzKGZvcm11bGEuY2hhckNvZGVBdChpZHgpKSkge1xuICAgICAgICAgICAgaWR4Kys7XG4gICAgICAgIH1cbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYXJzZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/nth-check/lib/esm/parse.js\n");

/***/ })

};
;