// Test script to verify Doubao embedding API integration
// Run with: node test-doubao-integration.js

const fetch = require('node-fetch');

async function testDoubaoEmbeddingAPI() {
  const apiKey = process.env.DOUBAO_API_KEY;
  const baseURL = process.env.DOUBAO_BASE_URL || 'https://ark.cn-beijing.volces.com/api/v3';
  const model = process.env.EMBEDDING_MODEL || 'doubao-embedding-text-240515';
  
  if (!apiKey || apiKey === 'your_doubao_api_key_here') {
    console.log('❌ Doubao API key not configured');
    console.log('Please set DOUBAO_API_KEY in your environment variables');
    return;
  }

  console.log('🧪 Testing Doubao Embedding API integration...');
  console.log(`📍 Base URL: ${baseURL}`);
  console.log(`🤖 Model: ${model}`);
  
  try {
    // Test 1: Single text embedding
    console.log('\n📝 Testing single text embedding...');
    const singleResponse = await fetch(`${baseURL}/embeddings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: model,
        input: 'This is a test document about artificial intelligence and machine learning.',
        encoding_format: 'float',
      }),
    });

    if (singleResponse.ok) {
      const singleData = await singleResponse.json();
      console.log('✅ Single embedding successful');
      console.log(`📊 Embedding dimension: ${singleData.data[0].embedding.length}`);
      console.log(`🔢 Token usage: ${singleData.usage.total_tokens}`);
    } else {
      const errorData = await singleResponse.json().catch(() => ({}));
      console.log('❌ Single embedding failed:', singleResponse.status, errorData);
    }

    // Test 2: Multiple text embeddings
    console.log('\n📚 Testing multiple text embeddings...');
    const multipleTexts = [
      'Document about machine learning algorithms',
      'Text discussing natural language processing',
      'Content related to computer vision and image recognition'
    ];

    const multipleResponse = await fetch(`${baseURL}/embeddings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: model,
        input: multipleTexts,
        encoding_format: 'float',
      }),
    });

    if (multipleResponse.ok) {
      const multipleData = await multipleResponse.json();
      console.log('✅ Multiple embeddings successful');
      console.log(`📊 Number of embeddings: ${multipleData.data.length}`);
      console.log(`🔢 Total token usage: ${multipleData.usage.total_tokens}`);
      
      // Test similarity calculation
      if (multipleData.data.length >= 2) {
        const embedding1 = multipleData.data[0].embedding;
        const embedding2 = multipleData.data[1].embedding;
        const similarity = calculateCosineSimilarity(embedding1, embedding2);
        console.log(`🔗 Similarity between first two texts: ${similarity.toFixed(4)}`);
      }
    } else {
      const errorData = await multipleResponse.json().catch(() => ({}));
      console.log('❌ Multiple embeddings failed:', multipleResponse.status, errorData);
    }

    // Test 3: Document-style text embedding
    console.log('\n📄 Testing document-style text embedding...');
    const documentText = `
    ChatDoc Application Documentation
    
    Overview:
    ChatDoc is a modern web application that allows users to upload text and markdown documents 
    and then ask questions about their content using AI-powered natural language processing.
    
    Key Features:
    1. Document Upload: Users can upload .txt and .md files through a drag-and-drop interface
    2. AI-Powered Chat: Ask questions about uploaded documents using natural language
    3. Source Attribution: All responses include references to the source documents
    4. Real-time Processing: Documents are automatically indexed and made searchable
    
    Technical Architecture:
    - Frontend: Next.js 14 with React 18 and TypeScript
    - Backend: Next.js API routes with DeepSeek and Doubao integration
    - AI/ML: DeepSeek for reasoning and Doubao for embeddings
    `;

    const documentResponse = await fetch(`${baseURL}/embeddings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: model,
        input: documentText.trim(),
        encoding_format: 'float',
      }),
    });

    if (documentResponse.ok) {
      const documentData = await documentResponse.json();
      console.log('✅ Document embedding successful');
      console.log(`📊 Document embedding dimension: ${documentData.data[0].embedding.length}`);
      console.log(`🔢 Document token usage: ${documentData.usage.total_tokens}`);
    } else {
      const errorData = await documentResponse.json().catch(() => ({}));
      console.log('❌ Document embedding failed:', documentResponse.status, errorData);
    }

    console.log('\n🎉 Doubao Embedding API integration test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

function calculateCosineSimilarity(embedding1, embedding2) {
  if (embedding1.length !== embedding2.length) {
    throw new Error('Embeddings must have the same dimension');
  }

  let dotProduct = 0;
  let norm1 = 0;
  let norm2 = 0;

  for (let i = 0; i < embedding1.length; i++) {
    dotProduct += embedding1[i] * embedding2[i];
    norm1 += embedding1[i] * embedding1[i];
    norm2 += embedding2[i] * embedding2[i];
  }

  const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
  return magnitude === 0 ? 0 : dotProduct / magnitude;
}

// Run the test
testDoubaoEmbeddingAPI();
