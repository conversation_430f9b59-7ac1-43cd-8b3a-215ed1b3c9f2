Object.defineProperty(exports, '__esModule', { value: true });

var schema = require('@llamaindex/core/schema');
var mammoth = require('mammoth');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var mammoth__default = /*#__PURE__*/_interopDefault(mammoth);

class DocxReader extends schema.FileReader {
    /** DocxParser */ async loadDataAsContent(fileContent) {
        // Note: await mammoth.extractRawText({ arrayBuffer: fileContent });  is not working
        // So we need to convert to Buffer first
        const buffer = Buffer.from(fileContent);
        const { value } = await mammoth__default.default.extractRawText({
            buffer
        });
        return [
            new schema.Document({
                text: value
            })
        ];
    }
}

exports.DocxReader = DocxReader;
