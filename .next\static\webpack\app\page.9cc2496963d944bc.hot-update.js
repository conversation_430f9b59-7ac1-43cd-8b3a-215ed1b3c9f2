"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/documents/document-upload.tsx":
/*!******************************************************!*\
  !*** ./src/components/documents/document-upload.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentUpload: () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ DocumentUpload auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DocumentUpload() {\n    _s();\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fetch document status on component mount and periodically\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentUpload.useEffect\": ()=>{\n            fetchDocumentStatus();\n            const interval = setInterval(fetchDocumentStatus, 5000); // Check every 5 seconds\n            return ({\n                \"DocumentUpload.useEffect\": ()=>clearInterval(interval)\n            })[\"DocumentUpload.useEffect\"];\n        }\n    }[\"DocumentUpload.useEffect\"], []);\n    const fetchDocumentStatus = async ()=>{\n        try {\n            const response = await fetch('/api/documents/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUploadedFiles(data.documents.map((doc)=>({\n                        id: doc.id,\n                        name: doc.originalName || doc.filename,\n                        size: doc.size,\n                        type: '',\n                        status: doc.status,\n                        errorMessage: doc.errorMessage\n                    })));\n            }\n        } catch (error) {\n            console.error('Failed to fetch document status:', error);\n        }\n    };\n    const handleUpload = async (files)=>{\n        try {\n            for (const file of files){\n                // Create FormData for upload\n                const formData = new FormData();\n                formData.append('file', file);\n                // Upload file to API\n                const response = await fetch('/api/documents/upload', {\n                    method: 'POST',\n                    body: formData\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to upload \".concat(file.name));\n                }\n                const result = await response.json();\n                console.log(\"Successfully uploaded: \".concat(file.name));\n            }\n            // Refresh document status after upload\n            await fetchDocumentStatus();\n        } catch (error) {\n            console.error('Upload error:', error);\n            throw error; // Let the enhanced file upload component handle the error\n        }\n    };\n    const handleRemoveFile = async (fileId)=>{\n        try {\n            const response = await fetch(\"/api/documents/\".concat(fileId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                setUploadedFiles((prev)=>prev.filter((file)=>file.id !== fileId));\n            } else {\n                alert('Failed to remove file');\n            }\n        } catch (error) {\n            console.error('Remove file error:', error);\n            alert('Failed to remove file');\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'uploading':\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 16\n                }, this);\n            case 'indexed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'uploading':\n                return 'Uploading...';\n            case 'processing':\n                return 'Processing...';\n            case 'indexed':\n                return 'Ready';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Ready';\n        }\n    };\n    const handleFileSelect = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        setIsUploading(true);\n        try {\n            await handleUpload(Array.from(files));\n        } catch (error) {\n            console.error('Upload failed:', error);\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            setIsUploading(true);\n            try {\n                await handleUpload(Array.from(files));\n            } catch (error) {\n                console.error('Upload failed:', error);\n            } finally{\n                setIsUploading(false);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-2 border-dashed transition-all duration-300 cursor-pointer group \".concat(isDragOver ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg' : 'border-border hover:border-primary/50 hover:bg-muted/30 hover:shadow-md'),\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-300 \".concat(isDragOver ? 'bg-primary text-primary-foreground scale-110' : 'bg-gradient-to-br from-muted to-muted/50 text-muted-foreground group-hover:scale-105 group-hover:from-primary/20 group-hover:to-primary/10'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 transition-transform duration-300 \".concat(isDragOver ? 'scale-110' : 'group-hover:scale-110')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2 transition-colors duration-300 \".concat(isDragOver ? 'text-primary' : 'text-foreground'),\n                                children: isDragOver ? '✨ Drop files here' : 'Upload your documents'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-4\",\n                                children: \"Drag and drop files here, or click to browse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2 text-sm text-muted-foreground mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-muted/50 rounded-full\",\n                                        children: \"Max file size: 10 MB\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-muted/50 rounded-full\",\n                                        children: \"Max files: 10\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                disabled: isUploading,\n                                className: \"transition-all duration-300 hover:scale-105 hover:shadow-md\",\n                                children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Uploading...\"\n                                    ]\n                                }, void 0, true) : 'Choose Files'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground mt-2\",\n                                children: \"Supported formats: .txt, .md\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        className: \"hidden\",\n                        accept: \".txt,.md\",\n                        onChange: handleFileSelect\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            uploadedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-5 rounded bg-primary/10 flex items-center justify-center\",\n                                children: \"\\uD83D\\uDCC1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            \"Uploaded Documents (\",\n                            uploadedFiles.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    uploadedFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"p-4 transition-all duration-200 hover:shadow-md hover:scale-[1.01] group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-lg bg-muted/50 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium group-hover:text-primary transition-colors\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-0.5 bg-muted/50 rounded-full\",\n                                                                children: formatFileSize(file.size)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    getStatusIcon(file.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium \".concat(file.status === 'indexed' ? 'text-green-600' : file.status === 'error' ? 'text-red-600' : 'text-blue-600'),\n                                                                        children: getStatusText(file.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    file.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 p-2 bg-red-50 border border-red-200 rounded-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-600\",\n                                                            children: file.errorMessage\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleRemoveFile(file.id),\n                                        disabled: file.status === 'processing',\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-50 hover:text-red-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this)\n                        }, file.id, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this),\n            uploadedFiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No files uploaded yet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"z9LA/xowQ39FNkw5vGmX3b76ZvI=\");\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/documents/document-upload.tsx\n"));

/***/ })

});