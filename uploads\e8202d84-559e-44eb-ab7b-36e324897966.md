# What is Capacity Management

Capacity Management is a mandatory process of making sure that we always have the right capacity in the right place at the right time for the right cost.

* Right Capacity means we need to ensure that we have sufficient infra to meet business requirements. This is typically measured by ensuring that service can meet defined performance requirement (normally documented as non-functional requirements)  
* Right Place generally refers to the place with the architecture rather than physical location, so in this context, means ensuring that all the layers (app, middleware, db etc.) have the right infra. Care must be taken to ensure that it is properly allocated such ath one layer doesn’t flood another layer with traffic, moving the bottleneck around the different components in the service.  
* Right Time means that we need to ensure that we have sufficient capacity in place before issues occur, or in  time to meet planned business requirements (such as new products, or application migration), We don’t want to have the infra too early or we incur additional costs  
* Right Cost means that we need to ensure that we don’t significantly over-provision just to prevent issues. Excess capacity costs unjustifiable costs in software licencing, power consumption and support

# What are the Benefit of an Effective Capacity Management Process

By having an effective Capacity Management process:

* Improved Service Availability \-the incidents relating to a lack of capacity should be reduced, ideally eliminated  
* Investment Forecasting \- allows investment to be forecast in alignment with planning and budget cycle. It avoid both unplanned investment also ensuring that we don’t have excessive unused capacity  
* Optimizing Infra performance \- helps ensure that infra performance is optimized by monitoring consumption and identifying bottlenecks.   
* Scaling \- potential issues from rapid scaling can be identified and highlighted, allowing remedial action to be taken before service impacting incidents occur.  
* Continual Performance Improvement \- can help drive continual improvement within services by highlighting the next constraints.

# Capacity Management Tool \- Athene

Syncsort Capacity Management (SCM) is the Group Standard Capacity Planning and Performance Management tool set

* SCM is able to take data from variety data source  
* SCN allows the environment to be managed in a far more agile manner than conventional Capacity Management tools. This can be done at a service by server level, using standard reporting dashboard techniques  
* SCM offers predictive analytics that allows forward looking projection to be created. This allows a view on the expected performance of the service in the future  
* Using the power of the predictive analytics in the “Service View” module, when a potential breach is identified the defined support team for that service will receive an alert containing details of what has been identified.

# How are we doing today in MBG

A service view created on Athene is required for service to support Capacity Management process and it identifies potential breach dates to raise alert when a breach is projected. This allows automated alerts to be raised, ensuring that any potential issues are identified with sufficient notice to allow remedial action to be taken before production incident or service failure occurs.  
The operational effectiveness of this control is measured by the use of 4 Key Control Indicators.

## Approach

We will establish a Capacity Management Practice Framework (CMPF) to meet expectation consistency and at scale. This will ensure the systematic identification and management of services that are capacity-sensitive 

This practice will leverage the existing capacity management control not in isolation, but in coordination with other critical disciplines such as scenario testing, architecture, and the software development lifecycle.

## Objectives

- Business-aligned capacity management: bring BSOs inputs for growth plans  
- Expanding to include Capacity Management beyond infra including IT Assess level and business business service  
- Enable a consistent practice across Teams  
- Proactive management of capacity-related risks through adequate KPIs and KCIs  
- Improved resource allocation efficiency

## Delivery Approach

1. Capacity Management Practice  
2. BSOs capacity assessment results for stress  
3. Teams playbooks & adoption plans  
4. Practice interlocks and tooling review  
5. Control Redesign (Procedures, Operating instructions, KCIs)  
6. Teams prioritised adoption  
7. Control uplift  
8. Teams scaled adoption / Control Effectiveness

## Scope

- UK IBS (Important Business Services)  
- Critical Services by Line of Business

## Capacity Management Practices Framework

## High Level Timeline

* May  
  * Define Capacity Management Practice Framework proposed structure  
  * Capacity Management working group Kick-off  
  * Draft Core Capabilities  
* Jun  
  * Reuse and develop the Capacity Management Practice Practise Framework content  
  * Draft enablement Capabilities  
  * Internal review and coverage content into CMPF draft  
  * Adoption planning and playbook refresh started  
* Jul  
  * Circulate to key stakeholders for comments and feedback  
  * Revise Practice Framework, prompting from draft to final  
  * CMPF Adoption & Playbook pilot planning  
* Aug  
  * Playbook pilot execution  
* Sep  
  * Delver playbooks to implement the CMPF

## Notes

* Practice outline will be articulated in to CMPF (guidelines, standards, mandatory requirement, RACI)  
* Framework will be contextualised by department and specific guideline will be reflected in the playbooks (“how-to” guide, what to do and when)  
* Framework will be actioned through supporting practices, processes and tooling such as SDLC, NFRs and Incident management process  
* Departments will adopt the CMPF using their playbooks  
* Leveraging Control Operation, tooling and reporting uplift, supported by enhanced governance, tooling, clearer roles and responsibilities and refreshed KCIs