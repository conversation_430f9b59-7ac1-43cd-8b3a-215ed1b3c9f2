"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Upload, File, X, CheckCircle, Clock, AlertCircle } from "lucide-react";

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status?: 'uploading' | 'processing' | 'indexed' | 'error';
  errorMessage?: string;
}

export function DocumentUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch document status on component mount and periodically
  useEffect(() => {
    fetchDocumentStatus();
    const interval = setInterval(fetchDocumentStatus, 5000); // Check every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchDocumentStatus = async () => {
    try {
      const response = await fetch('/api/documents/stats');
      if (response.ok) {
        const data = await response.json();
        setUploadedFiles(data.documents.map((doc: any) => ({
          id: doc.id,
          name: doc.originalName || doc.filename,
          size: doc.size,
          type: '', // We don't store type in the backend
          status: doc.status,
          errorMessage: doc.errorMessage,
        })));
      }
    } catch (error) {
      console.error('Failed to fetch document status:', error);
    }
  };

  const validateAndUploadFiles = async (files: FileList | File[]) => {
    setIsUploading(true);

    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(file => {
      if (!file.name.endsWith('.txt') && !file.name.endsWith('.md')) {
        alert(`File ${file.name} is not supported. Please upload .txt or .md files only.`);
        return false;
      }
      return true;
    });

    if (validFiles.length === 0) {
      setIsUploading(false);
      return;
    }

    try {
      for (const file of validFiles) {
        // Create FormData for upload
        const formData = new FormData();
        formData.append('file', file);

        // Upload file to API
        const response = await fetch('/api/documents/upload', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const result = await response.json();
          const newFile: UploadedFile = {
            id: result.id,
            name: file.name,
            size: file.size,
            type: file.type,
          };
          setUploadedFiles(prev => [...prev, newFile]);
        } else {
          alert(`Failed to upload ${file.name}`);
        }
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    await validateAndUploadFiles(files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      await validateAndUploadFiles(files);
    }
  };

  const handleRemoveFile = async (fileId: string) => {
    try {
      const response = await fetch(`/api/documents/${fileId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
      } else {
        alert('Failed to remove file');
      }
    } catch (error) {
      console.error('Remove file error:', error);
      alert('Failed to remove file');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'indexed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'indexed':
        return 'Ready';
      case 'processing':
        return 'Processing...';
      case 'error':
        return 'Error';
      default:
        return 'Uploading...';
    }
  };

  return (
    <div className="space-y-4">
      <div 
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragOver 
            ? 'border-primary bg-primary/5' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Upload className={`mx-auto h-12 w-12 mb-4 ${
          isDragOver ? 'text-primary' : 'text-muted-foreground'
        }`} />
        <p className="text-sm text-muted-foreground mb-4">
          {isDragOver 
            ? 'Drop files here to upload' 
            : 'Drag and drop files here, or click to select'
          }
        </p>
        <Input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".txt,.md"
          onChange={handleFileSelect}
          className="hidden"
        />
        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          variant="outline"
        >
          {isUploading ? 'Uploading...' : 'Select Files'}
        </Button>
        <p className="text-xs text-muted-foreground mt-2">
          Supported formats: .txt, .md (Max 10MB per file)
        </p>
      </div>

      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Uploaded Documents</h3>
          {uploadedFiles.map((file) => (
            <div
              key={file.id}
              className="flex items-center justify-between p-3 border rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <File className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1">
                  <p className="text-sm font-medium">{file.name}</p>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <span>{formatFileSize(file.size)}</span>
                    <span>•</span>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(file.status)}
                      <span>{getStatusText(file.status)}</span>
                    </div>
                  </div>
                  {file.errorMessage && (
                    <p className="text-xs text-red-500 mt-1">{file.errorMessage}</p>
                  )}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveFile(file.id)}
                disabled={file.status === 'processing'}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
