Object.defineProperty(exports, '__esModule', { value: true });

var jsonExt = require('@discoveryjs/json-ext');
var schema = require('@llamaindex/core/schema');
var env = require('@llamaindex/env');

class JSONReaderError extends Error {
}
class JSONParseError extends JSONReaderError {
}
class JSONStringifyError extends JSONReaderError {
}
class JSONParser {
    static async *parse(content, isJsonLines, isStream, logger) {
        logger?.log(`Parsing JSON ${isJsonLines ? "as JSON Lines" : ""}`);
        try {
            if (isStream) {
                const stream = new ReadableStream({
                    start (controller) {
                        controller.enqueue(content);
                        controller.close();
                    }
                });
                yield* this.parseStream(stream, isJsonLines);
            } else {
                yield* this.parseString(content, isJsonLines);
            }
        } catch (e) {
            throw new JSONParseError(`Failed to parse JSON ${isJsonLines ? "as JSON Lines" : ""} ${isStream ? "while streaming" : ""}:  ${e instanceof Error ? e.message : "Unknown error occurred"}`, {
                cause: e
            });
        }
    }
    static async *parseStream(stream, isJsonLines) {
        if (isJsonLines) {
            yield* this.parseJsonLinesStream(stream);
        } else {
            yield await jsonExt.parseChunked(stream);
        }
    }
    static async *parseString(jsonStr, isJsonLines) {
        if (isJsonLines) {
            yield* this.parseJsonLines(jsonStr);
        } else {
            yield JSON.parse(jsonStr);
        }
    }
    static async *parseJsonLines(jsonStr) {
        for (const line of jsonStr.split("\n").filter((l)=>l.trim())){
            yield JSON.parse(line.trim());
        }
    }
    static async *parseJsonLinesStream(stream) {
        const reader = stream.getReader();
        const decoder = new TextDecoder("utf-8");
        let buffer = "";
        while(true){
            const { done, value } = await reader.read();
            if (done) break;
            buffer += decoder.decode(value, {
                stream: true
            });
            const lines = buffer.split("\n");
            buffer = lines.pop() || "";
            for (const line of lines){
                if (line.trim()) {
                    yield JSON.parse(line.trim());
                }
            }
        }
        if (buffer.trim()) {
            yield JSON.parse(buffer.trim());
        }
    }
}
/**
 * A reader that reads JSON data and returns an array of Document objects.
 * Supports various options to modify the output.
 */ class JSONReader extends schema.FileReader {
    constructor(options = {}){
        super();
        this.options = this.normalizeOptions(options);
    }
    // Initialize options with validation
    normalizeOptions(providedOptions) {
        const defaultOptions = {
            streamingThreshold: 50,
            ensureAscii: false,
            isJsonLines: false,
            cleanJson: true,
            logger: env.consoleLogger
        };
        const options = {
            ...defaultOptions,
            ...providedOptions
        };
        // Validation logic for numeric options
        const numericOptions = [
            "streamingThreshold",
            "collapseLength",
            "levelsBack"
        ];
        for (const key of numericOptions){
            if (typeof options[key] === "number" && options[key] < 0) {
                throw new JSONReaderError(`${key} must not be negative`);
            }
        }
        return options;
    }
    /**
   * Loads JSON data and returns an array of Document objects.
   *
   * @param {Uint8Array} content - The JSON data as a Uint8Array.
   * @return {Promise<Document[]>} A Promise that resolves to an array of Document objects.
   */ async loadDataAsContent(content) {
        const documents = [];
        for await (const document of this.parseJson(content)){
            documents.push(document);
        }
        return documents;
    }
    async *parseJson(content) {
        const jsonStr = new TextDecoder("utf-8").decode(content);
        const limit = (this.options.streamingThreshold ?? Infinity) * 1024 * 1024 / 2;
        const shouldStream = jsonStr.length > limit;
        const parsedData = JSONParser.parse(shouldStream ? content : jsonStr, this.options.isJsonLines ?? false, shouldStream ? true : false, this.options.logger);
        this.options.logger?.log(`Using ${shouldStream ? "streaming parser" : "JSON.parse"} as string length ${shouldStream ? "exceeds" : "is less than"} calculated character limit: "${limit}"`);
        for await (const value of parsedData){
            // Yield the parsed data directly if cleanJson is false or the value is not an array.
            if (!this.options.cleanJson || !Array.isArray(value)) {
                yield await this.createDocument(value);
            } else {
                // If it's an Array, yield each item seperately, i.e. create a document per top-level array of the json
                for (const item of value){
                    yield await this.createDocument(item);
                }
            }
        }
    }
    async createDocument(data) {
        const docText = this.options.levelsBack === undefined ? this.formatJsonString(data) : await this.prepareDepthFirstYield(data);
        return new schema.Document({
            text: this.options.ensureAscii ? this.convertToAscii(docText) : docText,
            metadata: {
                doc_length: docText.length,
                traversal_data: {
                    levels_back: this.options.levelsBack,
                    collapse_length: this.options.collapseLength
                }
            }
        });
    }
    // Note: JSON.stringify does not differentiate between indent "undefined/null"(= no whitespaces) and "0"(= no whitespaces, but linebreaks)
    // as python json.dumps does. Thats why we use indent 1 and remove the leading spaces.
    formatJsonString(data) {
        try {
            const jsonStr = JSON.stringify(data, null, this.options.cleanJson ? 1 : 0);
            if (this.options.cleanJson) {
                return jsonStr.split("\n").filter((line)=>!/^[{}[\],]*$/.test(line.trim())).map((line)=>line.trimStart()).join("\n");
            }
            return jsonStr;
        } catch (e) {
            throw new JSONStringifyError(`Error stringifying JSON data: ${e instanceof Error ? e.message : "Unknown error occurred"}`, {
                cause: e
            });
        }
    }
    async prepareDepthFirstYield(data) {
        const levelsBack = this.options.levelsBack ?? 0;
        const results = [];
        for await (const value of this.depthFirstYield(data, levelsBack === 0 ? Infinity : levelsBack, [], this.options.collapseLength)){
            results.push(value);
        }
        return results.join("\n");
    }
    /**
   * A generator function that determines the next step in traversing the JSON data.
   * If collapseLength is set and the serialized JSON string is not null, it yields the collapsed string.
   * Otherwise it delegates traversal to the `traverseJsonData` function.
   */ async *depthFirstYield(jsonData, levelsBack, path, collapseLength) {
        const jsonStr = this.serializeAndCollapse(jsonData, levelsBack, path, collapseLength);
        if (jsonStr !== null) {
            yield jsonStr;
        } else {
            yield* this.traverseJsonData(jsonData, levelsBack, path, collapseLength);
        }
    }
    /**
   * Traverse the JSON data.
   * If the data is an array, it traverses each item in the array.
   * If the data is an object, it delegates traversal to the `traverseObject` function.
   * If the data is a primitive value, it yields the value with the path.
   *
   */ async *traverseJsonData(jsonData, levelsBack, path, collapseLength) {
        if (Array.isArray(jsonData)) {
            for (const item of jsonData){
                yield* this.depthFirstYield(item, levelsBack, path, collapseLength);
            }
        } else if (jsonData !== null && typeof jsonData === "object") {
            yield* this.traverseObject(jsonData, levelsBack, path, collapseLength);
        } else {
            yield `${path.slice(-levelsBack).join(" ")} ${String(jsonData)}`;
        }
    }
    async *traverseObject(jsonObject, levelsBack, path, collapseLength) {
        const originalLength = path.length;
        for (const [key, value] of Object.entries(jsonObject)){
            path.push(key);
            yield* this.depthFirstYield(value, levelsBack, path, collapseLength);
            path.length = originalLength; // Reset path length to original. Avoids cloning the path array every time.
        }
    }
    serializeAndCollapse(jsonData, levelsBack, path, collapseLength) {
        try {
            const jsonStr = JSON.stringify(jsonData);
            return collapseLength !== undefined && jsonStr.length <= collapseLength ? `${path.slice(-levelsBack).join(" ")} ${jsonStr}` : null;
        } catch (e) {
            throw new JSONStringifyError(`Error stringifying JSON data: ${e instanceof Error ? e.message : "Unknown error occurred"}`, {
                cause: e
            });
        }
    }
    convertToAscii(str) {
        return str.replaceAll(/[\u007F-\uFFFF]/g, (char)=>`\\u${char.charCodeAt(0).toString(16).padStart(4, "0")}`);
    }
}

exports.JSONParseError = JSONParseError;
exports.JSONReader = JSONReader;
exports.JSONReaderError = JSONReaderError;
exports.JSONStringifyError = JSONStringifyError;
