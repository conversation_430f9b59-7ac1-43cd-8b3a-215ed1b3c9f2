/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/documents/[id]/route";
exports.ids = ["app/api/documents/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute&page=%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute&page=%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_code_chatdoc_v1_src_app_api_documents_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/documents/[id]/route.ts */ \"(rsc)/./src/app/api/documents/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/documents/[id]/route\",\n        pathname: \"/api/documents/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/documents/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\api\\\\documents\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_code_chatdoc_v1_src_app_api_documents_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute&page=%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/documents/[id]/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/documents/[id]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_enhanced_document_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/enhanced-document-service */ \"(rsc)/./src/lib/enhanced-document-service.ts\");\n\n\n\n\n\nconst UPLOAD_DIR = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'uploads');\nasync function DELETE(request, { params }) {\n    try {\n        const { id } = params;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Document ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Find the file with this ID (check both .txt and .md extensions)\n        const possibleExtensions = [\n            '.txt',\n            '.md'\n        ];\n        let filePath = null;\n        for (const ext of possibleExtensions){\n            const testPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(UPLOAD_DIR, `${id}${ext}`);\n            if ((0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(testPath)) {\n                filePath = testPath;\n                break;\n            }\n        }\n        if (!filePath) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Document not found'\n            }, {\n                status: 404\n            });\n        }\n        // Delete the file\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.unlink)(filePath);\n        // Remove from Enhanced Document Service\n        try {\n            await _lib_enhanced_document_service__WEBPACK_IMPORTED_MODULE_4__[\"default\"].removeDocument(id);\n        } catch (serviceError) {\n            console.error('Failed to remove document from enhanced service:', serviceError);\n        // Continue even if service removal fails - file is still deleted\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Document deleted successfully'\n        });\n    } catch (error) {\n        console.error('Delete error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete document'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/documents/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/deepseek-llm.ts":
/*!*********************************!*\
  !*** ./src/lib/deepseek-llm.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeepSeekService: () => (/* binding */ DeepSeekService)\n/* harmony export */ });\nclass DeepSeekService {\n    constructor(config){\n        this.config = {\n            temperature: 0.7,\n            maxTokens: 4000,\n            ...config\n        };\n    }\n    async chat(messages) {\n        try {\n            const response = await fetch(`${this.config.baseURL}/chat/completions`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.config.apiKey}`\n                },\n                body: JSON.stringify({\n                    model: this.config.model,\n                    messages: messages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content\n                        })),\n                    temperature: this.config.temperature,\n                    max_tokens: this.config.maxTokens,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('No response from DeepSeek API');\n            }\n            const choice = data.choices[0];\n            return {\n                message: {\n                    role: choice.message.role,\n                    content: choice.message.content\n                },\n                raw: data\n            };\n        } catch (error) {\n            console.error('DeepSeek LLM error:', error);\n            throw error;\n        }\n    }\n    async queryWithContext(message, context) {\n        const messages = [\n            {\n                role: 'system',\n                content: `You are a helpful AI assistant that answers questions based on the provided document context. Use the context to provide accurate and relevant answers. If the context doesn't contain enough information to answer the question, say so clearly.\n\nContext:\n${context}`\n            },\n            {\n                role: 'user',\n                content: message\n            }\n        ];\n        const response = await this.chat(messages);\n        return response.message.content;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/deepseek-llm.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/document-manager.ts":
/*!*************************************!*\
  !*** ./src/lib/document-manager.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nclass DocumentManager {\n    async initialize() {\n        try {\n            if ((0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(this.metadataFile)) {\n                const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile)(this.metadataFile, 'utf-8');\n                const documentsArray = JSON.parse(data);\n                this.documents = new Map(documentsArray.map((doc)=>[\n                        doc.id,\n                        doc\n                    ]));\n                console.log(`Loaded ${this.documents.size} document metadata entries`);\n            }\n        } catch (error) {\n            console.error('Failed to load document metadata:', error);\n        }\n    }\n    async addDocument(metadata) {\n        const fullMetadata = {\n            ...metadata,\n            uploadDate: new Date().toISOString(),\n            lastModified: new Date().toISOString(),\n            status: 'uploading'\n        };\n        this.documents.set(metadata.id, fullMetadata);\n        await this.saveMetadata();\n        return fullMetadata;\n    }\n    async updateDocumentStatus(id, status, errorMessage) {\n        const doc = this.documents.get(id);\n        if (doc) {\n            doc.status = status;\n            doc.lastModified = new Date().toISOString();\n            if (errorMessage) {\n                doc.errorMessage = errorMessage;\n            }\n            this.documents.set(id, doc);\n            await this.saveMetadata();\n        }\n    }\n    async removeDocument(id) {\n        this.documents.delete(id);\n        await this.saveMetadata();\n    }\n    getDocument(id) {\n        return this.documents.get(id);\n    }\n    getAllDocuments() {\n        return Array.from(this.documents.values());\n    }\n    getDocumentsByStatus(status) {\n        return Array.from(this.documents.values()).filter((doc)=>doc.status === status);\n    }\n    async saveMetadata() {\n        try {\n            const documentsArray = Array.from(this.documents.values());\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.writeFile)(this.metadataFile, JSON.stringify(documentsArray, null, 2));\n        } catch (error) {\n            console.error('Failed to save document metadata:', error);\n        }\n    }\n    async getStats() {\n        const docs = Array.from(this.documents.values());\n        return {\n            total: docs.length,\n            indexed: docs.filter((d)=>d.status === 'indexed').length,\n            processing: docs.filter((d)=>d.status === 'processing').length,\n            errors: docs.filter((d)=>d.status === 'error').length,\n            totalSize: docs.reduce((sum, doc)=>sum + doc.size, 0)\n        };\n    }\n    constructor(){\n        this.metadataFile = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'documents_metadata.json');\n        this.documents = new Map();\n    }\n}\n// Singleton instance\nconst documentManager = new DocumentManager();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (documentManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/document-manager.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/doubao-embedding.ts":
/*!*************************************!*\
  !*** ./src/lib/doubao-embedding.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoubaoEmbeddingService: () => (/* binding */ DoubaoEmbeddingService)\n/* harmony export */ });\nclass DoubaoEmbeddingService {\n    constructor(config){\n        this.config = config;\n    }\n    async getEmbedding(text) {\n        try {\n            const response = await fetch(`${this.config.baseURL}/embeddings`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.config.apiKey}`\n                },\n                body: JSON.stringify({\n                    model: this.config.model,\n                    input: text,\n                    encoding_format: 'float'\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`Doubao Embedding API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.data || data.data.length === 0) {\n                throw new Error('No embedding data returned from Doubao API');\n            }\n            return data.data[0].embedding;\n        } catch (error) {\n            console.error('Doubao embedding error:', error);\n            throw error;\n        }\n    }\n    async getEmbeddings(texts) {\n        try {\n            const response = await fetch(`${this.config.baseURL}/embeddings`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.config.apiKey}`\n                },\n                body: JSON.stringify({\n                    model: this.config.model,\n                    input: texts,\n                    encoding_format: 'float'\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`Doubao Embedding API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.data || data.data.length === 0) {\n                throw new Error('No embedding data returned from Doubao API');\n            }\n            // Sort by index to maintain order\n            return data.data.sort((a, b)=>a.index - b.index).map((item)=>item.embedding);\n        } catch (error) {\n            console.error('Doubao embeddings error:', error);\n            throw error;\n        }\n    }\n    async calculateSimilarity(embedding1, embedding2) {\n        if (embedding1.length !== embedding2.length) {\n            throw new Error('Embeddings must have the same dimension');\n        }\n        // Calculate cosine similarity\n        let dotProduct = 0;\n        let norm1 = 0;\n        let norm2 = 0;\n        for(let i = 0; i < embedding1.length; i++){\n            dotProduct += embedding1[i] * embedding2[i];\n            norm1 += embedding1[i] * embedding1[i];\n            norm2 += embedding2[i] * embedding2[i];\n        }\n        const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);\n        return magnitude === 0 ? 0 : dotProduct / magnitude;\n    }\n    async findMostSimilar(queryEmbedding, documentEmbeddings, topK = 5) {\n        const similarities = await Promise.all(documentEmbeddings.map(async (doc)=>({\n                similarity: await this.calculateSimilarity(queryEmbedding, doc.embedding),\n                metadata: doc.metadata\n            })));\n        return similarities.sort((a, b)=>b.similarity - a.similarity).slice(0, topK);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/doubao-embedding.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/enhanced-document-service.ts":
/*!**********************************************!*\
  !*** ./src/lib/enhanced-document-service.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _document_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./document-manager */ \"(rsc)/./src/lib/document-manager.ts\");\n/* harmony import */ var _deepseek_llm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./deepseek-llm */ \"(rsc)/./src/lib/deepseek-llm.ts\");\n/* harmony import */ var _doubao_embedding__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./doubao-embedding */ \"(rsc)/./src/lib/doubao-embedding.ts\");\n// Enhanced document service with DeepSeek + Doubao integration\n\n\n\n\n// Create service instances\nconst deepSeekService = new _deepseek_llm__WEBPACK_IMPORTED_MODULE_2__.DeepSeekService({\n    apiKey: process.env.DEEPSEEK_API_KEY || \"\",\n    baseURL: process.env.DEEPSEEK_BASE_URL || \"https://ark.cn-beijing.volces.com/api/v3\",\n    model: process.env.DEEPSEEK_CHAT_MODEL || \"deepseek-chat\",\n    temperature: 0.7,\n    maxTokens: 4000\n});\nconst doubaoEmbeddingService = new _doubao_embedding__WEBPACK_IMPORTED_MODULE_3__.DoubaoEmbeddingService({\n    apiKey: process.env.DOUBAO_API_KEY || \"\",\n    baseURL: process.env.DOUBAO_BASE_URL || \"https://ark.cn-beijing.volces.com/api/v3\",\n    model: process.env.EMBEDDING_MODEL || \"doubao-embedding-text-240515\"\n});\nclass EnhancedDocumentService {\n    async initialize() {\n        try {\n            // Initialize document manager\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].initialize();\n            // Ensure chunks directory exists\n            await this.ensureChunksDirectory();\n            // Reload existing indexed documents into memory\n            await this.reloadExistingDocuments();\n            console.log(`Enhanced document service initialized with DeepSeek + Doubao integration. Loaded ${this.documents.size} documents.`);\n        } catch (error) {\n            console.error(\"Failed to initialize enhanced document service:\", error);\n            throw error;\n        }\n    }\n    async ensureChunksDirectory() {\n        try {\n            const fs = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! fs/promises */ \"fs/promises\", 23));\n            await fs.mkdir(this.chunksDir, {\n                recursive: true\n            });\n        } catch (error) {\n            console.error(\"Failed to create chunks directory:\", error);\n        }\n    }\n    async saveDocumentChunks(documentId, chunks) {\n        try {\n            const fs = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! fs/promises */ \"fs/promises\", 23));\n            const chunksFilePath = path__WEBPACK_IMPORTED_MODULE_0___default().join(this.chunksDir, `${documentId}.json`);\n            await fs.writeFile(chunksFilePath, JSON.stringify(chunks, null, 2));\n            console.log(`Saved ${chunks.length} chunks for document ${documentId}`);\n        } catch (error) {\n            console.error(`Failed to save chunks for document ${documentId}:`, error);\n        }\n    }\n    async loadDocumentChunks(documentId) {\n        try {\n            const fs = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! fs/promises */ \"fs/promises\", 23));\n            const chunksFilePath = path__WEBPACK_IMPORTED_MODULE_0___default().join(this.chunksDir, `${documentId}.json`);\n            try {\n                await fs.access(chunksFilePath);\n            } catch  {\n                return null; // File doesn't exist\n            }\n            const chunksData = await fs.readFile(chunksFilePath, 'utf-8');\n            const chunks = JSON.parse(chunksData);\n            console.log(`Loaded ${chunks.length} chunks for document ${documentId}`);\n            return chunks;\n        } catch (error) {\n            console.error(`Failed to load chunks for document ${documentId}:`, error);\n            return null;\n        }\n    }\n    async removeDocumentChunks(documentId) {\n        try {\n            const fs = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! fs/promises */ \"fs/promises\", 23));\n            const chunksFilePath = path__WEBPACK_IMPORTED_MODULE_0___default().join(this.chunksDir, `${documentId}.json`);\n            try {\n                await fs.unlink(chunksFilePath);\n                console.log(`Removed chunks file for document ${documentId}`);\n            } catch (error) {\n                // File might not exist, which is fine\n                console.log(`Chunks file for document ${documentId} not found (already removed)`);\n            }\n        } catch (error) {\n            console.error(`Failed to remove chunks for document ${documentId}:`, error);\n        }\n    }\n    async reloadExistingDocuments() {\n        try {\n            // Get all indexed documents from document manager\n            const indexedDocuments = _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getDocumentsByStatus('indexed');\n            console.log(`Found ${indexedDocuments.length} indexed documents to reload`);\n            for (const docMetadata of indexedDocuments){\n                try {\n                    // Construct file path\n                    const filePath = path__WEBPACK_IMPORTED_MODULE_0___default().join(process.cwd(), 'uploads', docMetadata.filename);\n                    // Check if file exists\n                    const fs = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! fs/promises */ \"fs/promises\", 23));\n                    try {\n                        await fs.access(filePath);\n                    } catch  {\n                        console.warn(`File not found for document ${docMetadata.id}: ${filePath}`);\n                        // Update status to error since file is missing\n                        await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(docMetadata.id, 'error', 'File not found on disk');\n                        continue;\n                    }\n                    // Read document content\n                    const content = await fs.readFile(filePath, 'utf-8');\n                    // Store document in memory\n                    this.documents.set(docMetadata.id, {\n                        content,\n                        metadata: docMetadata\n                    });\n                    // Try to load existing chunks first\n                    const existingChunks = await this.loadDocumentChunks(docMetadata.id);\n                    if (existingChunks && existingChunks.length > 0) {\n                        // Use existing chunks\n                        this.documentChunks.set(docMetadata.id, existingChunks);\n                        console.log(`Loaded existing ${existingChunks.length} chunks for document: ${docMetadata.originalName}`);\n                    } else {\n                        // Regenerate chunks and embeddings\n                        await this.regenerateDocumentEmbeddings(docMetadata.id, content, docMetadata.filename);\n                    }\n                    console.log(`Reloaded document: ${docMetadata.originalName} (${docMetadata.id})`);\n                } catch (docError) {\n                    console.error(`Failed to reload document ${docMetadata.id}:`, docError);\n                    // Update status to error\n                    await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(docMetadata.id, 'error', `Failed to reload: ${docError instanceof Error ? docError.message : 'Unknown error'}`);\n                }\n            }\n        } catch (error) {\n            console.error(\"Failed to reload existing documents:\", error);\n        // Don't throw here - we want the service to still initialize even if some documents fail to reload\n        }\n    }\n    async regenerateDocumentEmbeddings(documentId, content, filename) {\n        try {\n            // Chunk the document\n            const textChunks = this.chunkText(content);\n            // Generate embeddings for each chunk\n            const chunks = [];\n            for(let i = 0; i < textChunks.length; i++){\n                const chunkContent = textChunks[i];\n                try {\n                    const embedding = await doubaoEmbeddingService.getEmbedding(chunkContent);\n                    chunks.push({\n                        id: `${documentId}_chunk_${i}`,\n                        content: chunkContent,\n                        embedding,\n                        metadata: {\n                            documentId,\n                            filename,\n                            chunkIndex: i,\n                            startChar: i * 800,\n                            endChar: i * 800 + chunkContent.length\n                        }\n                    });\n                } catch (embeddingError) {\n                    console.error(`Failed to generate embedding for chunk ${i} of document ${documentId}:`, embeddingError);\n                // Continue with other chunks even if one fails\n                }\n            }\n            // Store chunks in memory and save to disk\n            this.documentChunks.set(documentId, chunks);\n            await this.saveDocumentChunks(documentId, chunks);\n            console.log(`Generated ${chunks.length} chunks with embeddings for document ${filename}`);\n        } catch (error) {\n            console.error(`Failed to regenerate embeddings for document ${documentId}:`, error);\n            throw error;\n        }\n    }\n    chunkText(text, chunkSize = 1000, overlap = 200) {\n        const chunks = [];\n        let start = 0;\n        while(start < text.length){\n            const end = Math.min(start + chunkSize, text.length);\n            const chunk = text.slice(start, end);\n            chunks.push(chunk);\n            if (end === text.length) break;\n            start = end - overlap;\n        }\n        return chunks;\n    }\n    async addDocument(filePath, documentId, filename, fileSize) {\n        try {\n            await this.initialize();\n            // Update document status to processing\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'processing');\n            // Read the document content\n            const fs = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! fs/promises */ \"fs/promises\", 23));\n            const content = await fs.readFile(filePath, 'utf-8');\n            // Store document\n            this.documents.set(documentId, {\n                content,\n                metadata: {\n                    id: documentId,\n                    filename,\n                    originalName: filename,\n                    size: fileSize,\n                    uploadDate: new Date().toISOString(),\n                    lastModified: new Date().toISOString(),\n                    type: path__WEBPACK_IMPORTED_MODULE_0___default().extname(filename),\n                    status: 'processing'\n                }\n            });\n            // Chunk the document\n            const textChunks = this.chunkText(content);\n            // Generate embeddings for each chunk\n            const chunks = [];\n            for(let i = 0; i < textChunks.length; i++){\n                const chunkContent = textChunks[i];\n                try {\n                    const embedding = await doubaoEmbeddingService.getEmbedding(chunkContent);\n                    chunks.push({\n                        id: `${documentId}_chunk_${i}`,\n                        content: chunkContent,\n                        embedding,\n                        metadata: {\n                            documentId,\n                            filename,\n                            chunkIndex: i,\n                            startChar: i * 800,\n                            endChar: i * 800 + chunkContent.length\n                        }\n                    });\n                } catch (embeddingError) {\n                    console.error(`Failed to generate embedding for chunk ${i}:`, embeddingError);\n                // Continue with other chunks even if one fails\n                }\n            }\n            // Store chunks in memory and save to disk\n            this.documentChunks.set(documentId, chunks);\n            await this.saveDocumentChunks(documentId, chunks);\n            // Update document status to indexed\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'indexed');\n            console.log(`Document ${filename} processed with ${chunks.length} chunks and embeddings`);\n        } catch (error) {\n            console.error(\"Failed to add document:\", error);\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'error', error instanceof Error ? error.message : 'Unknown error');\n            throw error;\n        }\n    }\n    async removeDocument(documentId) {\n        try {\n            await this.initialize();\n            // Remove from document manager\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].removeDocument(documentId);\n            // Remove from our stores\n            this.documents.delete(documentId);\n            this.documentChunks.delete(documentId);\n            // Remove chunks file from disk\n            await this.removeDocumentChunks(documentId);\n            console.log(`Document ${documentId} removed from enhanced document store`);\n        } catch (error) {\n            console.error(\"Failed to remove document:\", error);\n            throw error;\n        }\n    }\n    async query(message, _chatHistory = []) {\n        try {\n            await this.initialize();\n            console.log(`Query received: \"${message}\"`);\n            console.log(`Documents in memory: ${this.documents.size}`);\n            console.log(`Document chunks available: ${Array.from(this.documentChunks.values()).flat().length}`);\n            // Check if we have any documents\n            if (this.documents.size === 0) {\n                console.log(\"No documents found in memory - returning empty response\");\n                return {\n                    response: \"I don't have any documents to search through. Please upload some documents first and wait for them to be processed.\",\n                    sources: []\n                };\n            }\n            // Generate embedding for the query\n            console.log(\"Generating embedding for query...\");\n            const queryEmbedding = await doubaoEmbeddingService.getEmbedding(message);\n            console.log(`Query embedding generated with ${queryEmbedding.length} dimensions`);\n            // Get all document chunks with embeddings\n            const allChunks = [];\n            for (const [documentId, chunks] of this.documentChunks.entries()){\n                console.log(`Processing ${chunks.length} chunks from document ${documentId}`);\n                for (const chunk of chunks){\n                    allChunks.push({\n                        embedding: chunk.embedding,\n                        metadata: {\n                            documentId,\n                            filename: chunk.metadata.filename,\n                            chunkIndex: chunk.metadata.chunkIndex\n                        },\n                        content: chunk.content\n                    });\n                }\n            }\n            console.log(`Total chunks available for similarity search: ${allChunks.length}`);\n            // Find most similar chunks\n            console.log(\"Finding most similar chunks...\");\n            const similarChunks = await doubaoEmbeddingService.findMostSimilar(queryEmbedding, allChunks, 5 // Top 5 most relevant chunks\n            );\n            console.log(`Found ${similarChunks.length} similar chunks`);\n            // Create context from relevant chunks\n            const context = similarChunks.map((result, index)=>{\n                const chunk = allChunks.find((c)=>c.metadata.documentId === result.metadata.documentId && c.metadata.chunkIndex === result.metadata.chunkIndex);\n                return `[Chunk ${index + 1} from ${result.metadata.filename}]:\\n${chunk?.content || 'Content not found'}`;\n            }).join('\\n\\n---\\n\\n');\n            // Use DeepSeek to answer the question with context\n            const response = await deepSeekService.queryWithContext(message, context);\n            // Create sources from similar chunks\n            const sources = similarChunks.map((result)=>{\n                const chunk = allChunks.find((c)=>c.metadata.documentId === result.metadata.documentId && c.metadata.chunkIndex === result.metadata.chunkIndex);\n                return {\n                    document: result.metadata.filename,\n                    chunk: chunk?.content.substring(0, 200) + \"...\" || 0,\n                    relevance: Math.round(result.similarity * 100) / 100\n                };\n            });\n            return {\n                response,\n                sources\n            };\n        } catch (error) {\n            console.error(\"Failed to query with DeepSeek + Doubao:\", error);\n            throw error;\n        }\n    }\n    async getDocuments() {\n        return Array.from(this.documents.values()).map((doc)=>doc.metadata);\n    }\n    async getDocumentStats() {\n        const documents = Array.from(this.documents.values());\n        const chunks = Array.from(this.documentChunks.values()).flat();\n        return {\n            totalDocuments: documents.length,\n            totalChunks: chunks.length,\n            totalEmbeddings: chunks.filter((c)=>c.embedding.length > 0).length,\n            averageChunksPerDocument: documents.length > 0 ? Math.round(chunks.length / documents.length) : 0\n        };\n    }\n    constructor(){\n        this.documents = new Map();\n        this.documentChunks = new Map();\n        this.chunksDir = path__WEBPACK_IMPORTED_MODULE_0___default().join(process.cwd(), 'document_chunks');\n    }\n}\n// Singleton instance\nconst enhancedDocumentService = new EnhancedDocumentService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (enhancedDocumentService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/enhanced-document-service.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute&page=%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();