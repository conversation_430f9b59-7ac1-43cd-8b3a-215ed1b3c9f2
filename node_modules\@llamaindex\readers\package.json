{"name": "@llamaindex/readers", "description": "LlamaIndex Readers", "version": "3.1.12", "type": "module", "exports": {"./node/hook": "./node/dist/hook.js", "./node": "./node/dist/index.js", "./csv": {"edge-light": {"types": "./csv/dist/index.edge-light.d.ts", "default": "./csv/dist/index.edge-light.js"}, "workerd": {"types": "./csv/dist/index.workerd.d.ts", "default": "./csv/dist/index.workerd.js"}, "require": {"types": "./csv/dist/index.d.cts", "default": "./csv/dist/index.cjs"}, "import": {"types": "./csv/dist/index.d.ts", "default": "./csv/dist/index.js"}}, "./directory": {"edge-light": {"types": "./directory/dist/index.edge-light.d.ts", "default": "./directory/dist/index.edge-light.js"}, "workerd": {"types": "./directory/dist/index.workerd.d.ts", "default": "./directory/dist/index.workerd.js"}, "require": {"types": "./directory/dist/index.d.cts", "default": "./directory/dist/index.cjs"}, "import": {"types": "./directory/dist/index.d.ts", "default": "./directory/dist/index.js"}}, "./docx": {"require": {"types": "./docx/dist/index.d.cts", "default": "./docx/dist/index.cjs"}, "import": {"types": "./docx/dist/index.d.ts", "default": "./docx/dist/index.js"}}, "./html": {"require": {"types": "./html/dist/index.d.cts", "default": "./html/dist/index.cjs"}, "import": {"types": "./html/dist/index.d.ts", "default": "./html/dist/index.js"}}, "./image": {"require": {"types": "./image/dist/index.d.cts", "default": "./image/dist/index.cjs"}, "import": {"types": "./image/dist/index.d.ts", "default": "./image/dist/index.js"}}, "./json": {"require": {"types": "./json/dist/index.d.cts", "default": "./json/dist/index.cjs"}, "import": {"types": "./json/dist/index.d.ts", "default": "./json/dist/index.js"}}, "./markdown": {"require": {"types": "./markdown/dist/index.d.cts", "default": "./markdown/dist/index.cjs"}, "import": {"types": "./markdown/dist/index.d.ts", "default": "./markdown/dist/index.js"}}, "./obsidian": {"edge-light": {"types": "./obsidian/dist/index.edge-light.d.ts", "default": "./obsidian/dist/index.edge-light.js"}, "workerd": {"types": "./obsidian/dist/index.workerd.d.ts", "default": "./obsidian/dist/index.workerd.js"}, "require": {"types": "./obsidian/dist/index.d.cts", "default": "./obsidian/dist/index.cjs"}, "import": {"types": "./obsidian/dist/index.d.ts", "default": "./obsidian/dist/index.js"}}, "./pdf": {"require": {"types": "./pdf/dist/index.d.cts", "default": "./pdf/dist/index.cjs"}, "import": {"types": "./pdf/dist/index.d.ts", "default": "./pdf/dist/index.js"}}, "./text": {"require": {"types": "./text/dist/index.d.cts", "default": "./text/dist/index.cjs"}, "import": {"types": "./text/dist/index.d.ts", "default": "./text/dist/index.js"}}, "./xml": {"require": {"types": "./xml/dist/index.d.cts", "default": "./xml/dist/index.cjs"}, "import": {"types": "./xml/dist/index.d.ts", "default": "./xml/dist/index.js"}}}, "files": ["csv", "directory", "docx", "html", "image", "json", "markdown", "obsidian", "pdf", "text", "node", "xml"], "repository": {"type": "git", "url": "git+https://github.com/run-llama/LlamaIndexTS.git", "directory": "packages/readers"}, "devDependencies": {"@types/node": "^22.9.0", "p-limit": "^6.1.0", "string-strip-html": "^13.4.8", "@llamaindex/core": "0.6.13", "@llamaindex/env": "0.1.30"}, "peerDependencies": {"@llamaindex/core": "0.6.13", "@llamaindex/env": "0.1.30"}, "dependencies": {"@discoveryjs/json-ext": "^0.6.1", "@xmldom/xmldom": "^0.9.8", "csv-parse": "^5.5.6", "mammoth": "^1.7.2", "unpdf": "^0.12.1"}, "scripts": {"build": "bunchee", "dev": "bunchee --watch"}}