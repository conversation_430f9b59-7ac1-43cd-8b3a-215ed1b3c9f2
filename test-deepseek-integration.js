// Test script to verify DeepSeek API integration
// Run with: node test-deepseek-integration.js

const fetch = require('node-fetch');

async function testDeepSeekAPI() {
  const apiKey = process.env.DEEPSEEK_API_KEY;
  const baseURL = process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com';
  
  if (!apiKey || apiKey === 'your_deepseek_api_key_here') {
    console.log('❌ DeepSeek API key not configured');
    console.log('Please set DEEPSEEK_API_KEY in your environment variables');
    return;
  }

  console.log('🧪 Testing DeepSeek API integration...');
  
  try {
    // Test 1: List available models
    console.log('\n📋 Testing model listing...');
    const modelsResponse = await fetch(`${baseURL}/models`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
    });
    
    if (modelsResponse.ok) {
      const models = await modelsResponse.json();
      console.log('✅ Models available:', models.data.map(m => m.id).join(', '));
    } else {
      console.log('❌ Failed to list models:', modelsResponse.status);
    }

    // Test 2: Simple chat completion
    console.log('\n💬 Testing chat completion...');
    const chatResponse = await fetch(`${baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'user',
            content: 'Hello! Can you help me analyze documents?'
          }
        ],
        temperature: 0.7,
        max_tokens: 100,
      }),
    });

    if (chatResponse.ok) {
      const chatData = await chatResponse.json();
      console.log('✅ Chat response:', chatData.choices[0].message.content);
    } else {
      const errorData = await chatResponse.json().catch(() => ({}));
      console.log('❌ Chat failed:', chatResponse.status, errorData);
    }

    // Test 3: Reasoning model
    console.log('\n🧠 Testing reasoning model...');
    const reasoningResponse = await fetch(`${baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'deepseek-reasoner',
        messages: [
          {
            role: 'user',
            content: 'Analyze this document content and explain the main concepts: "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed."'
          }
        ],
        temperature: 0.3,
        max_tokens: 200,
      }),
    });

    if (reasoningResponse.ok) {
      const reasoningData = await reasoningResponse.json();
      console.log('✅ Reasoning response:', reasoningData.choices[0].message.content);
    } else {
      const errorData = await reasoningResponse.json().catch(() => ({}));
      console.log('❌ Reasoning failed:', reasoningResponse.status, errorData);
    }

    console.log('\n🎉 DeepSeek API integration test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testDeepSeekAPI();
