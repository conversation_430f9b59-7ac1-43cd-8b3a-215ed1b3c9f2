Object.defineProperty(exports, '__esModule', { value: true });

var schema = require('@llamaindex/core/schema');

/**
 * Read a .txt file
 */ class TextFileReader extends schema.FileReader {
    async loadDataAsContent(fileContent) {
        const decoder = new TextDecoder("utf-8");
        const dataBuffer = decoder.decode(fileContent);
        return [
            new schema.Document({
                text: dataBuffer
            })
        ];
    }
}

exports.TextFileReader = TextFileReader;
