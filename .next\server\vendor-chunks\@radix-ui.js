"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcY29kZVxcY2hhdGRvYy12MVxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXG51bWJlclxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvbnVtYmVyL3NyYy9udW1iZXIudHNcbmZ1bmN0aW9uIGNsYW1wKHZhbHVlLCBbbWluLCBtYXhdKSB7XG4gIHJldHVybiBNYXRoLm1pbihtYXgsIE1hdGgubWF4KG1pbiwgdmFsdWUpKTtcbn1cbmV4cG9ydCB7XG4gIGNsYW1wXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcY29kZVxcY2hhdGRvYy12MVxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHByaW1pdGl2ZVxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvcHJpbWl0aXZlL3NyYy9wcmltaXRpdmUudHN4XG5mdW5jdGlvbiBjb21wb3NlRXZlbnRIYW5kbGVycyhvcmlnaW5hbEV2ZW50SGFuZGxlciwgb3VyRXZlbnRIYW5kbGVyLCB7IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgfSA9IHt9KSB7XG4gIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgIG9yaWdpbmFsRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIGlmIChjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPT09IGZhbHNlIHx8ICFldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICByZXR1cm4gb3VyRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIH1cbiAgfTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VFdmVudEhhbmRsZXJzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEMsdUJBQXVCLGdEQUFtQjtBQUMxQztBQUNBLFVBQVUsZ0JBQWdCO0FBQzFCLHlCQUF5QixzREFBRyw4QkFBOEIsc0JBQXNCO0FBQ2hGO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxjb2RlXFxjaGF0ZG9jLXYxXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtZGlyZWN0aW9uXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvZGlyZWN0aW9uL3NyYy9kaXJlY3Rpb24udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIERpcmVjdGlvbkNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KHZvaWQgMCk7XG52YXIgRGlyZWN0aW9uUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgY29uc3QgeyBkaXIsIGNoaWxkcmVuIH0gPSBwcm9wcztcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goRGlyZWN0aW9uQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZTogZGlyLCBjaGlsZHJlbiB9KTtcbn07XG5mdW5jdGlvbiB1c2VEaXJlY3Rpb24obG9jYWxEaXIpIHtcbiAgY29uc3QgZ2xvYmFsRGlyID0gUmVhY3QudXNlQ29udGV4dChEaXJlY3Rpb25Db250ZXh0KTtcbiAgcmV0dXJuIGxvY2FsRGlyIHx8IGdsb2JhbERpciB8fCBcImx0clwiO1xufVxudmFyIFByb3ZpZGVyID0gRGlyZWN0aW9uUHJvdmlkZXI7XG5leHBvcnQge1xuICBEaXJlY3Rpb25Qcm92aWRlcixcbiAgUHJvdmlkZXIsXG4gIHVzZURpcmVjdGlvblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePresence.useEffect\": ()=>{\n            const currentAnimationName = getAnimationName(stylesRef.current);\n            prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n        }\n    }[\"usePresence.useEffect\"], [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            const styles = stylesRef.current;\n            const wasPresent = prevPresentRef.current;\n            const hasPresentChanged = wasPresent !== present;\n            if (hasPresentChanged) {\n                const prevAnimationName = prevAnimationNameRef.current;\n                const currentAnimationName = getAnimationName(styles);\n                if (present) {\n                    send(\"MOUNT\");\n                } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                    send(\"UNMOUNT\");\n                } else {\n                    const isAnimating = prevAnimationName !== currentAnimationName;\n                    if (wasPresent && isAnimating) {\n                        send(\"ANIMATION_OUT\");\n                    } else {\n                        send(\"UNMOUNT\");\n                    }\n                }\n                prevPresentRef.current = present;\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            if (node) {\n                let timeoutId;\n                const ownerWindow = node.ownerDocument.defaultView ?? window;\n                const handleAnimationEnd = {\n                    \"usePresence.useLayoutEffect.handleAnimationEnd\": (event)=>{\n                        const currentAnimationName = getAnimationName(stylesRef.current);\n                        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                        if (event.target === node && isCurrentAnimation) {\n                            send(\"ANIMATION_END\");\n                            if (!prevPresentRef.current) {\n                                const currentFillMode = node.style.animationFillMode;\n                                node.style.animationFillMode = \"forwards\";\n                                timeoutId = ownerWindow.setTimeout({\n                                    \"usePresence.useLayoutEffect.handleAnimationEnd\": ()=>{\n                                        if (node.style.animationFillMode === \"forwards\") {\n                                            node.style.animationFillMode = currentFillMode;\n                                        }\n                                    }\n                                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"]);\n                            }\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"];\n                const handleAnimationStart = {\n                    \"usePresence.useLayoutEffect.handleAnimationStart\": (event)=>{\n                        if (event.target === node) {\n                            prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationStart\"];\n                node.addEventListener(\"animationstart\", handleAnimationStart);\n                node.addEventListener(\"animationcancel\", handleAnimationEnd);\n                node.addEventListener(\"animationend\", handleAnimationEnd);\n                return ({\n                    \"usePresence.useLayoutEffect\": ()=>{\n                        ownerWindow.clearTimeout(timeoutId);\n                        node.removeEventListener(\"animationstart\", handleAnimationStart);\n                        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                        node.removeEventListener(\"animationend\", handleAnimationEnd);\n                    }\n                })[\"usePresence.useLayoutEffect\"];\n            } else {\n                send(\"ANIMATION_END\");\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"usePresence.useCallback\": (node2)=>{\n                stylesRef.current = node2 ? getComputedStyle(node2) : null;\n                setNode(node2);\n            }\n        }[\"usePresence.useCallback\"], [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-progress/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Progress: () => (/* binding */ Progress),\n/* harmony export */   ProgressIndicator: () => (/* binding */ ProgressIndicator),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createProgressScope: () => (/* binding */ createProgressScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Indicator,Progress,ProgressIndicator,Root,createProgressScope auto */ // src/progress.tsx\n\n\n\n\nvar PROGRESS_NAME = \"Progress\";\nvar DEFAULT_MAX = 100;\nvar [createProgressContext, createProgressScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROGRESS_NAME);\nvar [ProgressProvider, useProgressContext] = createProgressContext(PROGRESS_NAME);\nvar Progress = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeProgress, value: valueProp = null, max: maxProp, getValueLabel = defaultGetValueLabel, ...progressProps } = props;\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n        console.error(getInvalidMaxError(`${maxProp}`, \"Progress\"));\n    }\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n        console.error(getInvalidValueError(`${valueProp}`, \"Progress\"));\n    }\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : void 0;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ProgressProvider, {\n        scope: __scopeProgress,\n        value,\n        max,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.div, {\n            \"aria-valuemax\": max,\n            \"aria-valuemin\": 0,\n            \"aria-valuenow\": isNumber(value) ? value : void 0,\n            \"aria-valuetext\": valueLabel,\n            role: \"progressbar\",\n            \"data-state\": getProgressState(value, max),\n            \"data-value\": value ?? void 0,\n            \"data-max\": max,\n            ...progressProps,\n            ref: forwardedRef\n        })\n    });\n});\nProgress.displayName = PROGRESS_NAME;\nvar INDICATOR_NAME = \"ProgressIndicator\";\nvar ProgressIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.div, {\n        \"data-state\": getProgressState(context.value, context.max),\n        \"data-value\": context.value ?? void 0,\n        \"data-max\": context.max,\n        ...indicatorProps,\n        ref: forwardedRef\n    });\n});\nProgressIndicator.displayName = INDICATOR_NAME;\nfunction defaultGetValueLabel(value, max) {\n    return `${Math.round(value / max * 100)}%`;\n}\nfunction getProgressState(value, maxValue) {\n    return value == null ? \"indeterminate\" : value === maxValue ? \"complete\" : \"loading\";\n}\nfunction isNumber(value) {\n    return typeof value === \"number\";\n}\nfunction isValidMaxNumber(max) {\n    return isNumber(max) && !isNaN(max) && max > 0;\n}\nfunction isValidValueNumber(value, max) {\n    return isNumber(value) && !isNaN(value) && value <= max && value >= 0;\n}\nfunction getInvalidMaxError(propValue, componentName) {\n    return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\nfunction getInvalidValueError(propValue, componentName) {\n    return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\nvar Root = Progress;\nvar Indicator = ProgressIndicator;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // src/scroll-area.tsx\n\n\n\n\n\n\n\n\n\n\n// src/use-state-machine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/scroll-area.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollArea.useComposedRefs[composedRefs]\": (node)=>setScrollArea(node)\n    }[\"ScrollArea.useComposedRefs[composedRefs]\"]);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbar.useEffect\": ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n            return ({\n                \"ScrollAreaScrollbar.useEffect\": ()=>{\n                    isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n                }\n            })[\"ScrollAreaScrollbar.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbar.useEffect\"], [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n            const scrollArea = context.scrollArea;\n            let hideTimer = 0;\n            if (scrollArea) {\n                const handlePointerEnter = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        setVisible(true);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\"];\n                const handlePointerLeave = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>{\n                        hideTimer = window.setTimeout({\n                            \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>setVisible(false)\n                        }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"], context.scrollHideDelay);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"];\n                scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n                return ({\n                    \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n                    }\n                })[\"ScrollAreaScrollbarHover.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarHover.useEffect\"], [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\": ()=>send(\"SCROLL_END\")\n    }[\"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\"], 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            if (state === \"idle\") {\n                const hideTimer = window.setTimeout({\n                    \"ScrollAreaScrollbarScroll.useEffect.hideTimer\": ()=>send(\"HIDE\")\n                }[\"ScrollAreaScrollbarScroll.useEffect.hideTimer\"], context.scrollHideDelay);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>window.clearTimeout(hideTimer)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            const viewport = context.viewport;\n            const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n            if (viewport) {\n                let prevScrollPos = viewport[scrollDirection];\n                const handleScroll = {\n                    \"ScrollAreaScrollbarScroll.useEffect.handleScroll\": ()=>{\n                        const scrollPos = viewport[scrollDirection];\n                        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                        if (hasScrollInDirectionChanged) {\n                            send(\"SCROLL\");\n                            debounceScrollEnd();\n                        }\n                        prevScrollPos = scrollPos;\n                    }\n                }[\"ScrollAreaScrollbarScroll.useEffect.handleScroll\"];\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback({\n        \"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\": ()=>{\n            if (context.viewport) {\n                const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n                const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n                setVisible(isHorizontal ? isOverflowX : isOverflowY);\n            }\n        }\n    }[\"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\"], 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarX.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarX.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarY.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarY.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\": (node)=>setScrollbar(node)\n    }[\"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\"]);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarImpl.useEffect\": ()=>{\n            const handleWheel = {\n                \"ScrollAreaScrollbarImpl.useEffect.handleWheel\": (event)=>{\n                    const element = event.target;\n                    const isScrollbarWheel = scrollbar?.contains(element);\n                    if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n                }\n            }[\"ScrollAreaScrollbarImpl.useEffect.handleWheel\"];\n            document.addEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n            return ({\n                \"ScrollAreaScrollbarImpl.useEffect\": ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                        passive: false\n                    })\n            })[\"ScrollAreaScrollbarImpl.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbarImpl.useEffect\"], [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaThumbImpl.useComposedRefs[composedRef]\": (node)=>scrollbarContext.onThumbChange(node)\n    }[\"ScrollAreaThumbImpl.useComposedRefs[composedRef]\"]);\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\": ()=>{\n            if (removeUnlinkedScrollListenerRef.current) {\n                removeUnlinkedScrollListenerRef.current();\n                removeUnlinkedScrollListenerRef.current = void 0;\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\"], 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaThumbImpl.useEffect\": ()=>{\n            const viewport = scrollAreaContext.viewport;\n            if (viewport) {\n                const handleScroll = {\n                    \"ScrollAreaThumbImpl.useEffect.handleScroll\": ()=>{\n                        debounceScrollEnd();\n                        if (!removeUnlinkedScrollListenerRef.current) {\n                            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                            removeUnlinkedScrollListenerRef.current = listener;\n                            onThumbPositionChange();\n                        }\n                    }\n                }[\"ScrollAreaThumbImpl.useEffect.handleScroll\"];\n                onThumbPositionChange();\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaThumbImpl.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaThumbImpl.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useEffect\"], [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const height2 = context.scrollbarX?.offsetHeight || 0;\n            context.onCornerHeightChange(height2);\n            setHeight(height2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    useResizeObserver(context.scrollbarY, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const width2 = context.scrollbarY?.offsetWidth || 0;\n            context.onCornerWidthChange(width2);\n            setWidth(width2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useDebounceCallback.useEffect\": ()=>({\n                \"useDebounceCallback.useEffect\": ()=>window.clearTimeout(debounceTimerRef.current)\n            })[\"useDebounceCallback.useEffect\"]\n    }[\"useDebounceCallback.useEffect\"], []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useDebounceCallback.useCallback\": ()=>{\n            window.clearTimeout(debounceTimerRef.current);\n            debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n        }\n    }[\"useDebounceCallback.useCallback\"], [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)({\n        \"useResizeObserver.useLayoutEffect\": ()=>{\n            let rAF = 0;\n            if (element) {\n                const resizeObserver = new ResizeObserver({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        cancelAnimationFrame(rAF);\n                        rAF = window.requestAnimationFrame(handleResize);\n                    }\n                }[\"useResizeObserver.useLayoutEffect\"]);\n                resizeObserver.observe(element);\n                return ({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        window.cancelAnimationFrame(rAF);\n                        resizeObserver.unobserve(element);\n                    }\n                })[\"useResizeObserver.useLayoutEffect\"];\n            }\n        }\n    }[\"useResizeObserver.useLayoutEffect\"], [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcY29kZVxcY2hhdGRvYy12MVxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1jYWxsYmFjay1yZWZcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2UtY2FsbGJhY2stcmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsOENBQThDLGtEQUFxQjtBQUNuRTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxjb2RlXFxjaGF0ZG9jLXYxXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdXNlLWxheW91dC1lZmZlY3RcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtbGF5b3V0LWVmZmVjdC9zcmMvdXNlLWxheW91dC1lZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ })

};
;