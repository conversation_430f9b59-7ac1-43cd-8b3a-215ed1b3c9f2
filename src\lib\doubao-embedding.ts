interface DoubaoEmbeddingConfig {
  apiKey: string;
  baseURL: string;
  model: string;
}

interface EmbeddingResponse {
  data: Array<{
    embedding: number[];
    index: number;
    object: string;
  }>;
  model: string;
  object: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

export class DoubaoEmbeddingService {
  private config: DoubaoEmbeddingConfig;

  constructor(config: DoubaoEmbeddingConfig) {
    this.config = config;
  }

  async getEmbedding(text: string): Promise<number[]> {
    try {
      const response = await fetch(`${this.config.baseURL}/embeddings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          model: this.config.model,
          input: text,
          encoding_format: 'float',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Doubao Embedding API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data: EmbeddingResponse = await response.json();
      
      if (!data.data || data.data.length === 0) {
        throw new Error('No embedding data returned from Doubao API');
      }

      return data.data[0].embedding;
    } catch (error) {
      console.error('Doubao embedding error:', error);
      throw error;
    }
  }

  async getEmbeddings(texts: string[]): Promise<number[][]> {
    try {
      const response = await fetch(`${this.config.baseURL}/embeddings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          model: this.config.model,
          input: texts,
          encoding_format: 'float',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Doubao Embedding API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data: EmbeddingResponse = await response.json();
      
      if (!data.data || data.data.length === 0) {
        throw new Error('No embedding data returned from Doubao API');
      }

      // Sort by index to maintain order
      return data.data
        .sort((a, b) => a.index - b.index)
        .map(item => item.embedding);
    } catch (error) {
      console.error('Doubao embeddings error:', error);
      throw error;
    }
  }

  async calculateSimilarity(embedding1: number[], embedding2: number[]): Promise<number> {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimension');
    }

    // Calculate cosine similarity
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  async findMostSimilar(
    queryEmbedding: number[], 
    documentEmbeddings: Array<{ embedding: number[]; metadata: any }>,
    topK: number = 5
  ): Promise<Array<{ similarity: number; metadata: any }>> {
    const similarities = await Promise.all(
      documentEmbeddings.map(async (doc) => ({
        similarity: await this.calculateSimilarity(queryEmbedding, doc.embedding),
        metadata: doc.metadata,
      }))
    );

    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK);
  }
}
