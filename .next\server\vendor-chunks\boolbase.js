/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/boolbase";
exports.ids = ["vendor-chunks/boolbase"];
exports.modules = {

/***/ "(rsc)/./node_modules/boolbase/index.js":
/*!****************************************!*\
  !*** ./node_modules/boolbase/index.js ***!
  \****************************************/
/***/ ((module) => {

eval("module.exports = {\n\ttrueFunc: function trueFunc(){\n\t\treturn true;\n\t},\n\tfalseFunc: function falseFunc(){\n\t\treturn false;\n\t}\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYm9vbGJhc2UvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcY29kZVxcY2hhdGRvYy12MVxcbm9kZV9tb2R1bGVzXFxib29sYmFzZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSB7XG5cdHRydWVGdW5jOiBmdW5jdGlvbiB0cnVlRnVuYygpe1xuXHRcdHJldHVybiB0cnVlO1xuXHR9LFxuXHRmYWxzZUZ1bmM6IGZ1bmN0aW9uIGZhbHNlRnVuYygpe1xuXHRcdHJldHVybiBmYWxzZTtcblx0fVxufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/boolbase/index.js\n");

/***/ })

};
;