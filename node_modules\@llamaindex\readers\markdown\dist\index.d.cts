import { FileReader, Document } from '@llamaindex/core/schema';

type MarkdownTuple = [string | null, string];
/**
 * Extract text from markdown files.
 * Returns dictionary with keys as headers and values as the text between headers.
 */
declare class MarkdownReader extends FileReader<Document> {
    private _removeHyperlinks;
    private _removeImages;
    /**
     * @param {boolean} [removeHyperlinks=true] - Indicates whether hyperlinks should be removed.
     * @param {boolean} [removeImages=true] - Indicates whether images should be removed.
     */
    constructor(removeHyperlinks?: boolean, removeImages?: boolean);
    /**
     * Convert a markdown file to a dictionary.
     * The keys are the headers and the values are the text under each header.
     * @param {string} markdownText - The markdown text to convert.
     * @returns {Array<MarkdownTuple>} - An array of tuples, where each tuple contains a header (or null) and its corresponding text.
     */
    markdownToTups(markdownText: string): MarkdownTuple[];
    removeImages(content: string): string;
    removeHyperlinks(content: string): string;
    parseTups(content: string): MarkdownTuple[];
    loadDataAsContent(fileContent: Uint8Array): Promise<Document[]>;
}

export { MarkdownReader };
