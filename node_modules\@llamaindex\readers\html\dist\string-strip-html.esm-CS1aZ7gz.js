function getDefaultExportFromCjs (x) {
	return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}

var rfdc_1;
var hasRequiredRfdc;

function requireRfdc () {
	if (hasRequiredRfdc) return rfdc_1;
	hasRequiredRfdc = 1;
	rfdc_1 = rfdc;
	function copyBuffer(cur) {
	    if (cur instanceof Buffer) {
	        return Buffer.from(cur);
	    }
	    return new cur.constructor(cur.buffer.slice(), cur.byteOffset, cur.length);
	}
	function rfdc(opts) {
	    opts = opts || {};
	    if (opts.circles) return rfdcCircles(opts);
	    const constructorHandlers = new Map();
	    constructorHandlers.set(Date, (o)=>new Date(o));
	    constructorHandlers.set(Map, (o, fn)=>new Map(cloneArray(Array.from(o), fn)));
	    constructorHandlers.set(Set, (o, fn)=>new Set(cloneArray(Array.from(o), fn)));
	    if (opts.constructorHandlers) {
	        for (const handler of opts.constructorHandlers){
	            constructorHandlers.set(handler[0], handler[1]);
	        }
	    }
	    let handler = null;
	    return opts.proto ? cloneProto : clone;
	    function cloneArray(a, fn) {
	        const keys = Object.keys(a);
	        const a2 = new Array(keys.length);
	        for(let i = 0; i < keys.length; i++){
	            const k = keys[i];
	            const cur = a[k];
	            if (typeof cur !== 'object' || cur === null) {
	                a2[k] = cur;
	            } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {
	                a2[k] = handler(cur, fn);
	            } else if (ArrayBuffer.isView(cur)) {
	                a2[k] = copyBuffer(cur);
	            } else {
	                a2[k] = fn(cur);
	            }
	        }
	        return a2;
	    }
	    function clone(o) {
	        if (typeof o !== 'object' || o === null) return o;
	        if (Array.isArray(o)) return cloneArray(o, clone);
	        if (o.constructor !== Object && (handler = constructorHandlers.get(o.constructor))) {
	            return handler(o, clone);
	        }
	        const o2 = {};
	        for(const k in o){
	            if (Object.hasOwnProperty.call(o, k) === false) continue;
	            const cur = o[k];
	            if (typeof cur !== 'object' || cur === null) {
	                o2[k] = cur;
	            } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {
	                o2[k] = handler(cur, clone);
	            } else if (ArrayBuffer.isView(cur)) {
	                o2[k] = copyBuffer(cur);
	            } else {
	                o2[k] = clone(cur);
	            }
	        }
	        return o2;
	    }
	    function cloneProto(o) {
	        if (typeof o !== 'object' || o === null) return o;
	        if (Array.isArray(o)) return cloneArray(o, cloneProto);
	        if (o.constructor !== Object && (handler = constructorHandlers.get(o.constructor))) {
	            return handler(o, cloneProto);
	        }
	        const o2 = {};
	        for(const k in o){
	            const cur = o[k];
	            if (typeof cur !== 'object' || cur === null) {
	                o2[k] = cur;
	            } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {
	                o2[k] = handler(cur, cloneProto);
	            } else if (ArrayBuffer.isView(cur)) {
	                o2[k] = copyBuffer(cur);
	            } else {
	                o2[k] = cloneProto(cur);
	            }
	        }
	        return o2;
	    }
	}
	function rfdcCircles(opts) {
	    const refs = [];
	    const refsNew = [];
	    const constructorHandlers = new Map();
	    constructorHandlers.set(Date, (o)=>new Date(o));
	    constructorHandlers.set(Map, (o, fn)=>new Map(cloneArray(Array.from(o), fn)));
	    constructorHandlers.set(Set, (o, fn)=>new Set(cloneArray(Array.from(o), fn)));
	    if (opts.constructorHandlers) {
	        for (const handler of opts.constructorHandlers){
	            constructorHandlers.set(handler[0], handler[1]);
	        }
	    }
	    let handler = null;
	    return opts.proto ? cloneProto : clone;
	    function cloneArray(a, fn) {
	        const keys = Object.keys(a);
	        const a2 = new Array(keys.length);
	        for(let i = 0; i < keys.length; i++){
	            const k = keys[i];
	            const cur = a[k];
	            if (typeof cur !== 'object' || cur === null) {
	                a2[k] = cur;
	            } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {
	                a2[k] = handler(cur, fn);
	            } else if (ArrayBuffer.isView(cur)) {
	                a2[k] = copyBuffer(cur);
	            } else {
	                const index = refs.indexOf(cur);
	                if (index !== -1) {
	                    a2[k] = refsNew[index];
	                } else {
	                    a2[k] = fn(cur);
	                }
	            }
	        }
	        return a2;
	    }
	    function clone(o) {
	        if (typeof o !== 'object' || o === null) return o;
	        if (Array.isArray(o)) return cloneArray(o, clone);
	        if (o.constructor !== Object && (handler = constructorHandlers.get(o.constructor))) {
	            return handler(o, clone);
	        }
	        const o2 = {};
	        refs.push(o);
	        refsNew.push(o2);
	        for(const k in o){
	            if (Object.hasOwnProperty.call(o, k) === false) continue;
	            const cur = o[k];
	            if (typeof cur !== 'object' || cur === null) {
	                o2[k] = cur;
	            } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {
	                o2[k] = handler(cur, clone);
	            } else if (ArrayBuffer.isView(cur)) {
	                o2[k] = copyBuffer(cur);
	            } else {
	                const i = refs.indexOf(cur);
	                if (i !== -1) {
	                    o2[k] = refsNew[i];
	                } else {
	                    o2[k] = clone(cur);
	                }
	            }
	        }
	        refs.pop();
	        refsNew.pop();
	        return o2;
	    }
	    function cloneProto(o) {
	        if (typeof o !== 'object' || o === null) return o;
	        if (Array.isArray(o)) return cloneArray(o, cloneProto);
	        if (o.constructor !== Object && (handler = constructorHandlers.get(o.constructor))) {
	            return handler(o, cloneProto);
	        }
	        const o2 = {};
	        refs.push(o);
	        refsNew.push(o2);
	        for(const k in o){
	            const cur = o[k];
	            if (typeof cur !== 'object' || cur === null) {
	                o2[k] = cur;
	            } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {
	                o2[k] = handler(cur, cloneProto);
	            } else if (ArrayBuffer.isView(cur)) {
	                o2[k] = copyBuffer(cur);
	            } else {
	                const i = refs.indexOf(cur);
	                if (i !== -1) {
	                    o2[k] = refsNew[i];
	                } else {
	                    o2[k] = cloneProto(cur);
	                }
	            }
	        }
	        refs.pop();
	        refsNew.pop();
	        return o2;
	    }
	}
	return rfdc_1;
}

var rfdcExports = requireRfdc();
var I = /*@__PURE__*/getDefaultExportFromCjs(rfdcExports);

I();
function u(t) {
    if (t == null || typeof t != "object") return false;
    let e = Object.getPrototypeOf(t);
    return e !== null && e !== Object.prototype && Object.getPrototypeOf(e) !== null ? false : !(Symbol.iterator in t) && !(Symbol.toStringTag in t);
}
function r(t) {
    return typeof t == "string";
}
function q$1(t) {
    return Number.isSafeInteger(t) && t >= 0;
}
function G$1(t) {
    return t != null;
}
function v(t, e) {
    return u(t) && r(e) && e in t;
}

/** Detect free variable `global` from Node.js. */ var freeGlobal = typeof global == 'object' && global && global.Object === Object && global;

/** Detect free variable `self`. */ var freeSelf = typeof self == 'object' && self && self.Object === Object && self;
/** Used as a reference to the global object. */ var root = freeGlobal || freeSelf || Function('return this')();

/** Built-in value references. */ var Symbol$1 = root.Symbol;

/** Used for built-in method references. */ var objectProto$4 = Object.prototype;
/** Used to check objects for own properties. */ var hasOwnProperty$3 = objectProto$4.hasOwnProperty;
/**
 * Used to resolve the
 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
 * of values.
 */ var nativeObjectToString$1 = objectProto$4.toString;
/** Built-in value references. */ var symToStringTag$1 = Symbol$1 ? Symbol$1.toStringTag : undefined;
/**
 * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.
 *
 * @private
 * @param {*} value The value to query.
 * @returns {string} Returns the raw `toStringTag`.
 */ function getRawTag(value) {
    var isOwn = hasOwnProperty$3.call(value, symToStringTag$1), tag = value[symToStringTag$1];
    try {
        value[symToStringTag$1] = undefined;
        var unmasked = true;
    } catch (e) {}
    var result = nativeObjectToString$1.call(value);
    if (unmasked) {
        if (isOwn) {
            value[symToStringTag$1] = tag;
        } else {
            delete value[symToStringTag$1];
        }
    }
    return result;
}

/** Used for built-in method references. */ var objectProto$3 = Object.prototype;
/**
 * Used to resolve the
 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
 * of values.
 */ var nativeObjectToString = objectProto$3.toString;
/**
 * Converts `value` to a string using `Object.prototype.toString`.
 *
 * @private
 * @param {*} value The value to convert.
 * @returns {string} Returns the converted string.
 */ function objectToString(value) {
    return nativeObjectToString.call(value);
}

/** `Object#toString` result references. */ var nullTag = '[object Null]', undefinedTag = '[object Undefined]';
/** Built-in value references. */ var symToStringTag = Symbol$1 ? Symbol$1.toStringTag : undefined;
/**
 * The base implementation of `getTag` without fallbacks for buggy environments.
 *
 * @private
 * @param {*} value The value to query.
 * @returns {string} Returns the `toStringTag`.
 */ function baseGetTag(value) {
    if (value == null) {
        return value === undefined ? undefinedTag : nullTag;
    }
    return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);
}

/**
 * Checks if `value` is object-like. A value is object-like if it's not `null`
 * and has a `typeof` result of "object".
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
 * @example
 *
 * _.isObjectLike({});
 * // => true
 *
 * _.isObjectLike([1, 2, 3]);
 * // => true
 *
 * _.isObjectLike(_.noop);
 * // => false
 *
 * _.isObjectLike(null);
 * // => false
 */ function isObjectLike(value) {
    return value != null && typeof value == 'object';
}

/** `Object#toString` result references. */ var symbolTag = '[object Symbol]';
/**
 * Checks if `value` is classified as a `Symbol` primitive or object.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
 * @example
 *
 * _.isSymbol(Symbol.iterator);
 * // => true
 *
 * _.isSymbol('abc');
 * // => false
 */ function isSymbol(value) {
    return typeof value == 'symbol' || isObjectLike(value) && baseGetTag(value) == symbolTag;
}

/**
 * A specialized version of `_.map` for arrays without support for iteratee
 * shorthands.
 *
 * @private
 * @param {Array} [array] The array to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @returns {Array} Returns the new mapped array.
 */ function arrayMap(array, iteratee) {
    var index = -1, length = array == null ? 0 : array.length, result = Array(length);
    while(++index < length){
        result[index] = iteratee(array[index], index, array);
    }
    return result;
}

/**
 * Checks if `value` is classified as an `Array` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an array, else `false`.
 * @example
 *
 * _.isArray([1, 2, 3]);
 * // => true
 *
 * _.isArray(document.body.children);
 * // => false
 *
 * _.isArray('abc');
 * // => false
 *
 * _.isArray(_.noop);
 * // => false
 */ var isArray = Array.isArray;

/** Used to convert symbols to primitives and strings. */ var symbolProto = Symbol$1 ? Symbol$1.prototype : undefined, symbolToString = symbolProto ? symbolProto.toString : undefined;
/**
 * The base implementation of `_.toString` which doesn't convert nullish
 * values to empty strings.
 *
 * @private
 * @param {*} value The value to process.
 * @returns {string} Returns the string.
 */ function baseToString(value) {
    // Exit early for strings to avoid a performance hit in some environments.
    if (typeof value == 'string') {
        return value;
    }
    if (isArray(value)) {
        // Recursively convert values (susceptible to call stack limits).
        return arrayMap(value, baseToString) + '';
    }
    if (isSymbol(value)) {
        return symbolToString ? symbolToString.call(value) : '';
    }
    var result = value + '';
    return result == '0' && 1 / value == -Infinity ? '-0' : result;
}

/** Used to match a single whitespace character. */ var reWhitespace = /\s/;
/**
 * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace
 * character of `string`.
 *
 * @private
 * @param {string} string The string to inspect.
 * @returns {number} Returns the index of the last non-whitespace character.
 */ function trimmedEndIndex(string) {
    var index = string.length;
    while(index-- && reWhitespace.test(string.charAt(index))){}
    return index;
}

/** Used to match leading whitespace. */ var reTrimStart = /^\s+/;
/**
 * The base implementation of `_.trim`.
 *
 * @private
 * @param {string} string The string to trim.
 * @returns {string} Returns the trimmed string.
 */ function baseTrim(string) {
    return string ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '') : string;
}

/**
 * Checks if `value` is the
 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
 * @example
 *
 * _.isObject({});
 * // => true
 *
 * _.isObject([1, 2, 3]);
 * // => true
 *
 * _.isObject(_.noop);
 * // => true
 *
 * _.isObject(null);
 * // => false
 */ function isObject(value) {
    var type = typeof value;
    return value != null && (type == 'object' || type == 'function');
}

/**
 * This method returns the first argument it receives.
 *
 * @static
 * @since 0.1.0
 * @memberOf _
 * @category Util
 * @param {*} value Any value.
 * @returns {*} Returns `value`.
 * @example
 *
 * var object = { 'a': 1 };
 *
 * console.log(_.identity(object) === object);
 * // => true
 */ function identity(value) {
    return value;
}

/** `Object#toString` result references. */ var asyncTag = '[object AsyncFunction]', funcTag = '[object Function]', genTag = '[object GeneratorFunction]', proxyTag = '[object Proxy]';
/**
 * Checks if `value` is classified as a `Function` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a function, else `false`.
 * @example
 *
 * _.isFunction(_);
 * // => true
 *
 * _.isFunction(/abc/);
 * // => false
 */ function isFunction(value) {
    if (!isObject(value)) {
        return false;
    }
    // The use of `Object#toString` avoids issues with the `typeof` operator
    // in Safari 9 which returns 'object' for typed arrays and other constructors.
    var tag = baseGetTag(value);
    return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;
}

/** Used to detect overreaching core-js shims. */ var coreJsData = root['__core-js_shared__'];

/** Used to detect methods masquerading as native. */ var maskSrcKey = function() {
    var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');
    return uid ? 'Symbol(src)_1.' + uid : '';
}();
/**
 * Checks if `func` has its source masked.
 *
 * @private
 * @param {Function} func The function to check.
 * @returns {boolean} Returns `true` if `func` is masked, else `false`.
 */ function isMasked(func) {
    return !!maskSrcKey && maskSrcKey in func;
}

/** Used for built-in method references. */ var funcProto$1 = Function.prototype;
/** Used to resolve the decompiled source of functions. */ var funcToString$1 = funcProto$1.toString;
/**
 * Converts `func` to its source code.
 *
 * @private
 * @param {Function} func The function to convert.
 * @returns {string} Returns the source code.
 */ function toSource(func) {
    if (func != null) {
        try {
            return funcToString$1.call(func);
        } catch (e) {}
        try {
            return func + '';
        } catch (e) {}
    }
    return '';
}

/**
 * Used to match `RegExp`
 * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).
 */ var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;
/** Used to detect host constructors (Safari). */ var reIsHostCtor = /^\[object .+?Constructor\]$/;
/** Used for built-in method references. */ var funcProto = Function.prototype, objectProto$2 = Object.prototype;
/** Used to resolve the decompiled source of functions. */ var funcToString = funcProto.toString;
/** Used to check objects for own properties. */ var hasOwnProperty$2 = objectProto$2.hasOwnProperty;
/** Used to detect if a method is native. */ var reIsNative = RegExp('^' + funcToString.call(hasOwnProperty$2).replace(reRegExpChar, '\\$&').replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, '$1.*?') + '$');
/**
 * The base implementation of `_.isNative` without bad shim checks.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a native function,
 *  else `false`.
 */ function baseIsNative(value) {
    if (!isObject(value) || isMasked(value)) {
        return false;
    }
    var pattern = isFunction(value) ? reIsNative : reIsHostCtor;
    return pattern.test(toSource(value));
}

/**
 * Gets the value at `key` of `object`.
 *
 * @private
 * @param {Object} [object] The object to query.
 * @param {string} key The key of the property to get.
 * @returns {*} Returns the property value.
 */ function getValue(object, key) {
    return object == null ? undefined : object[key];
}

/**
 * Gets the native function at `key` of `object`.
 *
 * @private
 * @param {Object} object The object to query.
 * @param {string} key The key of the method to get.
 * @returns {*} Returns the function if it's native, else `undefined`.
 */ function getNative(object, key) {
    var value = getValue(object, key);
    return baseIsNative(value) ? value : undefined;
}

/**
 * A faster alternative to `Function#apply`, this function invokes `func`
 * with the `this` binding of `thisArg` and the arguments of `args`.
 *
 * @private
 * @param {Function} func The function to invoke.
 * @param {*} thisArg The `this` binding of `func`.
 * @param {Array} args The arguments to invoke `func` with.
 * @returns {*} Returns the result of `func`.
 */ function apply(func, thisArg, args) {
    switch(args.length){
        case 0:
            return func.call(thisArg);
        case 1:
            return func.call(thisArg, args[0]);
        case 2:
            return func.call(thisArg, args[0], args[1]);
        case 3:
            return func.call(thisArg, args[0], args[1], args[2]);
    }
    return func.apply(thisArg, args);
}

/** Used to detect hot functions by number of calls within a span of milliseconds. */ var HOT_COUNT = 800, HOT_SPAN = 16;
/* Built-in method references for those with the same name as other `lodash` methods. */ var nativeNow = Date.now;
/**
 * Creates a function that'll short out and invoke `identity` instead
 * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`
 * milliseconds.
 *
 * @private
 * @param {Function} func The function to restrict.
 * @returns {Function} Returns the new shortable function.
 */ function shortOut(func) {
    var count = 0, lastCalled = 0;
    return function() {
        var stamp = nativeNow(), remaining = HOT_SPAN - (stamp - lastCalled);
        lastCalled = stamp;
        if (remaining > 0) {
            if (++count >= HOT_COUNT) {
                return arguments[0];
            }
        } else {
            count = 0;
        }
        return func.apply(undefined, arguments);
    };
}

/**
 * Creates a function that returns `value`.
 *
 * @static
 * @memberOf _
 * @since 2.4.0
 * @category Util
 * @param {*} value The value to return from the new function.
 * @returns {Function} Returns the new constant function.
 * @example
 *
 * var objects = _.times(2, _.constant({ 'a': 1 }));
 *
 * console.log(objects);
 * // => [{ 'a': 1 }, { 'a': 1 }]
 *
 * console.log(objects[0] === objects[1]);
 * // => true
 */ function constant(value) {
    return function() {
        return value;
    };
}

var defineProperty = function() {
    try {
        var func = getNative(Object, 'defineProperty');
        func({}, '', {});
        return func;
    } catch (e) {}
}();

/**
 * The base implementation of `setToString` without support for hot loop shorting.
 *
 * @private
 * @param {Function} func The function to modify.
 * @param {Function} string The `toString` result.
 * @returns {Function} Returns `func`.
 */ var baseSetToString = !defineProperty ? identity : function(func, string) {
    return defineProperty(func, 'toString', {
        'configurable': true,
        'enumerable': false,
        'value': constant(string),
        'writable': true
    });
};

/**
 * Sets the `toString` method of `func` to return `string`.
 *
 * @private
 * @param {Function} func The function to modify.
 * @param {Function} string The `toString` result.
 * @returns {Function} Returns `func`.
 */ var setToString = shortOut(baseSetToString);

/**
 * The base implementation of `_.findIndex` and `_.findLastIndex` without
 * support for iteratee shorthands.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {Function} predicate The function invoked per iteration.
 * @param {number} fromIndex The index to search from.
 * @param {boolean} [fromRight] Specify iterating from right to left.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */ function baseFindIndex(array, predicate, fromIndex, fromRight) {
    var length = array.length, index = fromIndex + (-1);
    while(++index < length){
        if (predicate(array[index], index, array)) {
            return index;
        }
    }
    return -1;
}

/**
 * The base implementation of `_.isNaN` without support for number objects.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.
 */ function baseIsNaN(value) {
    return value !== value;
}

/**
 * A specialized version of `_.indexOf` which performs strict equality
 * comparisons of values, i.e. `===`.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {*} value The value to search for.
 * @param {number} fromIndex The index to search from.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */ function strictIndexOf(array, value, fromIndex) {
    var index = fromIndex - 1, length = array.length;
    while(++index < length){
        if (array[index] === value) {
            return index;
        }
    }
    return -1;
}

/**
 * The base implementation of `_.indexOf` without `fromIndex` bounds checks.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {*} value The value to search for.
 * @param {number} fromIndex The index to search from.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */ function baseIndexOf(array, value, fromIndex) {
    return value === value ? strictIndexOf(array, value, fromIndex) : baseFindIndex(array, baseIsNaN, fromIndex);
}

/**
 * A specialized version of `_.includes` for arrays without support for
 * specifying an index to search from.
 *
 * @private
 * @param {Array} [array] The array to inspect.
 * @param {*} target The value to search for.
 * @returns {boolean} Returns `true` if `target` is found, else `false`.
 */ function arrayIncludes(array, value) {
    var length = array == null ? 0 : array.length;
    return !!length && baseIndexOf(array, value, 0) > -1;
}

/**
 * Performs a
 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * comparison between two values to determine if they are equivalent.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 * @example
 *
 * var object = { 'a': 1 };
 * var other = { 'a': 1 };
 *
 * _.eq(object, object);
 * // => true
 *
 * _.eq(object, other);
 * // => false
 *
 * _.eq('a', 'a');
 * // => true
 *
 * _.eq('a', Object('a'));
 * // => false
 *
 * _.eq(NaN, NaN);
 * // => true
 */ function eq(value, other) {
    return value === other || value !== value && other !== other;
}

/* Built-in method references for those with the same name as other `lodash` methods. */ var nativeMax = Math.max;
/**
 * A specialized version of `baseRest` which transforms the rest array.
 *
 * @private
 * @param {Function} func The function to apply a rest parameter to.
 * @param {number} [start=func.length-1] The start position of the rest parameter.
 * @param {Function} transform The rest array transform.
 * @returns {Function} Returns the new function.
 */ function overRest(func, start, transform) {
    start = nativeMax(start === undefined ? func.length - 1 : start, 0);
    return function() {
        var args = arguments, index = -1, length = nativeMax(args.length - start, 0), array = Array(length);
        while(++index < length){
            array[index] = args[start + index];
        }
        index = -1;
        var otherArgs = Array(start + 1);
        while(++index < start){
            otherArgs[index] = args[index];
        }
        otherArgs[start] = transform(array);
        return apply(func, this, otherArgs);
    };
}

/**
 * The base implementation of `_.rest` which doesn't validate or coerce arguments.
 *
 * @private
 * @param {Function} func The function to apply a rest parameter to.
 * @param {number} [start=func.length-1] The start position of the rest parameter.
 * @returns {Function} Returns the new function.
 */ function baseRest(func, start) {
    return setToString(overRest(func, start, identity), func + '');
}

/** Used as references for various `Number` constants. */ var MAX_SAFE_INTEGER = 9007199254740991;
/**
 * Checks if `value` is a valid array-like length.
 *
 * **Note:** This method is loosely based on
 * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.
 * @example
 *
 * _.isLength(3);
 * // => true
 *
 * _.isLength(Number.MIN_VALUE);
 * // => false
 *
 * _.isLength(Infinity);
 * // => false
 *
 * _.isLength('3');
 * // => false
 */ function isLength(value) {
    return typeof value == 'number' && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;
}

/**
 * Checks if `value` is array-like. A value is considered array-like if it's
 * not a function and has a `value.length` that's an integer greater than or
 * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is array-like, else `false`.
 * @example
 *
 * _.isArrayLike([1, 2, 3]);
 * // => true
 *
 * _.isArrayLike(document.body.children);
 * // => true
 *
 * _.isArrayLike('abc');
 * // => true
 *
 * _.isArrayLike(_.noop);
 * // => false
 */ function isArrayLike(value) {
    return value != null && isLength(value.length) && !isFunction(value);
}

/* Built-in method references that are verified to be native. */ var nativeCreate = getNative(Object, 'create');

/**
 * Removes all key-value entries from the hash.
 *
 * @private
 * @name clear
 * @memberOf Hash
 */ function hashClear() {
    this.__data__ = nativeCreate ? nativeCreate(null) : {};
    this.size = 0;
}

/**
 * Removes `key` and its value from the hash.
 *
 * @private
 * @name delete
 * @memberOf Hash
 * @param {Object} hash The hash to modify.
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */ function hashDelete(key) {
    var result = this.has(key) && delete this.__data__[key];
    this.size -= result ? 1 : 0;
    return result;
}

/** Used to stand-in for `undefined` hash values. */ var HASH_UNDEFINED$2 = '__lodash_hash_undefined__';
/** Used for built-in method references. */ var objectProto$1 = Object.prototype;
/** Used to check objects for own properties. */ var hasOwnProperty$1 = objectProto$1.hasOwnProperty;
/**
 * Gets the hash value for `key`.
 *
 * @private
 * @name get
 * @memberOf Hash
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */ function hashGet(key) {
    var data = this.__data__;
    if (nativeCreate) {
        var result = data[key];
        return result === HASH_UNDEFINED$2 ? undefined : result;
    }
    return hasOwnProperty$1.call(data, key) ? data[key] : undefined;
}

/** Used for built-in method references. */ var objectProto = Object.prototype;
/** Used to check objects for own properties. */ var hasOwnProperty = objectProto.hasOwnProperty;
/**
 * Checks if a hash value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf Hash
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */ function hashHas(key) {
    var data = this.__data__;
    return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);
}

/** Used to stand-in for `undefined` hash values. */ var HASH_UNDEFINED$1 = '__lodash_hash_undefined__';
/**
 * Sets the hash `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf Hash
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the hash instance.
 */ function hashSet(key, value) {
    var data = this.__data__;
    this.size += this.has(key) ? 0 : 1;
    data[key] = nativeCreate && value === undefined ? HASH_UNDEFINED$1 : value;
    return this;
}

/**
 * Creates a hash object.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */ function Hash(entries) {
    var index = -1, length = entries == null ? 0 : entries.length;
    this.clear();
    while(++index < length){
        var entry = entries[index];
        this.set(entry[0], entry[1]);
    }
}
// Add methods to `Hash`.
Hash.prototype.clear = hashClear;
Hash.prototype['delete'] = hashDelete;
Hash.prototype.get = hashGet;
Hash.prototype.has = hashHas;
Hash.prototype.set = hashSet;

/**
 * Removes all key-value entries from the list cache.
 *
 * @private
 * @name clear
 * @memberOf ListCache
 */ function listCacheClear() {
    this.__data__ = [];
    this.size = 0;
}

/**
 * Gets the index at which the `key` is found in `array` of key-value pairs.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {*} key The key to search for.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */ function assocIndexOf(array, key) {
    var length = array.length;
    while(length--){
        if (eq(array[length][0], key)) {
            return length;
        }
    }
    return -1;
}

/** Used for built-in method references. */ var arrayProto = Array.prototype;
/** Built-in value references. */ var splice = arrayProto.splice;
/**
 * Removes `key` and its value from the list cache.
 *
 * @private
 * @name delete
 * @memberOf ListCache
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */ function listCacheDelete(key) {
    var data = this.__data__, index = assocIndexOf(data, key);
    if (index < 0) {
        return false;
    }
    var lastIndex = data.length - 1;
    if (index == lastIndex) {
        data.pop();
    } else {
        splice.call(data, index, 1);
    }
    --this.size;
    return true;
}

/**
 * Gets the list cache value for `key`.
 *
 * @private
 * @name get
 * @memberOf ListCache
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */ function listCacheGet(key) {
    var data = this.__data__, index = assocIndexOf(data, key);
    return index < 0 ? undefined : data[index][1];
}

/**
 * Checks if a list cache value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf ListCache
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */ function listCacheHas(key) {
    return assocIndexOf(this.__data__, key) > -1;
}

/**
 * Sets the list cache `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf ListCache
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the list cache instance.
 */ function listCacheSet(key, value) {
    var data = this.__data__, index = assocIndexOf(data, key);
    if (index < 0) {
        ++this.size;
        data.push([
            key,
            value
        ]);
    } else {
        data[index][1] = value;
    }
    return this;
}

/**
 * Creates an list cache object.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */ function ListCache(entries) {
    var index = -1, length = entries == null ? 0 : entries.length;
    this.clear();
    while(++index < length){
        var entry = entries[index];
        this.set(entry[0], entry[1]);
    }
}
// Add methods to `ListCache`.
ListCache.prototype.clear = listCacheClear;
ListCache.prototype['delete'] = listCacheDelete;
ListCache.prototype.get = listCacheGet;
ListCache.prototype.has = listCacheHas;
ListCache.prototype.set = listCacheSet;

/* Built-in method references that are verified to be native. */ var Map$1 = getNative(root, 'Map');

/**
 * Removes all key-value entries from the map.
 *
 * @private
 * @name clear
 * @memberOf MapCache
 */ function mapCacheClear() {
    this.size = 0;
    this.__data__ = {
        'hash': new Hash,
        'map': new (Map$1 || ListCache),
        'string': new Hash
    };
}

/**
 * Checks if `value` is suitable for use as unique object key.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is suitable, else `false`.
 */ function isKeyable(value) {
    var type = typeof value;
    return type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean' ? value !== '__proto__' : value === null;
}

/**
 * Gets the data for `map`.
 *
 * @private
 * @param {Object} map The map to query.
 * @param {string} key The reference key.
 * @returns {*} Returns the map data.
 */ function getMapData(map, key) {
    var data = map.__data__;
    return isKeyable(key) ? data[typeof key == 'string' ? 'string' : 'hash'] : data.map;
}

/**
 * Removes `key` and its value from the map.
 *
 * @private
 * @name delete
 * @memberOf MapCache
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */ function mapCacheDelete(key) {
    var result = getMapData(this, key)['delete'](key);
    this.size -= result ? 1 : 0;
    return result;
}

/**
 * Gets the map value for `key`.
 *
 * @private
 * @name get
 * @memberOf MapCache
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */ function mapCacheGet(key) {
    return getMapData(this, key).get(key);
}

/**
 * Checks if a map value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf MapCache
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */ function mapCacheHas(key) {
    return getMapData(this, key).has(key);
}

/**
 * Sets the map `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf MapCache
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the map cache instance.
 */ function mapCacheSet(key, value) {
    var data = getMapData(this, key), size = data.size;
    data.set(key, value);
    this.size += data.size == size ? 0 : 1;
    return this;
}

/**
 * Creates a map cache object to store key-value pairs.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */ function MapCache(entries) {
    var index = -1, length = entries == null ? 0 : entries.length;
    this.clear();
    while(++index < length){
        var entry = entries[index];
        this.set(entry[0], entry[1]);
    }
}
// Add methods to `MapCache`.
MapCache.prototype.clear = mapCacheClear;
MapCache.prototype['delete'] = mapCacheDelete;
MapCache.prototype.get = mapCacheGet;
MapCache.prototype.has = mapCacheHas;
MapCache.prototype.set = mapCacheSet;

/**
 * Converts `value` to a string. An empty string is returned for `null`
 * and `undefined` values. The sign of `-0` is preserved.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to convert.
 * @returns {string} Returns the converted string.
 * @example
 *
 * _.toString(null);
 * // => ''
 *
 * _.toString(-0);
 * // => '-0'
 *
 * _.toString([1, 2, 3]);
 * // => '1,2,3'
 */ function toString(value) {
    return value == null ? '' : baseToString(value);
}

/**
 * The base implementation of `_.slice` without an iteratee call guard.
 *
 * @private
 * @param {Array} array The array to slice.
 * @param {number} [start=0] The start position.
 * @param {number} [end=array.length] The end position.
 * @returns {Array} Returns the slice of `array`.
 */ function baseSlice(array, start, end) {
    var index = -1, length = array.length;
    if (start < 0) {
        start = -start > length ? 0 : length + start;
    }
    end = end > length ? length : end;
    if (end < 0) {
        end += length;
    }
    length = start > end ? 0 : end - start >>> 0;
    start >>>= 0;
    var result = Array(length);
    while(++index < length){
        result[index] = array[index + start];
    }
    return result;
}

/**
 * Casts `array` to a slice if it's needed.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {number} start The start position.
 * @param {number} [end=array.length] The end position.
 * @returns {Array} Returns the cast slice.
 */ function castSlice(array, start, end) {
    var length = array.length;
    end = end === undefined ? length : end;
    return !start && end >= length ? array : baseSlice(array, start, end);
}

/** Used to compose unicode character classes. */ var rsAstralRange$1 = '\\ud800-\\udfff', rsComboMarksRange$1 = '\\u0300-\\u036f', reComboHalfMarksRange$1 = '\\ufe20-\\ufe2f', rsComboSymbolsRange$1 = '\\u20d0-\\u20ff', rsComboRange$1 = rsComboMarksRange$1 + reComboHalfMarksRange$1 + rsComboSymbolsRange$1, rsVarRange$1 = '\\ufe0e\\ufe0f';
/** Used to compose unicode capture groups. */ var rsZWJ$1 = '\\u200d';
/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */ var reHasUnicode = RegExp('[' + rsZWJ$1 + rsAstralRange$1 + rsComboRange$1 + rsVarRange$1 + ']');
/**
 * Checks if `string` contains Unicode symbols.
 *
 * @private
 * @param {string} string The string to inspect.
 * @returns {boolean} Returns `true` if a symbol is found, else `false`.
 */ function hasUnicode(string) {
    return reHasUnicode.test(string);
}

/**
 * Converts an ASCII `string` to an array.
 *
 * @private
 * @param {string} string The string to convert.
 * @returns {Array} Returns the converted array.
 */ function asciiToArray(string) {
    return string.split('');
}

/** Used to compose unicode character classes. */ var rsAstralRange = '\\ud800-\\udfff', rsComboMarksRange = '\\u0300-\\u036f', reComboHalfMarksRange = '\\ufe20-\\ufe2f', rsComboSymbolsRange = '\\u20d0-\\u20ff', rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange, rsVarRange = '\\ufe0e\\ufe0f';
/** Used to compose unicode capture groups. */ var rsAstral = '[' + rsAstralRange + ']', rsCombo = '[' + rsComboRange + ']', rsFitz = '\\ud83c[\\udffb-\\udfff]', rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')', rsNonAstral = '[^' + rsAstralRange + ']', rsRegional = '(?:\\ud83c[\\udde6-\\uddff]){2}', rsSurrPair = '[\\ud800-\\udbff][\\udc00-\\udfff]', rsZWJ = '\\u200d';
/** Used to compose unicode regexes. */ var reOptMod = rsModifier + '?', rsOptVar = '[' + rsVarRange + ']?', rsOptJoin = '(?:' + rsZWJ + '(?:' + [
    rsNonAstral,
    rsRegional,
    rsSurrPair
].join('|') + ')' + rsOptVar + reOptMod + ')*', rsSeq = rsOptVar + reOptMod + rsOptJoin, rsSymbol = '(?:' + [
    rsNonAstral + rsCombo + '?',
    rsCombo,
    rsRegional,
    rsSurrPair,
    rsAstral
].join('|') + ')';
/** Used to match [string symbols](https://mathiasbynens.be/notes/javascript-unicode). */ var reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');
/**
 * Converts a Unicode `string` to an array.
 *
 * @private
 * @param {string} string The string to convert.
 * @returns {Array} Returns the converted array.
 */ function unicodeToArray(string) {
    return string.match(reUnicode) || [];
}

/**
 * Converts `string` to an array.
 *
 * @private
 * @param {string} string The string to convert.
 * @returns {Array} Returns the converted array.
 */ function stringToArray(string) {
    return hasUnicode(string) ? unicodeToArray(string) : asciiToArray(string);
}

/** Used to stand-in for `undefined` hash values. */ var HASH_UNDEFINED = '__lodash_hash_undefined__';
/**
 * Adds `value` to the array cache.
 *
 * @private
 * @name add
 * @memberOf SetCache
 * @alias push
 * @param {*} value The value to cache.
 * @returns {Object} Returns the cache instance.
 */ function setCacheAdd(value) {
    this.__data__.set(value, HASH_UNDEFINED);
    return this;
}

/**
 * Checks if `value` is in the array cache.
 *
 * @private
 * @name has
 * @memberOf SetCache
 * @param {*} value The value to search for.
 * @returns {number} Returns `true` if `value` is found, else `false`.
 */ function setCacheHas(value) {
    return this.__data__.has(value);
}

/**
 *
 * Creates an array cache object to store unique values.
 *
 * @private
 * @constructor
 * @param {Array} [values] The values to cache.
 */ function SetCache(values) {
    var index = -1, length = values == null ? 0 : values.length;
    this.__data__ = new MapCache;
    while(++index < length){
        this.add(values[index]);
    }
}
// Add methods to `SetCache`.
SetCache.prototype.add = SetCache.prototype.push = setCacheAdd;
SetCache.prototype.has = setCacheHas;

/**
 * Checks if a `cache` value for `key` exists.
 *
 * @private
 * @param {Object} cache The cache to query.
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */ function cacheHas(cache, key) {
    return cache.has(key);
}

/**
 * This method is like `_.isArrayLike` except that it also checks if `value`
 * is an object.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an array-like object,
 *  else `false`.
 * @example
 *
 * _.isArrayLikeObject([1, 2, 3]);
 * // => true
 *
 * _.isArrayLikeObject(document.body.children);
 * // => true
 *
 * _.isArrayLikeObject('abc');
 * // => false
 *
 * _.isArrayLikeObject(_.noop);
 * // => false
 */ function isArrayLikeObject(value) {
    return isObjectLike(value) && isArrayLike(value);
}

/** Used as the size to enable large array optimizations. */ var LARGE_ARRAY_SIZE = 200;
/**
 * The base implementation of methods like `_.difference` without support
 * for excluding multiple arrays or iteratee shorthands.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {Array} values The values to exclude.
 * @param {Function} [iteratee] The iteratee invoked per element.
 * @param {Function} [comparator] The comparator invoked per element.
 * @returns {Array} Returns the new array of filtered values.
 */ function baseDifference(array, values, iteratee, comparator) {
    var index = -1, includes = arrayIncludes, isCommon = true, length = array.length, result = [], valuesLength = values.length;
    if (!length) {
        return result;
    }
    if (values.length >= LARGE_ARRAY_SIZE) {
        includes = cacheHas;
        isCommon = false;
        values = new SetCache(values);
    }
    outer: while(++index < length){
        var value = array[index], computed = value ;
        value = value !== 0 ? value : 0;
        if (isCommon && computed === computed) {
            var valuesIndex = valuesLength;
            while(valuesIndex--){
                if (values[valuesIndex] === computed) {
                    continue outer;
                }
            }
            result.push(value);
        } else if (!includes(values, computed, comparator)) {
            result.push(value);
        }
    }
    return result;
}

/**
 * Used by `_.trim` and `_.trimEnd` to get the index of the last string symbol
 * that is not found in the character symbols.
 *
 * @private
 * @param {Array} strSymbols The string symbols to inspect.
 * @param {Array} chrSymbols The character symbols to find.
 * @returns {number} Returns the index of the last unmatched string symbol.
 */ function charsEndIndex(strSymbols, chrSymbols) {
    var index = strSymbols.length;
    while(index-- && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1){}
    return index;
}

/**
 * Used by `_.trim` and `_.trimStart` to get the index of the first string symbol
 * that is not found in the character symbols.
 *
 * @private
 * @param {Array} strSymbols The string symbols to inspect.
 * @param {Array} chrSymbols The character symbols to find.
 * @returns {number} Returns the index of the first unmatched string symbol.
 */ function charsStartIndex(strSymbols, chrSymbols) {
    var index = -1, length = strSymbols.length;
    while(++index < length && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1){}
    return index;
}

/**
 * Removes leading and trailing whitespace or specified characters from `string`.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category String
 * @param {string} [string=''] The string to trim.
 * @param {string} [chars=whitespace] The characters to trim.
 * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.
 * @returns {string} Returns the trimmed string.
 * @example
 *
 * _.trim('  abc  ');
 * // => 'abc'
 *
 * _.trim('-_-abc-_-', '_-');
 * // => 'abc'
 *
 * _.map(['  foo  ', '  bar  '], _.trim);
 * // => ['foo', 'bar']
 */ function trim(string, chars, guard) {
    string = toString(string);
    if (string && (chars === undefined)) {
        return baseTrim(string);
    }
    if (!string || !(chars = baseToString(chars))) {
        return string;
    }
    var strSymbols = stringToArray(string), chrSymbols = stringToArray(chars), start = charsStartIndex(strSymbols, chrSymbols), end = charsEndIndex(strSymbols, chrSymbols) + 1;
    return castSlice(strSymbols, start, end).join('');
}

/**
 * Creates an array excluding all given values using
 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * for equality comparisons.
 *
 * **Note:** Unlike `_.pull`, this method returns a new array.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Array
 * @param {Array} array The array to inspect.
 * @param {...*} [values] The values to exclude.
 * @returns {Array} Returns the new array of filtered values.
 * @see _.difference, _.xor
 * @example
 *
 * _.without([2, 1, 2, 3], 1, 2);
 * // => [3]
 */ var without = baseRest(function(array, values) {
    return isArrayLikeObject(array) ? baseDifference(array, values) : [];
});

var lib = {};

var namedReferences = {};

var hasRequiredNamedReferences;

function requireNamedReferences () {
	if (hasRequiredNamedReferences) return namedReferences;
	hasRequiredNamedReferences = 1;
	Object.defineProperty(namedReferences, "__esModule", {
	    value: true
	});
	namedReferences.bodyRegExps = {
	    xml: /&(?:#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+);?/g,
	    html4: /&notin;|&(?:nbsp|iexcl|cent|pound|curren|yen|brvbar|sect|uml|copy|ordf|laquo|not|shy|reg|macr|deg|plusmn|sup2|sup3|acute|micro|para|middot|cedil|sup1|ordm|raquo|frac14|frac12|frac34|iquest|Agrave|Aacute|Acirc|Atilde|Auml|Aring|AElig|Ccedil|Egrave|Eacute|Ecirc|Euml|Igrave|Iacute|Icirc|Iuml|ETH|Ntilde|Ograve|Oacute|Ocirc|Otilde|Ouml|times|Oslash|Ugrave|Uacute|Ucirc|Uuml|Yacute|THORN|szlig|agrave|aacute|acirc|atilde|auml|aring|aelig|ccedil|egrave|eacute|ecirc|euml|igrave|iacute|icirc|iuml|eth|ntilde|ograve|oacute|ocirc|otilde|ouml|divide|oslash|ugrave|uacute|ucirc|uuml|yacute|thorn|yuml|quot|amp|lt|gt|#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+);?/g,
	    html5: /&centerdot;|&copysr;|&divideontimes;|&gtcc;|&gtcir;|&gtdot;|&gtlPar;|&gtquest;|&gtrapprox;|&gtrarr;|&gtrdot;|&gtreqless;|&gtreqqless;|&gtrless;|&gtrsim;|&ltcc;|&ltcir;|&ltdot;|&lthree;|&ltimes;|&ltlarr;|&ltquest;|&ltrPar;|&ltri;|&ltrie;|&ltrif;|&notin;|&notinE;|&notindot;|&notinva;|&notinvb;|&notinvc;|&notni;|&notniva;|&notnivb;|&notnivc;|&parallel;|&timesb;|&timesbar;|&timesd;|&(?:AElig|AMP|Aacute|Acirc|Agrave|Aring|Atilde|Auml|COPY|Ccedil|ETH|Eacute|Ecirc|Egrave|Euml|GT|Iacute|Icirc|Igrave|Iuml|LT|Ntilde|Oacute|Ocirc|Ograve|Oslash|Otilde|Ouml|QUOT|REG|THORN|Uacute|Ucirc|Ugrave|Uuml|Yacute|aacute|acirc|acute|aelig|agrave|amp|aring|atilde|auml|brvbar|ccedil|cedil|cent|copy|curren|deg|divide|eacute|ecirc|egrave|eth|euml|frac12|frac14|frac34|gt|iacute|icirc|iexcl|igrave|iquest|iuml|laquo|lt|macr|micro|middot|nbsp|not|ntilde|oacute|ocirc|ograve|ordf|ordm|oslash|otilde|ouml|para|plusmn|pound|quot|raquo|reg|sect|shy|sup1|sup2|sup3|szlig|thorn|times|uacute|ucirc|ugrave|uml|uuml|yacute|yen|yuml|#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+);?/g
	};
	namedReferences.namedReferences = {
	    xml: {
	        entities: {
	            "&lt;": "<",
	            "&gt;": ">",
	            "&quot;": '"',
	            "&apos;": "'",
	            "&amp;": "&"
	        },
	        characters: {
	            "<": "&lt;",
	            ">": "&gt;",
	            '"': "&quot;",
	            "'": "&apos;",
	            "&": "&amp;"
	        }
	    },
	    html4: {
	        entities: {
	            "&apos;": "'",
	            "&nbsp": " ",
	            "&nbsp;": " ",
	            "&iexcl": "¡",
	            "&iexcl;": "¡",
	            "&cent": "¢",
	            "&cent;": "¢",
	            "&pound": "£",
	            "&pound;": "£",
	            "&curren": "¤",
	            "&curren;": "¤",
	            "&yen": "¥",
	            "&yen;": "¥",
	            "&brvbar": "¦",
	            "&brvbar;": "¦",
	            "&sect": "§",
	            "&sect;": "§",
	            "&uml": "¨",
	            "&uml;": "¨",
	            "&copy": "©",
	            "&copy;": "©",
	            "&ordf": "ª",
	            "&ordf;": "ª",
	            "&laquo": "«",
	            "&laquo;": "«",
	            "&not": "¬",
	            "&not;": "¬",
	            "&shy": "­",
	            "&shy;": "­",
	            "&reg": "®",
	            "&reg;": "®",
	            "&macr": "¯",
	            "&macr;": "¯",
	            "&deg": "°",
	            "&deg;": "°",
	            "&plusmn": "±",
	            "&plusmn;": "±",
	            "&sup2": "²",
	            "&sup2;": "²",
	            "&sup3": "³",
	            "&sup3;": "³",
	            "&acute": "´",
	            "&acute;": "´",
	            "&micro": "µ",
	            "&micro;": "µ",
	            "&para": "¶",
	            "&para;": "¶",
	            "&middot": "·",
	            "&middot;": "·",
	            "&cedil": "¸",
	            "&cedil;": "¸",
	            "&sup1": "¹",
	            "&sup1;": "¹",
	            "&ordm": "º",
	            "&ordm;": "º",
	            "&raquo": "»",
	            "&raquo;": "»",
	            "&frac14": "¼",
	            "&frac14;": "¼",
	            "&frac12": "½",
	            "&frac12;": "½",
	            "&frac34": "¾",
	            "&frac34;": "¾",
	            "&iquest": "¿",
	            "&iquest;": "¿",
	            "&Agrave": "À",
	            "&Agrave;": "À",
	            "&Aacute": "Á",
	            "&Aacute;": "Á",
	            "&Acirc": "Â",
	            "&Acirc;": "Â",
	            "&Atilde": "Ã",
	            "&Atilde;": "Ã",
	            "&Auml": "Ä",
	            "&Auml;": "Ä",
	            "&Aring": "Å",
	            "&Aring;": "Å",
	            "&AElig": "Æ",
	            "&AElig;": "Æ",
	            "&Ccedil": "Ç",
	            "&Ccedil;": "Ç",
	            "&Egrave": "È",
	            "&Egrave;": "È",
	            "&Eacute": "É",
	            "&Eacute;": "É",
	            "&Ecirc": "Ê",
	            "&Ecirc;": "Ê",
	            "&Euml": "Ë",
	            "&Euml;": "Ë",
	            "&Igrave": "Ì",
	            "&Igrave;": "Ì",
	            "&Iacute": "Í",
	            "&Iacute;": "Í",
	            "&Icirc": "Î",
	            "&Icirc;": "Î",
	            "&Iuml": "Ï",
	            "&Iuml;": "Ï",
	            "&ETH": "Ð",
	            "&ETH;": "Ð",
	            "&Ntilde": "Ñ",
	            "&Ntilde;": "Ñ",
	            "&Ograve": "Ò",
	            "&Ograve;": "Ò",
	            "&Oacute": "Ó",
	            "&Oacute;": "Ó",
	            "&Ocirc": "Ô",
	            "&Ocirc;": "Ô",
	            "&Otilde": "Õ",
	            "&Otilde;": "Õ",
	            "&Ouml": "Ö",
	            "&Ouml;": "Ö",
	            "&times": "×",
	            "&times;": "×",
	            "&Oslash": "Ø",
	            "&Oslash;": "Ø",
	            "&Ugrave": "Ù",
	            "&Ugrave;": "Ù",
	            "&Uacute": "Ú",
	            "&Uacute;": "Ú",
	            "&Ucirc": "Û",
	            "&Ucirc;": "Û",
	            "&Uuml": "Ü",
	            "&Uuml;": "Ü",
	            "&Yacute": "Ý",
	            "&Yacute;": "Ý",
	            "&THORN": "Þ",
	            "&THORN;": "Þ",
	            "&szlig": "ß",
	            "&szlig;": "ß",
	            "&agrave": "à",
	            "&agrave;": "à",
	            "&aacute": "á",
	            "&aacute;": "á",
	            "&acirc": "â",
	            "&acirc;": "â",
	            "&atilde": "ã",
	            "&atilde;": "ã",
	            "&auml": "ä",
	            "&auml;": "ä",
	            "&aring": "å",
	            "&aring;": "å",
	            "&aelig": "æ",
	            "&aelig;": "æ",
	            "&ccedil": "ç",
	            "&ccedil;": "ç",
	            "&egrave": "è",
	            "&egrave;": "è",
	            "&eacute": "é",
	            "&eacute;": "é",
	            "&ecirc": "ê",
	            "&ecirc;": "ê",
	            "&euml": "ë",
	            "&euml;": "ë",
	            "&igrave": "ì",
	            "&igrave;": "ì",
	            "&iacute": "í",
	            "&iacute;": "í",
	            "&icirc": "î",
	            "&icirc;": "î",
	            "&iuml": "ï",
	            "&iuml;": "ï",
	            "&eth": "ð",
	            "&eth;": "ð",
	            "&ntilde": "ñ",
	            "&ntilde;": "ñ",
	            "&ograve": "ò",
	            "&ograve;": "ò",
	            "&oacute": "ó",
	            "&oacute;": "ó",
	            "&ocirc": "ô",
	            "&ocirc;": "ô",
	            "&otilde": "õ",
	            "&otilde;": "õ",
	            "&ouml": "ö",
	            "&ouml;": "ö",
	            "&divide": "÷",
	            "&divide;": "÷",
	            "&oslash": "ø",
	            "&oslash;": "ø",
	            "&ugrave": "ù",
	            "&ugrave;": "ù",
	            "&uacute": "ú",
	            "&uacute;": "ú",
	            "&ucirc": "û",
	            "&ucirc;": "û",
	            "&uuml": "ü",
	            "&uuml;": "ü",
	            "&yacute": "ý",
	            "&yacute;": "ý",
	            "&thorn": "þ",
	            "&thorn;": "þ",
	            "&yuml": "ÿ",
	            "&yuml;": "ÿ",
	            "&quot": '"',
	            "&quot;": '"',
	            "&amp": "&",
	            "&amp;": "&",
	            "&lt": "<",
	            "&lt;": "<",
	            "&gt": ">",
	            "&gt;": ">",
	            "&OElig;": "Œ",
	            "&oelig;": "œ",
	            "&Scaron;": "Š",
	            "&scaron;": "š",
	            "&Yuml;": "Ÿ",
	            "&circ;": "ˆ",
	            "&tilde;": "˜",
	            "&ensp;": " ",
	            "&emsp;": " ",
	            "&thinsp;": " ",
	            "&zwnj;": "‌",
	            "&zwj;": "‍",
	            "&lrm;": "‎",
	            "&rlm;": "‏",
	            "&ndash;": "–",
	            "&mdash;": "—",
	            "&lsquo;": "‘",
	            "&rsquo;": "’",
	            "&sbquo;": "‚",
	            "&ldquo;": "“",
	            "&rdquo;": "”",
	            "&bdquo;": "„",
	            "&dagger;": "†",
	            "&Dagger;": "‡",
	            "&permil;": "‰",
	            "&lsaquo;": "‹",
	            "&rsaquo;": "›",
	            "&euro;": "€",
	            "&fnof;": "ƒ",
	            "&Alpha;": "Α",
	            "&Beta;": "Β",
	            "&Gamma;": "Γ",
	            "&Delta;": "Δ",
	            "&Epsilon;": "Ε",
	            "&Zeta;": "Ζ",
	            "&Eta;": "Η",
	            "&Theta;": "Θ",
	            "&Iota;": "Ι",
	            "&Kappa;": "Κ",
	            "&Lambda;": "Λ",
	            "&Mu;": "Μ",
	            "&Nu;": "Ν",
	            "&Xi;": "Ξ",
	            "&Omicron;": "Ο",
	            "&Pi;": "Π",
	            "&Rho;": "Ρ",
	            "&Sigma;": "Σ",
	            "&Tau;": "Τ",
	            "&Upsilon;": "Υ",
	            "&Phi;": "Φ",
	            "&Chi;": "Χ",
	            "&Psi;": "Ψ",
	            "&Omega;": "Ω",
	            "&alpha;": "α",
	            "&beta;": "β",
	            "&gamma;": "γ",
	            "&delta;": "δ",
	            "&epsilon;": "ε",
	            "&zeta;": "ζ",
	            "&eta;": "η",
	            "&theta;": "θ",
	            "&iota;": "ι",
	            "&kappa;": "κ",
	            "&lambda;": "λ",
	            "&mu;": "μ",
	            "&nu;": "ν",
	            "&xi;": "ξ",
	            "&omicron;": "ο",
	            "&pi;": "π",
	            "&rho;": "ρ",
	            "&sigmaf;": "ς",
	            "&sigma;": "σ",
	            "&tau;": "τ",
	            "&upsilon;": "υ",
	            "&phi;": "φ",
	            "&chi;": "χ",
	            "&psi;": "ψ",
	            "&omega;": "ω",
	            "&thetasym;": "ϑ",
	            "&upsih;": "ϒ",
	            "&piv;": "ϖ",
	            "&bull;": "•",
	            "&hellip;": "…",
	            "&prime;": "′",
	            "&Prime;": "″",
	            "&oline;": "‾",
	            "&frasl;": "⁄",
	            "&weierp;": "℘",
	            "&image;": "ℑ",
	            "&real;": "ℜ",
	            "&trade;": "™",
	            "&alefsym;": "ℵ",
	            "&larr;": "←",
	            "&uarr;": "↑",
	            "&rarr;": "→",
	            "&darr;": "↓",
	            "&harr;": "↔",
	            "&crarr;": "↵",
	            "&lArr;": "⇐",
	            "&uArr;": "⇑",
	            "&rArr;": "⇒",
	            "&dArr;": "⇓",
	            "&hArr;": "⇔",
	            "&forall;": "∀",
	            "&part;": "∂",
	            "&exist;": "∃",
	            "&empty;": "∅",
	            "&nabla;": "∇",
	            "&isin;": "∈",
	            "&notin;": "∉",
	            "&ni;": "∋",
	            "&prod;": "∏",
	            "&sum;": "∑",
	            "&minus;": "−",
	            "&lowast;": "∗",
	            "&radic;": "√",
	            "&prop;": "∝",
	            "&infin;": "∞",
	            "&ang;": "∠",
	            "&and;": "∧",
	            "&or;": "∨",
	            "&cap;": "∩",
	            "&cup;": "∪",
	            "&int;": "∫",
	            "&there4;": "∴",
	            "&sim;": "∼",
	            "&cong;": "≅",
	            "&asymp;": "≈",
	            "&ne;": "≠",
	            "&equiv;": "≡",
	            "&le;": "≤",
	            "&ge;": "≥",
	            "&sub;": "⊂",
	            "&sup;": "⊃",
	            "&nsub;": "⊄",
	            "&sube;": "⊆",
	            "&supe;": "⊇",
	            "&oplus;": "⊕",
	            "&otimes;": "⊗",
	            "&perp;": "⊥",
	            "&sdot;": "⋅",
	            "&lceil;": "⌈",
	            "&rceil;": "⌉",
	            "&lfloor;": "⌊",
	            "&rfloor;": "⌋",
	            "&lang;": "〈",
	            "&rang;": "〉",
	            "&loz;": "◊",
	            "&spades;": "♠",
	            "&clubs;": "♣",
	            "&hearts;": "♥",
	            "&diams;": "♦"
	        },
	        characters: {
	            "'": "&apos;",
	            " ": "&nbsp;",
	            "¡": "&iexcl;",
	            "¢": "&cent;",
	            "£": "&pound;",
	            "¤": "&curren;",
	            "¥": "&yen;",
	            "¦": "&brvbar;",
	            "§": "&sect;",
	            "¨": "&uml;",
	            "©": "&copy;",
	            "ª": "&ordf;",
	            "«": "&laquo;",
	            "¬": "&not;",
	            "­": "&shy;",
	            "®": "&reg;",
	            "¯": "&macr;",
	            "°": "&deg;",
	            "±": "&plusmn;",
	            "²": "&sup2;",
	            "³": "&sup3;",
	            "´": "&acute;",
	            "µ": "&micro;",
	            "¶": "&para;",
	            "·": "&middot;",
	            "¸": "&cedil;",
	            "¹": "&sup1;",
	            "º": "&ordm;",
	            "»": "&raquo;",
	            "¼": "&frac14;",
	            "½": "&frac12;",
	            "¾": "&frac34;",
	            "¿": "&iquest;",
	            "À": "&Agrave;",
	            "Á": "&Aacute;",
	            "Â": "&Acirc;",
	            "Ã": "&Atilde;",
	            "Ä": "&Auml;",
	            "Å": "&Aring;",
	            "Æ": "&AElig;",
	            "Ç": "&Ccedil;",
	            "È": "&Egrave;",
	            "É": "&Eacute;",
	            "Ê": "&Ecirc;",
	            "Ë": "&Euml;",
	            "Ì": "&Igrave;",
	            "Í": "&Iacute;",
	            "Î": "&Icirc;",
	            "Ï": "&Iuml;",
	            "Ð": "&ETH;",
	            "Ñ": "&Ntilde;",
	            "Ò": "&Ograve;",
	            "Ó": "&Oacute;",
	            "Ô": "&Ocirc;",
	            "Õ": "&Otilde;",
	            "Ö": "&Ouml;",
	            "×": "&times;",
	            "Ø": "&Oslash;",
	            "Ù": "&Ugrave;",
	            "Ú": "&Uacute;",
	            "Û": "&Ucirc;",
	            "Ü": "&Uuml;",
	            "Ý": "&Yacute;",
	            "Þ": "&THORN;",
	            "ß": "&szlig;",
	            "à": "&agrave;",
	            "á": "&aacute;",
	            "â": "&acirc;",
	            "ã": "&atilde;",
	            "ä": "&auml;",
	            "å": "&aring;",
	            "æ": "&aelig;",
	            "ç": "&ccedil;",
	            "è": "&egrave;",
	            "é": "&eacute;",
	            "ê": "&ecirc;",
	            "ë": "&euml;",
	            "ì": "&igrave;",
	            "í": "&iacute;",
	            "î": "&icirc;",
	            "ï": "&iuml;",
	            "ð": "&eth;",
	            "ñ": "&ntilde;",
	            "ò": "&ograve;",
	            "ó": "&oacute;",
	            "ô": "&ocirc;",
	            "õ": "&otilde;",
	            "ö": "&ouml;",
	            "÷": "&divide;",
	            "ø": "&oslash;",
	            "ù": "&ugrave;",
	            "ú": "&uacute;",
	            "û": "&ucirc;",
	            "ü": "&uuml;",
	            "ý": "&yacute;",
	            "þ": "&thorn;",
	            "ÿ": "&yuml;",
	            '"': "&quot;",
	            "&": "&amp;",
	            "<": "&lt;",
	            ">": "&gt;",
	            "Œ": "&OElig;",
	            "œ": "&oelig;",
	            "Š": "&Scaron;",
	            "š": "&scaron;",
	            "Ÿ": "&Yuml;",
	            "ˆ": "&circ;",
	            "˜": "&tilde;",
	            " ": "&ensp;",
	            " ": "&emsp;",
	            " ": "&thinsp;",
	            "‌": "&zwnj;",
	            "‍": "&zwj;",
	            "‎": "&lrm;",
	            "‏": "&rlm;",
	            "–": "&ndash;",
	            "—": "&mdash;",
	            "‘": "&lsquo;",
	            "’": "&rsquo;",
	            "‚": "&sbquo;",
	            "“": "&ldquo;",
	            "”": "&rdquo;",
	            "„": "&bdquo;",
	            "†": "&dagger;",
	            "‡": "&Dagger;",
	            "‰": "&permil;",
	            "‹": "&lsaquo;",
	            "›": "&rsaquo;",
	            "€": "&euro;",
	            "ƒ": "&fnof;",
	            "Α": "&Alpha;",
	            "Β": "&Beta;",
	            "Γ": "&Gamma;",
	            "Δ": "&Delta;",
	            "Ε": "&Epsilon;",
	            "Ζ": "&Zeta;",
	            "Η": "&Eta;",
	            "Θ": "&Theta;",
	            "Ι": "&Iota;",
	            "Κ": "&Kappa;",
	            "Λ": "&Lambda;",
	            "Μ": "&Mu;",
	            "Ν": "&Nu;",
	            "Ξ": "&Xi;",
	            "Ο": "&Omicron;",
	            "Π": "&Pi;",
	            "Ρ": "&Rho;",
	            "Σ": "&Sigma;",
	            "Τ": "&Tau;",
	            "Υ": "&Upsilon;",
	            "Φ": "&Phi;",
	            "Χ": "&Chi;",
	            "Ψ": "&Psi;",
	            "Ω": "&Omega;",
	            "α": "&alpha;",
	            "β": "&beta;",
	            "γ": "&gamma;",
	            "δ": "&delta;",
	            "ε": "&epsilon;",
	            "ζ": "&zeta;",
	            "η": "&eta;",
	            "θ": "&theta;",
	            "ι": "&iota;",
	            "κ": "&kappa;",
	            "λ": "&lambda;",
	            "μ": "&mu;",
	            "ν": "&nu;",
	            "ξ": "&xi;",
	            "ο": "&omicron;",
	            "π": "&pi;",
	            "ρ": "&rho;",
	            "ς": "&sigmaf;",
	            "σ": "&sigma;",
	            "τ": "&tau;",
	            "υ": "&upsilon;",
	            "φ": "&phi;",
	            "χ": "&chi;",
	            "ψ": "&psi;",
	            "ω": "&omega;",
	            "ϑ": "&thetasym;",
	            "ϒ": "&upsih;",
	            "ϖ": "&piv;",
	            "•": "&bull;",
	            "…": "&hellip;",
	            "′": "&prime;",
	            "″": "&Prime;",
	            "‾": "&oline;",
	            "⁄": "&frasl;",
	            "℘": "&weierp;",
	            "ℑ": "&image;",
	            "ℜ": "&real;",
	            "™": "&trade;",
	            "ℵ": "&alefsym;",
	            "←": "&larr;",
	            "↑": "&uarr;",
	            "→": "&rarr;",
	            "↓": "&darr;",
	            "↔": "&harr;",
	            "↵": "&crarr;",
	            "⇐": "&lArr;",
	            "⇑": "&uArr;",
	            "⇒": "&rArr;",
	            "⇓": "&dArr;",
	            "⇔": "&hArr;",
	            "∀": "&forall;",
	            "∂": "&part;",
	            "∃": "&exist;",
	            "∅": "&empty;",
	            "∇": "&nabla;",
	            "∈": "&isin;",
	            "∉": "&notin;",
	            "∋": "&ni;",
	            "∏": "&prod;",
	            "∑": "&sum;",
	            "−": "&minus;",
	            "∗": "&lowast;",
	            "√": "&radic;",
	            "∝": "&prop;",
	            "∞": "&infin;",
	            "∠": "&ang;",
	            "∧": "&and;",
	            "∨": "&or;",
	            "∩": "&cap;",
	            "∪": "&cup;",
	            "∫": "&int;",
	            "∴": "&there4;",
	            "∼": "&sim;",
	            "≅": "&cong;",
	            "≈": "&asymp;",
	            "≠": "&ne;",
	            "≡": "&equiv;",
	            "≤": "&le;",
	            "≥": "&ge;",
	            "⊂": "&sub;",
	            "⊃": "&sup;",
	            "⊄": "&nsub;",
	            "⊆": "&sube;",
	            "⊇": "&supe;",
	            "⊕": "&oplus;",
	            "⊗": "&otimes;",
	            "⊥": "&perp;",
	            "⋅": "&sdot;",
	            "⌈": "&lceil;",
	            "⌉": "&rceil;",
	            "⌊": "&lfloor;",
	            "⌋": "&rfloor;",
	            "〈": "&lang;",
	            "〉": "&rang;",
	            "◊": "&loz;",
	            "♠": "&spades;",
	            "♣": "&clubs;",
	            "♥": "&hearts;",
	            "♦": "&diams;"
	        }
	    },
	    html5: {
	        entities: {
	            "&AElig": "Æ",
	            "&AElig;": "Æ",
	            "&AMP": "&",
	            "&AMP;": "&",
	            "&Aacute": "Á",
	            "&Aacute;": "Á",
	            "&Abreve;": "Ă",
	            "&Acirc": "Â",
	            "&Acirc;": "Â",
	            "&Acy;": "А",
	            "&Afr;": "𝔄",
	            "&Agrave": "À",
	            "&Agrave;": "À",
	            "&Alpha;": "Α",
	            "&Amacr;": "Ā",
	            "&And;": "⩓",
	            "&Aogon;": "Ą",
	            "&Aopf;": "𝔸",
	            "&ApplyFunction;": "⁡",
	            "&Aring": "Å",
	            "&Aring;": "Å",
	            "&Ascr;": "𝒜",
	            "&Assign;": "≔",
	            "&Atilde": "Ã",
	            "&Atilde;": "Ã",
	            "&Auml": "Ä",
	            "&Auml;": "Ä",
	            "&Backslash;": "∖",
	            "&Barv;": "⫧",
	            "&Barwed;": "⌆",
	            "&Bcy;": "Б",
	            "&Because;": "∵",
	            "&Bernoullis;": "ℬ",
	            "&Beta;": "Β",
	            "&Bfr;": "𝔅",
	            "&Bopf;": "𝔹",
	            "&Breve;": "˘",
	            "&Bscr;": "ℬ",
	            "&Bumpeq;": "≎",
	            "&CHcy;": "Ч",
	            "&COPY": "©",
	            "&COPY;": "©",
	            "&Cacute;": "Ć",
	            "&Cap;": "⋒",
	            "&CapitalDifferentialD;": "ⅅ",
	            "&Cayleys;": "ℭ",
	            "&Ccaron;": "Č",
	            "&Ccedil": "Ç",
	            "&Ccedil;": "Ç",
	            "&Ccirc;": "Ĉ",
	            "&Cconint;": "∰",
	            "&Cdot;": "Ċ",
	            "&Cedilla;": "¸",
	            "&CenterDot;": "·",
	            "&Cfr;": "ℭ",
	            "&Chi;": "Χ",
	            "&CircleDot;": "⊙",
	            "&CircleMinus;": "⊖",
	            "&CirclePlus;": "⊕",
	            "&CircleTimes;": "⊗",
	            "&ClockwiseContourIntegral;": "∲",
	            "&CloseCurlyDoubleQuote;": "”",
	            "&CloseCurlyQuote;": "’",
	            "&Colon;": "∷",
	            "&Colone;": "⩴",
	            "&Congruent;": "≡",
	            "&Conint;": "∯",
	            "&ContourIntegral;": "∮",
	            "&Copf;": "ℂ",
	            "&Coproduct;": "∐",
	            "&CounterClockwiseContourIntegral;": "∳",
	            "&Cross;": "⨯",
	            "&Cscr;": "𝒞",
	            "&Cup;": "⋓",
	            "&CupCap;": "≍",
	            "&DD;": "ⅅ",
	            "&DDotrahd;": "⤑",
	            "&DJcy;": "Ђ",
	            "&DScy;": "Ѕ",
	            "&DZcy;": "Џ",
	            "&Dagger;": "‡",
	            "&Darr;": "↡",
	            "&Dashv;": "⫤",
	            "&Dcaron;": "Ď",
	            "&Dcy;": "Д",
	            "&Del;": "∇",
	            "&Delta;": "Δ",
	            "&Dfr;": "𝔇",
	            "&DiacriticalAcute;": "´",
	            "&DiacriticalDot;": "˙",
	            "&DiacriticalDoubleAcute;": "˝",
	            "&DiacriticalGrave;": "`",
	            "&DiacriticalTilde;": "˜",
	            "&Diamond;": "⋄",
	            "&DifferentialD;": "ⅆ",
	            "&Dopf;": "𝔻",
	            "&Dot;": "¨",
	            "&DotDot;": "⃜",
	            "&DotEqual;": "≐",
	            "&DoubleContourIntegral;": "∯",
	            "&DoubleDot;": "¨",
	            "&DoubleDownArrow;": "⇓",
	            "&DoubleLeftArrow;": "⇐",
	            "&DoubleLeftRightArrow;": "⇔",
	            "&DoubleLeftTee;": "⫤",
	            "&DoubleLongLeftArrow;": "⟸",
	            "&DoubleLongLeftRightArrow;": "⟺",
	            "&DoubleLongRightArrow;": "⟹",
	            "&DoubleRightArrow;": "⇒",
	            "&DoubleRightTee;": "⊨",
	            "&DoubleUpArrow;": "⇑",
	            "&DoubleUpDownArrow;": "⇕",
	            "&DoubleVerticalBar;": "∥",
	            "&DownArrow;": "↓",
	            "&DownArrowBar;": "⤓",
	            "&DownArrowUpArrow;": "⇵",
	            "&DownBreve;": "̑",
	            "&DownLeftRightVector;": "⥐",
	            "&DownLeftTeeVector;": "⥞",
	            "&DownLeftVector;": "↽",
	            "&DownLeftVectorBar;": "⥖",
	            "&DownRightTeeVector;": "⥟",
	            "&DownRightVector;": "⇁",
	            "&DownRightVectorBar;": "⥗",
	            "&DownTee;": "⊤",
	            "&DownTeeArrow;": "↧",
	            "&Downarrow;": "⇓",
	            "&Dscr;": "𝒟",
	            "&Dstrok;": "Đ",
	            "&ENG;": "Ŋ",
	            "&ETH": "Ð",
	            "&ETH;": "Ð",
	            "&Eacute": "É",
	            "&Eacute;": "É",
	            "&Ecaron;": "Ě",
	            "&Ecirc": "Ê",
	            "&Ecirc;": "Ê",
	            "&Ecy;": "Э",
	            "&Edot;": "Ė",
	            "&Efr;": "𝔈",
	            "&Egrave": "È",
	            "&Egrave;": "È",
	            "&Element;": "∈",
	            "&Emacr;": "Ē",
	            "&EmptySmallSquare;": "◻",
	            "&EmptyVerySmallSquare;": "▫",
	            "&Eogon;": "Ę",
	            "&Eopf;": "𝔼",
	            "&Epsilon;": "Ε",
	            "&Equal;": "⩵",
	            "&EqualTilde;": "≂",
	            "&Equilibrium;": "⇌",
	            "&Escr;": "ℰ",
	            "&Esim;": "⩳",
	            "&Eta;": "Η",
	            "&Euml": "Ë",
	            "&Euml;": "Ë",
	            "&Exists;": "∃",
	            "&ExponentialE;": "ⅇ",
	            "&Fcy;": "Ф",
	            "&Ffr;": "𝔉",
	            "&FilledSmallSquare;": "◼",
	            "&FilledVerySmallSquare;": "▪",
	            "&Fopf;": "𝔽",
	            "&ForAll;": "∀",
	            "&Fouriertrf;": "ℱ",
	            "&Fscr;": "ℱ",
	            "&GJcy;": "Ѓ",
	            "&GT": ">",
	            "&GT;": ">",
	            "&Gamma;": "Γ",
	            "&Gammad;": "Ϝ",
	            "&Gbreve;": "Ğ",
	            "&Gcedil;": "Ģ",
	            "&Gcirc;": "Ĝ",
	            "&Gcy;": "Г",
	            "&Gdot;": "Ġ",
	            "&Gfr;": "𝔊",
	            "&Gg;": "⋙",
	            "&Gopf;": "𝔾",
	            "&GreaterEqual;": "≥",
	            "&GreaterEqualLess;": "⋛",
	            "&GreaterFullEqual;": "≧",
	            "&GreaterGreater;": "⪢",
	            "&GreaterLess;": "≷",
	            "&GreaterSlantEqual;": "⩾",
	            "&GreaterTilde;": "≳",
	            "&Gscr;": "𝒢",
	            "&Gt;": "≫",
	            "&HARDcy;": "Ъ",
	            "&Hacek;": "ˇ",
	            "&Hat;": "^",
	            "&Hcirc;": "Ĥ",
	            "&Hfr;": "ℌ",
	            "&HilbertSpace;": "ℋ",
	            "&Hopf;": "ℍ",
	            "&HorizontalLine;": "─",
	            "&Hscr;": "ℋ",
	            "&Hstrok;": "Ħ",
	            "&HumpDownHump;": "≎",
	            "&HumpEqual;": "≏",
	            "&IEcy;": "Е",
	            "&IJlig;": "Ĳ",
	            "&IOcy;": "Ё",
	            "&Iacute": "Í",
	            "&Iacute;": "Í",
	            "&Icirc": "Î",
	            "&Icirc;": "Î",
	            "&Icy;": "И",
	            "&Idot;": "İ",
	            "&Ifr;": "ℑ",
	            "&Igrave": "Ì",
	            "&Igrave;": "Ì",
	            "&Im;": "ℑ",
	            "&Imacr;": "Ī",
	            "&ImaginaryI;": "ⅈ",
	            "&Implies;": "⇒",
	            "&Int;": "∬",
	            "&Integral;": "∫",
	            "&Intersection;": "⋂",
	            "&InvisibleComma;": "⁣",
	            "&InvisibleTimes;": "⁢",
	            "&Iogon;": "Į",
	            "&Iopf;": "𝕀",
	            "&Iota;": "Ι",
	            "&Iscr;": "ℐ",
	            "&Itilde;": "Ĩ",
	            "&Iukcy;": "І",
	            "&Iuml": "Ï",
	            "&Iuml;": "Ï",
	            "&Jcirc;": "Ĵ",
	            "&Jcy;": "Й",
	            "&Jfr;": "𝔍",
	            "&Jopf;": "𝕁",
	            "&Jscr;": "𝒥",
	            "&Jsercy;": "Ј",
	            "&Jukcy;": "Є",
	            "&KHcy;": "Х",
	            "&KJcy;": "Ќ",
	            "&Kappa;": "Κ",
	            "&Kcedil;": "Ķ",
	            "&Kcy;": "К",
	            "&Kfr;": "𝔎",
	            "&Kopf;": "𝕂",
	            "&Kscr;": "𝒦",
	            "&LJcy;": "Љ",
	            "&LT": "<",
	            "&LT;": "<",
	            "&Lacute;": "Ĺ",
	            "&Lambda;": "Λ",
	            "&Lang;": "⟪",
	            "&Laplacetrf;": "ℒ",
	            "&Larr;": "↞",
	            "&Lcaron;": "Ľ",
	            "&Lcedil;": "Ļ",
	            "&Lcy;": "Л",
	            "&LeftAngleBracket;": "⟨",
	            "&LeftArrow;": "←",
	            "&LeftArrowBar;": "⇤",
	            "&LeftArrowRightArrow;": "⇆",
	            "&LeftCeiling;": "⌈",
	            "&LeftDoubleBracket;": "⟦",
	            "&LeftDownTeeVector;": "⥡",
	            "&LeftDownVector;": "⇃",
	            "&LeftDownVectorBar;": "⥙",
	            "&LeftFloor;": "⌊",
	            "&LeftRightArrow;": "↔",
	            "&LeftRightVector;": "⥎",
	            "&LeftTee;": "⊣",
	            "&LeftTeeArrow;": "↤",
	            "&LeftTeeVector;": "⥚",
	            "&LeftTriangle;": "⊲",
	            "&LeftTriangleBar;": "⧏",
	            "&LeftTriangleEqual;": "⊴",
	            "&LeftUpDownVector;": "⥑",
	            "&LeftUpTeeVector;": "⥠",
	            "&LeftUpVector;": "↿",
	            "&LeftUpVectorBar;": "⥘",
	            "&LeftVector;": "↼",
	            "&LeftVectorBar;": "⥒",
	            "&Leftarrow;": "⇐",
	            "&Leftrightarrow;": "⇔",
	            "&LessEqualGreater;": "⋚",
	            "&LessFullEqual;": "≦",
	            "&LessGreater;": "≶",
	            "&LessLess;": "⪡",
	            "&LessSlantEqual;": "⩽",
	            "&LessTilde;": "≲",
	            "&Lfr;": "𝔏",
	            "&Ll;": "⋘",
	            "&Lleftarrow;": "⇚",
	            "&Lmidot;": "Ŀ",
	            "&LongLeftArrow;": "⟵",
	            "&LongLeftRightArrow;": "⟷",
	            "&LongRightArrow;": "⟶",
	            "&Longleftarrow;": "⟸",
	            "&Longleftrightarrow;": "⟺",
	            "&Longrightarrow;": "⟹",
	            "&Lopf;": "𝕃",
	            "&LowerLeftArrow;": "↙",
	            "&LowerRightArrow;": "↘",
	            "&Lscr;": "ℒ",
	            "&Lsh;": "↰",
	            "&Lstrok;": "Ł",
	            "&Lt;": "≪",
	            "&Map;": "⤅",
	            "&Mcy;": "М",
	            "&MediumSpace;": " ",
	            "&Mellintrf;": "ℳ",
	            "&Mfr;": "𝔐",
	            "&MinusPlus;": "∓",
	            "&Mopf;": "𝕄",
	            "&Mscr;": "ℳ",
	            "&Mu;": "Μ",
	            "&NJcy;": "Њ",
	            "&Nacute;": "Ń",
	            "&Ncaron;": "Ň",
	            "&Ncedil;": "Ņ",
	            "&Ncy;": "Н",
	            "&NegativeMediumSpace;": "​",
	            "&NegativeThickSpace;": "​",
	            "&NegativeThinSpace;": "​",
	            "&NegativeVeryThinSpace;": "​",
	            "&NestedGreaterGreater;": "≫",
	            "&NestedLessLess;": "≪",
	            "&NewLine;": "\n",
	            "&Nfr;": "𝔑",
	            "&NoBreak;": "⁠",
	            "&NonBreakingSpace;": " ",
	            "&Nopf;": "ℕ",
	            "&Not;": "⫬",
	            "&NotCongruent;": "≢",
	            "&NotCupCap;": "≭",
	            "&NotDoubleVerticalBar;": "∦",
	            "&NotElement;": "∉",
	            "&NotEqual;": "≠",
	            "&NotEqualTilde;": "≂̸",
	            "&NotExists;": "∄",
	            "&NotGreater;": "≯",
	            "&NotGreaterEqual;": "≱",
	            "&NotGreaterFullEqual;": "≧̸",
	            "&NotGreaterGreater;": "≫̸",
	            "&NotGreaterLess;": "≹",
	            "&NotGreaterSlantEqual;": "⩾̸",
	            "&NotGreaterTilde;": "≵",
	            "&NotHumpDownHump;": "≎̸",
	            "&NotHumpEqual;": "≏̸",
	            "&NotLeftTriangle;": "⋪",
	            "&NotLeftTriangleBar;": "⧏̸",
	            "&NotLeftTriangleEqual;": "⋬",
	            "&NotLess;": "≮",
	            "&NotLessEqual;": "≰",
	            "&NotLessGreater;": "≸",
	            "&NotLessLess;": "≪̸",
	            "&NotLessSlantEqual;": "⩽̸",
	            "&NotLessTilde;": "≴",
	            "&NotNestedGreaterGreater;": "⪢̸",
	            "&NotNestedLessLess;": "⪡̸",
	            "&NotPrecedes;": "⊀",
	            "&NotPrecedesEqual;": "⪯̸",
	            "&NotPrecedesSlantEqual;": "⋠",
	            "&NotReverseElement;": "∌",
	            "&NotRightTriangle;": "⋫",
	            "&NotRightTriangleBar;": "⧐̸",
	            "&NotRightTriangleEqual;": "⋭",
	            "&NotSquareSubset;": "⊏̸",
	            "&NotSquareSubsetEqual;": "⋢",
	            "&NotSquareSuperset;": "⊐̸",
	            "&NotSquareSupersetEqual;": "⋣",
	            "&NotSubset;": "⊂⃒",
	            "&NotSubsetEqual;": "⊈",
	            "&NotSucceeds;": "⊁",
	            "&NotSucceedsEqual;": "⪰̸",
	            "&NotSucceedsSlantEqual;": "⋡",
	            "&NotSucceedsTilde;": "≿̸",
	            "&NotSuperset;": "⊃⃒",
	            "&NotSupersetEqual;": "⊉",
	            "&NotTilde;": "≁",
	            "&NotTildeEqual;": "≄",
	            "&NotTildeFullEqual;": "≇",
	            "&NotTildeTilde;": "≉",
	            "&NotVerticalBar;": "∤",
	            "&Nscr;": "𝒩",
	            "&Ntilde": "Ñ",
	            "&Ntilde;": "Ñ",
	            "&Nu;": "Ν",
	            "&OElig;": "Œ",
	            "&Oacute": "Ó",
	            "&Oacute;": "Ó",
	            "&Ocirc": "Ô",
	            "&Ocirc;": "Ô",
	            "&Ocy;": "О",
	            "&Odblac;": "Ő",
	            "&Ofr;": "𝔒",
	            "&Ograve": "Ò",
	            "&Ograve;": "Ò",
	            "&Omacr;": "Ō",
	            "&Omega;": "Ω",
	            "&Omicron;": "Ο",
	            "&Oopf;": "𝕆",
	            "&OpenCurlyDoubleQuote;": "“",
	            "&OpenCurlyQuote;": "‘",
	            "&Or;": "⩔",
	            "&Oscr;": "𝒪",
	            "&Oslash": "Ø",
	            "&Oslash;": "Ø",
	            "&Otilde": "Õ",
	            "&Otilde;": "Õ",
	            "&Otimes;": "⨷",
	            "&Ouml": "Ö",
	            "&Ouml;": "Ö",
	            "&OverBar;": "‾",
	            "&OverBrace;": "⏞",
	            "&OverBracket;": "⎴",
	            "&OverParenthesis;": "⏜",
	            "&PartialD;": "∂",
	            "&Pcy;": "П",
	            "&Pfr;": "𝔓",
	            "&Phi;": "Φ",
	            "&Pi;": "Π",
	            "&PlusMinus;": "±",
	            "&Poincareplane;": "ℌ",
	            "&Popf;": "ℙ",
	            "&Pr;": "⪻",
	            "&Precedes;": "≺",
	            "&PrecedesEqual;": "⪯",
	            "&PrecedesSlantEqual;": "≼",
	            "&PrecedesTilde;": "≾",
	            "&Prime;": "″",
	            "&Product;": "∏",
	            "&Proportion;": "∷",
	            "&Proportional;": "∝",
	            "&Pscr;": "𝒫",
	            "&Psi;": "Ψ",
	            "&QUOT": '"',
	            "&QUOT;": '"',
	            "&Qfr;": "𝔔",
	            "&Qopf;": "ℚ",
	            "&Qscr;": "𝒬",
	            "&RBarr;": "⤐",
	            "&REG": "®",
	            "&REG;": "®",
	            "&Racute;": "Ŕ",
	            "&Rang;": "⟫",
	            "&Rarr;": "↠",
	            "&Rarrtl;": "⤖",
	            "&Rcaron;": "Ř",
	            "&Rcedil;": "Ŗ",
	            "&Rcy;": "Р",
	            "&Re;": "ℜ",
	            "&ReverseElement;": "∋",
	            "&ReverseEquilibrium;": "⇋",
	            "&ReverseUpEquilibrium;": "⥯",
	            "&Rfr;": "ℜ",
	            "&Rho;": "Ρ",
	            "&RightAngleBracket;": "⟩",
	            "&RightArrow;": "→",
	            "&RightArrowBar;": "⇥",
	            "&RightArrowLeftArrow;": "⇄",
	            "&RightCeiling;": "⌉",
	            "&RightDoubleBracket;": "⟧",
	            "&RightDownTeeVector;": "⥝",
	            "&RightDownVector;": "⇂",
	            "&RightDownVectorBar;": "⥕",
	            "&RightFloor;": "⌋",
	            "&RightTee;": "⊢",
	            "&RightTeeArrow;": "↦",
	            "&RightTeeVector;": "⥛",
	            "&RightTriangle;": "⊳",
	            "&RightTriangleBar;": "⧐",
	            "&RightTriangleEqual;": "⊵",
	            "&RightUpDownVector;": "⥏",
	            "&RightUpTeeVector;": "⥜",
	            "&RightUpVector;": "↾",
	            "&RightUpVectorBar;": "⥔",
	            "&RightVector;": "⇀",
	            "&RightVectorBar;": "⥓",
	            "&Rightarrow;": "⇒",
	            "&Ropf;": "ℝ",
	            "&RoundImplies;": "⥰",
	            "&Rrightarrow;": "⇛",
	            "&Rscr;": "ℛ",
	            "&Rsh;": "↱",
	            "&RuleDelayed;": "⧴",
	            "&SHCHcy;": "Щ",
	            "&SHcy;": "Ш",
	            "&SOFTcy;": "Ь",
	            "&Sacute;": "Ś",
	            "&Sc;": "⪼",
	            "&Scaron;": "Š",
	            "&Scedil;": "Ş",
	            "&Scirc;": "Ŝ",
	            "&Scy;": "С",
	            "&Sfr;": "𝔖",
	            "&ShortDownArrow;": "↓",
	            "&ShortLeftArrow;": "←",
	            "&ShortRightArrow;": "→",
	            "&ShortUpArrow;": "↑",
	            "&Sigma;": "Σ",
	            "&SmallCircle;": "∘",
	            "&Sopf;": "𝕊",
	            "&Sqrt;": "√",
	            "&Square;": "□",
	            "&SquareIntersection;": "⊓",
	            "&SquareSubset;": "⊏",
	            "&SquareSubsetEqual;": "⊑",
	            "&SquareSuperset;": "⊐",
	            "&SquareSupersetEqual;": "⊒",
	            "&SquareUnion;": "⊔",
	            "&Sscr;": "𝒮",
	            "&Star;": "⋆",
	            "&Sub;": "⋐",
	            "&Subset;": "⋐",
	            "&SubsetEqual;": "⊆",
	            "&Succeeds;": "≻",
	            "&SucceedsEqual;": "⪰",
	            "&SucceedsSlantEqual;": "≽",
	            "&SucceedsTilde;": "≿",
	            "&SuchThat;": "∋",
	            "&Sum;": "∑",
	            "&Sup;": "⋑",
	            "&Superset;": "⊃",
	            "&SupersetEqual;": "⊇",
	            "&Supset;": "⋑",
	            "&THORN": "Þ",
	            "&THORN;": "Þ",
	            "&TRADE;": "™",
	            "&TSHcy;": "Ћ",
	            "&TScy;": "Ц",
	            "&Tab;": "\t",
	            "&Tau;": "Τ",
	            "&Tcaron;": "Ť",
	            "&Tcedil;": "Ţ",
	            "&Tcy;": "Т",
	            "&Tfr;": "𝔗",
	            "&Therefore;": "∴",
	            "&Theta;": "Θ",
	            "&ThickSpace;": "  ",
	            "&ThinSpace;": " ",
	            "&Tilde;": "∼",
	            "&TildeEqual;": "≃",
	            "&TildeFullEqual;": "≅",
	            "&TildeTilde;": "≈",
	            "&Topf;": "𝕋",
	            "&TripleDot;": "⃛",
	            "&Tscr;": "𝒯",
	            "&Tstrok;": "Ŧ",
	            "&Uacute": "Ú",
	            "&Uacute;": "Ú",
	            "&Uarr;": "↟",
	            "&Uarrocir;": "⥉",
	            "&Ubrcy;": "Ў",
	            "&Ubreve;": "Ŭ",
	            "&Ucirc": "Û",
	            "&Ucirc;": "Û",
	            "&Ucy;": "У",
	            "&Udblac;": "Ű",
	            "&Ufr;": "𝔘",
	            "&Ugrave": "Ù",
	            "&Ugrave;": "Ù",
	            "&Umacr;": "Ū",
	            "&UnderBar;": "_",
	            "&UnderBrace;": "⏟",
	            "&UnderBracket;": "⎵",
	            "&UnderParenthesis;": "⏝",
	            "&Union;": "⋃",
	            "&UnionPlus;": "⊎",
	            "&Uogon;": "Ų",
	            "&Uopf;": "𝕌",
	            "&UpArrow;": "↑",
	            "&UpArrowBar;": "⤒",
	            "&UpArrowDownArrow;": "⇅",
	            "&UpDownArrow;": "↕",
	            "&UpEquilibrium;": "⥮",
	            "&UpTee;": "⊥",
	            "&UpTeeArrow;": "↥",
	            "&Uparrow;": "⇑",
	            "&Updownarrow;": "⇕",
	            "&UpperLeftArrow;": "↖",
	            "&UpperRightArrow;": "↗",
	            "&Upsi;": "ϒ",
	            "&Upsilon;": "Υ",
	            "&Uring;": "Ů",
	            "&Uscr;": "𝒰",
	            "&Utilde;": "Ũ",
	            "&Uuml": "Ü",
	            "&Uuml;": "Ü",
	            "&VDash;": "⊫",
	            "&Vbar;": "⫫",
	            "&Vcy;": "В",
	            "&Vdash;": "⊩",
	            "&Vdashl;": "⫦",
	            "&Vee;": "⋁",
	            "&Verbar;": "‖",
	            "&Vert;": "‖",
	            "&VerticalBar;": "∣",
	            "&VerticalLine;": "|",
	            "&VerticalSeparator;": "❘",
	            "&VerticalTilde;": "≀",
	            "&VeryThinSpace;": " ",
	            "&Vfr;": "𝔙",
	            "&Vopf;": "𝕍",
	            "&Vscr;": "𝒱",
	            "&Vvdash;": "⊪",
	            "&Wcirc;": "Ŵ",
	            "&Wedge;": "⋀",
	            "&Wfr;": "𝔚",
	            "&Wopf;": "𝕎",
	            "&Wscr;": "𝒲",
	            "&Xfr;": "𝔛",
	            "&Xi;": "Ξ",
	            "&Xopf;": "𝕏",
	            "&Xscr;": "𝒳",
	            "&YAcy;": "Я",
	            "&YIcy;": "Ї",
	            "&YUcy;": "Ю",
	            "&Yacute": "Ý",
	            "&Yacute;": "Ý",
	            "&Ycirc;": "Ŷ",
	            "&Ycy;": "Ы",
	            "&Yfr;": "𝔜",
	            "&Yopf;": "𝕐",
	            "&Yscr;": "𝒴",
	            "&Yuml;": "Ÿ",
	            "&ZHcy;": "Ж",
	            "&Zacute;": "Ź",
	            "&Zcaron;": "Ž",
	            "&Zcy;": "З",
	            "&Zdot;": "Ż",
	            "&ZeroWidthSpace;": "​",
	            "&Zeta;": "Ζ",
	            "&Zfr;": "ℨ",
	            "&Zopf;": "ℤ",
	            "&Zscr;": "𝒵",
	            "&aacute": "á",
	            "&aacute;": "á",
	            "&abreve;": "ă",
	            "&ac;": "∾",
	            "&acE;": "∾̳",
	            "&acd;": "∿",
	            "&acirc": "â",
	            "&acirc;": "â",
	            "&acute": "´",
	            "&acute;": "´",
	            "&acy;": "а",
	            "&aelig": "æ",
	            "&aelig;": "æ",
	            "&af;": "⁡",
	            "&afr;": "𝔞",
	            "&agrave": "à",
	            "&agrave;": "à",
	            "&alefsym;": "ℵ",
	            "&aleph;": "ℵ",
	            "&alpha;": "α",
	            "&amacr;": "ā",
	            "&amalg;": "⨿",
	            "&amp": "&",
	            "&amp;": "&",
	            "&and;": "∧",
	            "&andand;": "⩕",
	            "&andd;": "⩜",
	            "&andslope;": "⩘",
	            "&andv;": "⩚",
	            "&ang;": "∠",
	            "&ange;": "⦤",
	            "&angle;": "∠",
	            "&angmsd;": "∡",
	            "&angmsdaa;": "⦨",
	            "&angmsdab;": "⦩",
	            "&angmsdac;": "⦪",
	            "&angmsdad;": "⦫",
	            "&angmsdae;": "⦬",
	            "&angmsdaf;": "⦭",
	            "&angmsdag;": "⦮",
	            "&angmsdah;": "⦯",
	            "&angrt;": "∟",
	            "&angrtvb;": "⊾",
	            "&angrtvbd;": "⦝",
	            "&angsph;": "∢",
	            "&angst;": "Å",
	            "&angzarr;": "⍼",
	            "&aogon;": "ą",
	            "&aopf;": "𝕒",
	            "&ap;": "≈",
	            "&apE;": "⩰",
	            "&apacir;": "⩯",
	            "&ape;": "≊",
	            "&apid;": "≋",
	            "&apos;": "'",
	            "&approx;": "≈",
	            "&approxeq;": "≊",
	            "&aring": "å",
	            "&aring;": "å",
	            "&ascr;": "𝒶",
	            "&ast;": "*",
	            "&asymp;": "≈",
	            "&asympeq;": "≍",
	            "&atilde": "ã",
	            "&atilde;": "ã",
	            "&auml": "ä",
	            "&auml;": "ä",
	            "&awconint;": "∳",
	            "&awint;": "⨑",
	            "&bNot;": "⫭",
	            "&backcong;": "≌",
	            "&backepsilon;": "϶",
	            "&backprime;": "‵",
	            "&backsim;": "∽",
	            "&backsimeq;": "⋍",
	            "&barvee;": "⊽",
	            "&barwed;": "⌅",
	            "&barwedge;": "⌅",
	            "&bbrk;": "⎵",
	            "&bbrktbrk;": "⎶",
	            "&bcong;": "≌",
	            "&bcy;": "б",
	            "&bdquo;": "„",
	            "&becaus;": "∵",
	            "&because;": "∵",
	            "&bemptyv;": "⦰",
	            "&bepsi;": "϶",
	            "&bernou;": "ℬ",
	            "&beta;": "β",
	            "&beth;": "ℶ",
	            "&between;": "≬",
	            "&bfr;": "𝔟",
	            "&bigcap;": "⋂",
	            "&bigcirc;": "◯",
	            "&bigcup;": "⋃",
	            "&bigodot;": "⨀",
	            "&bigoplus;": "⨁",
	            "&bigotimes;": "⨂",
	            "&bigsqcup;": "⨆",
	            "&bigstar;": "★",
	            "&bigtriangledown;": "▽",
	            "&bigtriangleup;": "△",
	            "&biguplus;": "⨄",
	            "&bigvee;": "⋁",
	            "&bigwedge;": "⋀",
	            "&bkarow;": "⤍",
	            "&blacklozenge;": "⧫",
	            "&blacksquare;": "▪",
	            "&blacktriangle;": "▴",
	            "&blacktriangledown;": "▾",
	            "&blacktriangleleft;": "◂",
	            "&blacktriangleright;": "▸",
	            "&blank;": "␣",
	            "&blk12;": "▒",
	            "&blk14;": "░",
	            "&blk34;": "▓",
	            "&block;": "█",
	            "&bne;": "=⃥",
	            "&bnequiv;": "≡⃥",
	            "&bnot;": "⌐",
	            "&bopf;": "𝕓",
	            "&bot;": "⊥",
	            "&bottom;": "⊥",
	            "&bowtie;": "⋈",
	            "&boxDL;": "╗",
	            "&boxDR;": "╔",
	            "&boxDl;": "╖",
	            "&boxDr;": "╓",
	            "&boxH;": "═",
	            "&boxHD;": "╦",
	            "&boxHU;": "╩",
	            "&boxHd;": "╤",
	            "&boxHu;": "╧",
	            "&boxUL;": "╝",
	            "&boxUR;": "╚",
	            "&boxUl;": "╜",
	            "&boxUr;": "╙",
	            "&boxV;": "║",
	            "&boxVH;": "╬",
	            "&boxVL;": "╣",
	            "&boxVR;": "╠",
	            "&boxVh;": "╫",
	            "&boxVl;": "╢",
	            "&boxVr;": "╟",
	            "&boxbox;": "⧉",
	            "&boxdL;": "╕",
	            "&boxdR;": "╒",
	            "&boxdl;": "┐",
	            "&boxdr;": "┌",
	            "&boxh;": "─",
	            "&boxhD;": "╥",
	            "&boxhU;": "╨",
	            "&boxhd;": "┬",
	            "&boxhu;": "┴",
	            "&boxminus;": "⊟",
	            "&boxplus;": "⊞",
	            "&boxtimes;": "⊠",
	            "&boxuL;": "╛",
	            "&boxuR;": "╘",
	            "&boxul;": "┘",
	            "&boxur;": "└",
	            "&boxv;": "│",
	            "&boxvH;": "╪",
	            "&boxvL;": "╡",
	            "&boxvR;": "╞",
	            "&boxvh;": "┼",
	            "&boxvl;": "┤",
	            "&boxvr;": "├",
	            "&bprime;": "‵",
	            "&breve;": "˘",
	            "&brvbar": "¦",
	            "&brvbar;": "¦",
	            "&bscr;": "𝒷",
	            "&bsemi;": "⁏",
	            "&bsim;": "∽",
	            "&bsime;": "⋍",
	            "&bsol;": "\\",
	            "&bsolb;": "⧅",
	            "&bsolhsub;": "⟈",
	            "&bull;": "•",
	            "&bullet;": "•",
	            "&bump;": "≎",
	            "&bumpE;": "⪮",
	            "&bumpe;": "≏",
	            "&bumpeq;": "≏",
	            "&cacute;": "ć",
	            "&cap;": "∩",
	            "&capand;": "⩄",
	            "&capbrcup;": "⩉",
	            "&capcap;": "⩋",
	            "&capcup;": "⩇",
	            "&capdot;": "⩀",
	            "&caps;": "∩︀",
	            "&caret;": "⁁",
	            "&caron;": "ˇ",
	            "&ccaps;": "⩍",
	            "&ccaron;": "č",
	            "&ccedil": "ç",
	            "&ccedil;": "ç",
	            "&ccirc;": "ĉ",
	            "&ccups;": "⩌",
	            "&ccupssm;": "⩐",
	            "&cdot;": "ċ",
	            "&cedil": "¸",
	            "&cedil;": "¸",
	            "&cemptyv;": "⦲",
	            "&cent": "¢",
	            "&cent;": "¢",
	            "&centerdot;": "·",
	            "&cfr;": "𝔠",
	            "&chcy;": "ч",
	            "&check;": "✓",
	            "&checkmark;": "✓",
	            "&chi;": "χ",
	            "&cir;": "○",
	            "&cirE;": "⧃",
	            "&circ;": "ˆ",
	            "&circeq;": "≗",
	            "&circlearrowleft;": "↺",
	            "&circlearrowright;": "↻",
	            "&circledR;": "®",
	            "&circledS;": "Ⓢ",
	            "&circledast;": "⊛",
	            "&circledcirc;": "⊚",
	            "&circleddash;": "⊝",
	            "&cire;": "≗",
	            "&cirfnint;": "⨐",
	            "&cirmid;": "⫯",
	            "&cirscir;": "⧂",
	            "&clubs;": "♣",
	            "&clubsuit;": "♣",
	            "&colon;": ":",
	            "&colone;": "≔",
	            "&coloneq;": "≔",
	            "&comma;": ",",
	            "&commat;": "@",
	            "&comp;": "∁",
	            "&compfn;": "∘",
	            "&complement;": "∁",
	            "&complexes;": "ℂ",
	            "&cong;": "≅",
	            "&congdot;": "⩭",
	            "&conint;": "∮",
	            "&copf;": "𝕔",
	            "&coprod;": "∐",
	            "&copy": "©",
	            "&copy;": "©",
	            "&copysr;": "℗",
	            "&crarr;": "↵",
	            "&cross;": "✗",
	            "&cscr;": "𝒸",
	            "&csub;": "⫏",
	            "&csube;": "⫑",
	            "&csup;": "⫐",
	            "&csupe;": "⫒",
	            "&ctdot;": "⋯",
	            "&cudarrl;": "⤸",
	            "&cudarrr;": "⤵",
	            "&cuepr;": "⋞",
	            "&cuesc;": "⋟",
	            "&cularr;": "↶",
	            "&cularrp;": "⤽",
	            "&cup;": "∪",
	            "&cupbrcap;": "⩈",
	            "&cupcap;": "⩆",
	            "&cupcup;": "⩊",
	            "&cupdot;": "⊍",
	            "&cupor;": "⩅",
	            "&cups;": "∪︀",
	            "&curarr;": "↷",
	            "&curarrm;": "⤼",
	            "&curlyeqprec;": "⋞",
	            "&curlyeqsucc;": "⋟",
	            "&curlyvee;": "⋎",
	            "&curlywedge;": "⋏",
	            "&curren": "¤",
	            "&curren;": "¤",
	            "&curvearrowleft;": "↶",
	            "&curvearrowright;": "↷",
	            "&cuvee;": "⋎",
	            "&cuwed;": "⋏",
	            "&cwconint;": "∲",
	            "&cwint;": "∱",
	            "&cylcty;": "⌭",
	            "&dArr;": "⇓",
	            "&dHar;": "⥥",
	            "&dagger;": "†",
	            "&daleth;": "ℸ",
	            "&darr;": "↓",
	            "&dash;": "‐",
	            "&dashv;": "⊣",
	            "&dbkarow;": "⤏",
	            "&dblac;": "˝",
	            "&dcaron;": "ď",
	            "&dcy;": "д",
	            "&dd;": "ⅆ",
	            "&ddagger;": "‡",
	            "&ddarr;": "⇊",
	            "&ddotseq;": "⩷",
	            "&deg": "°",
	            "&deg;": "°",
	            "&delta;": "δ",
	            "&demptyv;": "⦱",
	            "&dfisht;": "⥿",
	            "&dfr;": "𝔡",
	            "&dharl;": "⇃",
	            "&dharr;": "⇂",
	            "&diam;": "⋄",
	            "&diamond;": "⋄",
	            "&diamondsuit;": "♦",
	            "&diams;": "♦",
	            "&die;": "¨",
	            "&digamma;": "ϝ",
	            "&disin;": "⋲",
	            "&div;": "÷",
	            "&divide": "÷",
	            "&divide;": "÷",
	            "&divideontimes;": "⋇",
	            "&divonx;": "⋇",
	            "&djcy;": "ђ",
	            "&dlcorn;": "⌞",
	            "&dlcrop;": "⌍",
	            "&dollar;": "$",
	            "&dopf;": "𝕕",
	            "&dot;": "˙",
	            "&doteq;": "≐",
	            "&doteqdot;": "≑",
	            "&dotminus;": "∸",
	            "&dotplus;": "∔",
	            "&dotsquare;": "⊡",
	            "&doublebarwedge;": "⌆",
	            "&downarrow;": "↓",
	            "&downdownarrows;": "⇊",
	            "&downharpoonleft;": "⇃",
	            "&downharpoonright;": "⇂",
	            "&drbkarow;": "⤐",
	            "&drcorn;": "⌟",
	            "&drcrop;": "⌌",
	            "&dscr;": "𝒹",
	            "&dscy;": "ѕ",
	            "&dsol;": "⧶",
	            "&dstrok;": "đ",
	            "&dtdot;": "⋱",
	            "&dtri;": "▿",
	            "&dtrif;": "▾",
	            "&duarr;": "⇵",
	            "&duhar;": "⥯",
	            "&dwangle;": "⦦",
	            "&dzcy;": "џ",
	            "&dzigrarr;": "⟿",
	            "&eDDot;": "⩷",
	            "&eDot;": "≑",
	            "&eacute": "é",
	            "&eacute;": "é",
	            "&easter;": "⩮",
	            "&ecaron;": "ě",
	            "&ecir;": "≖",
	            "&ecirc": "ê",
	            "&ecirc;": "ê",
	            "&ecolon;": "≕",
	            "&ecy;": "э",
	            "&edot;": "ė",
	            "&ee;": "ⅇ",
	            "&efDot;": "≒",
	            "&efr;": "𝔢",
	            "&eg;": "⪚",
	            "&egrave": "è",
	            "&egrave;": "è",
	            "&egs;": "⪖",
	            "&egsdot;": "⪘",
	            "&el;": "⪙",
	            "&elinters;": "⏧",
	            "&ell;": "ℓ",
	            "&els;": "⪕",
	            "&elsdot;": "⪗",
	            "&emacr;": "ē",
	            "&empty;": "∅",
	            "&emptyset;": "∅",
	            "&emptyv;": "∅",
	            "&emsp13;": " ",
	            "&emsp14;": " ",
	            "&emsp;": " ",
	            "&eng;": "ŋ",
	            "&ensp;": " ",
	            "&eogon;": "ę",
	            "&eopf;": "𝕖",
	            "&epar;": "⋕",
	            "&eparsl;": "⧣",
	            "&eplus;": "⩱",
	            "&epsi;": "ε",
	            "&epsilon;": "ε",
	            "&epsiv;": "ϵ",
	            "&eqcirc;": "≖",
	            "&eqcolon;": "≕",
	            "&eqsim;": "≂",
	            "&eqslantgtr;": "⪖",
	            "&eqslantless;": "⪕",
	            "&equals;": "=",
	            "&equest;": "≟",
	            "&equiv;": "≡",
	            "&equivDD;": "⩸",
	            "&eqvparsl;": "⧥",
	            "&erDot;": "≓",
	            "&erarr;": "⥱",
	            "&escr;": "ℯ",
	            "&esdot;": "≐",
	            "&esim;": "≂",
	            "&eta;": "η",
	            "&eth": "ð",
	            "&eth;": "ð",
	            "&euml": "ë",
	            "&euml;": "ë",
	            "&euro;": "€",
	            "&excl;": "!",
	            "&exist;": "∃",
	            "&expectation;": "ℰ",
	            "&exponentiale;": "ⅇ",
	            "&fallingdotseq;": "≒",
	            "&fcy;": "ф",
	            "&female;": "♀",
	            "&ffilig;": "ﬃ",
	            "&fflig;": "ﬀ",
	            "&ffllig;": "ﬄ",
	            "&ffr;": "𝔣",
	            "&filig;": "ﬁ",
	            "&fjlig;": "fj",
	            "&flat;": "♭",
	            "&fllig;": "ﬂ",
	            "&fltns;": "▱",
	            "&fnof;": "ƒ",
	            "&fopf;": "𝕗",
	            "&forall;": "∀",
	            "&fork;": "⋔",
	            "&forkv;": "⫙",
	            "&fpartint;": "⨍",
	            "&frac12": "½",
	            "&frac12;": "½",
	            "&frac13;": "⅓",
	            "&frac14": "¼",
	            "&frac14;": "¼",
	            "&frac15;": "⅕",
	            "&frac16;": "⅙",
	            "&frac18;": "⅛",
	            "&frac23;": "⅔",
	            "&frac25;": "⅖",
	            "&frac34": "¾",
	            "&frac34;": "¾",
	            "&frac35;": "⅗",
	            "&frac38;": "⅜",
	            "&frac45;": "⅘",
	            "&frac56;": "⅚",
	            "&frac58;": "⅝",
	            "&frac78;": "⅞",
	            "&frasl;": "⁄",
	            "&frown;": "⌢",
	            "&fscr;": "𝒻",
	            "&gE;": "≧",
	            "&gEl;": "⪌",
	            "&gacute;": "ǵ",
	            "&gamma;": "γ",
	            "&gammad;": "ϝ",
	            "&gap;": "⪆",
	            "&gbreve;": "ğ",
	            "&gcirc;": "ĝ",
	            "&gcy;": "г",
	            "&gdot;": "ġ",
	            "&ge;": "≥",
	            "&gel;": "⋛",
	            "&geq;": "≥",
	            "&geqq;": "≧",
	            "&geqslant;": "⩾",
	            "&ges;": "⩾",
	            "&gescc;": "⪩",
	            "&gesdot;": "⪀",
	            "&gesdoto;": "⪂",
	            "&gesdotol;": "⪄",
	            "&gesl;": "⋛︀",
	            "&gesles;": "⪔",
	            "&gfr;": "𝔤",
	            "&gg;": "≫",
	            "&ggg;": "⋙",
	            "&gimel;": "ℷ",
	            "&gjcy;": "ѓ",
	            "&gl;": "≷",
	            "&glE;": "⪒",
	            "&gla;": "⪥",
	            "&glj;": "⪤",
	            "&gnE;": "≩",
	            "&gnap;": "⪊",
	            "&gnapprox;": "⪊",
	            "&gne;": "⪈",
	            "&gneq;": "⪈",
	            "&gneqq;": "≩",
	            "&gnsim;": "⋧",
	            "&gopf;": "𝕘",
	            "&grave;": "`",
	            "&gscr;": "ℊ",
	            "&gsim;": "≳",
	            "&gsime;": "⪎",
	            "&gsiml;": "⪐",
	            "&gt": ">",
	            "&gt;": ">",
	            "&gtcc;": "⪧",
	            "&gtcir;": "⩺",
	            "&gtdot;": "⋗",
	            "&gtlPar;": "⦕",
	            "&gtquest;": "⩼",
	            "&gtrapprox;": "⪆",
	            "&gtrarr;": "⥸",
	            "&gtrdot;": "⋗",
	            "&gtreqless;": "⋛",
	            "&gtreqqless;": "⪌",
	            "&gtrless;": "≷",
	            "&gtrsim;": "≳",
	            "&gvertneqq;": "≩︀",
	            "&gvnE;": "≩︀",
	            "&hArr;": "⇔",
	            "&hairsp;": " ",
	            "&half;": "½",
	            "&hamilt;": "ℋ",
	            "&hardcy;": "ъ",
	            "&harr;": "↔",
	            "&harrcir;": "⥈",
	            "&harrw;": "↭",
	            "&hbar;": "ℏ",
	            "&hcirc;": "ĥ",
	            "&hearts;": "♥",
	            "&heartsuit;": "♥",
	            "&hellip;": "…",
	            "&hercon;": "⊹",
	            "&hfr;": "𝔥",
	            "&hksearow;": "⤥",
	            "&hkswarow;": "⤦",
	            "&hoarr;": "⇿",
	            "&homtht;": "∻",
	            "&hookleftarrow;": "↩",
	            "&hookrightarrow;": "↪",
	            "&hopf;": "𝕙",
	            "&horbar;": "―",
	            "&hscr;": "𝒽",
	            "&hslash;": "ℏ",
	            "&hstrok;": "ħ",
	            "&hybull;": "⁃",
	            "&hyphen;": "‐",
	            "&iacute": "í",
	            "&iacute;": "í",
	            "&ic;": "⁣",
	            "&icirc": "î",
	            "&icirc;": "î",
	            "&icy;": "и",
	            "&iecy;": "е",
	            "&iexcl": "¡",
	            "&iexcl;": "¡",
	            "&iff;": "⇔",
	            "&ifr;": "𝔦",
	            "&igrave": "ì",
	            "&igrave;": "ì",
	            "&ii;": "ⅈ",
	            "&iiiint;": "⨌",
	            "&iiint;": "∭",
	            "&iinfin;": "⧜",
	            "&iiota;": "℩",
	            "&ijlig;": "ĳ",
	            "&imacr;": "ī",
	            "&image;": "ℑ",
	            "&imagline;": "ℐ",
	            "&imagpart;": "ℑ",
	            "&imath;": "ı",
	            "&imof;": "⊷",
	            "&imped;": "Ƶ",
	            "&in;": "∈",
	            "&incare;": "℅",
	            "&infin;": "∞",
	            "&infintie;": "⧝",
	            "&inodot;": "ı",
	            "&int;": "∫",
	            "&intcal;": "⊺",
	            "&integers;": "ℤ",
	            "&intercal;": "⊺",
	            "&intlarhk;": "⨗",
	            "&intprod;": "⨼",
	            "&iocy;": "ё",
	            "&iogon;": "į",
	            "&iopf;": "𝕚",
	            "&iota;": "ι",
	            "&iprod;": "⨼",
	            "&iquest": "¿",
	            "&iquest;": "¿",
	            "&iscr;": "𝒾",
	            "&isin;": "∈",
	            "&isinE;": "⋹",
	            "&isindot;": "⋵",
	            "&isins;": "⋴",
	            "&isinsv;": "⋳",
	            "&isinv;": "∈",
	            "&it;": "⁢",
	            "&itilde;": "ĩ",
	            "&iukcy;": "і",
	            "&iuml": "ï",
	            "&iuml;": "ï",
	            "&jcirc;": "ĵ",
	            "&jcy;": "й",
	            "&jfr;": "𝔧",
	            "&jmath;": "ȷ",
	            "&jopf;": "𝕛",
	            "&jscr;": "𝒿",
	            "&jsercy;": "ј",
	            "&jukcy;": "є",
	            "&kappa;": "κ",
	            "&kappav;": "ϰ",
	            "&kcedil;": "ķ",
	            "&kcy;": "к",
	            "&kfr;": "𝔨",
	            "&kgreen;": "ĸ",
	            "&khcy;": "х",
	            "&kjcy;": "ќ",
	            "&kopf;": "𝕜",
	            "&kscr;": "𝓀",
	            "&lAarr;": "⇚",
	            "&lArr;": "⇐",
	            "&lAtail;": "⤛",
	            "&lBarr;": "⤎",
	            "&lE;": "≦",
	            "&lEg;": "⪋",
	            "&lHar;": "⥢",
	            "&lacute;": "ĺ",
	            "&laemptyv;": "⦴",
	            "&lagran;": "ℒ",
	            "&lambda;": "λ",
	            "&lang;": "⟨",
	            "&langd;": "⦑",
	            "&langle;": "⟨",
	            "&lap;": "⪅",
	            "&laquo": "«",
	            "&laquo;": "«",
	            "&larr;": "←",
	            "&larrb;": "⇤",
	            "&larrbfs;": "⤟",
	            "&larrfs;": "⤝",
	            "&larrhk;": "↩",
	            "&larrlp;": "↫",
	            "&larrpl;": "⤹",
	            "&larrsim;": "⥳",
	            "&larrtl;": "↢",
	            "&lat;": "⪫",
	            "&latail;": "⤙",
	            "&late;": "⪭",
	            "&lates;": "⪭︀",
	            "&lbarr;": "⤌",
	            "&lbbrk;": "❲",
	            "&lbrace;": "{",
	            "&lbrack;": "[",
	            "&lbrke;": "⦋",
	            "&lbrksld;": "⦏",
	            "&lbrkslu;": "⦍",
	            "&lcaron;": "ľ",
	            "&lcedil;": "ļ",
	            "&lceil;": "⌈",
	            "&lcub;": "{",
	            "&lcy;": "л",
	            "&ldca;": "⤶",
	            "&ldquo;": "“",
	            "&ldquor;": "„",
	            "&ldrdhar;": "⥧",
	            "&ldrushar;": "⥋",
	            "&ldsh;": "↲",
	            "&le;": "≤",
	            "&leftarrow;": "←",
	            "&leftarrowtail;": "↢",
	            "&leftharpoondown;": "↽",
	            "&leftharpoonup;": "↼",
	            "&leftleftarrows;": "⇇",
	            "&leftrightarrow;": "↔",
	            "&leftrightarrows;": "⇆",
	            "&leftrightharpoons;": "⇋",
	            "&leftrightsquigarrow;": "↭",
	            "&leftthreetimes;": "⋋",
	            "&leg;": "⋚",
	            "&leq;": "≤",
	            "&leqq;": "≦",
	            "&leqslant;": "⩽",
	            "&les;": "⩽",
	            "&lescc;": "⪨",
	            "&lesdot;": "⩿",
	            "&lesdoto;": "⪁",
	            "&lesdotor;": "⪃",
	            "&lesg;": "⋚︀",
	            "&lesges;": "⪓",
	            "&lessapprox;": "⪅",
	            "&lessdot;": "⋖",
	            "&lesseqgtr;": "⋚",
	            "&lesseqqgtr;": "⪋",
	            "&lessgtr;": "≶",
	            "&lesssim;": "≲",
	            "&lfisht;": "⥼",
	            "&lfloor;": "⌊",
	            "&lfr;": "𝔩",
	            "&lg;": "≶",
	            "&lgE;": "⪑",
	            "&lhard;": "↽",
	            "&lharu;": "↼",
	            "&lharul;": "⥪",
	            "&lhblk;": "▄",
	            "&ljcy;": "љ",
	            "&ll;": "≪",
	            "&llarr;": "⇇",
	            "&llcorner;": "⌞",
	            "&llhard;": "⥫",
	            "&lltri;": "◺",
	            "&lmidot;": "ŀ",
	            "&lmoust;": "⎰",
	            "&lmoustache;": "⎰",
	            "&lnE;": "≨",
	            "&lnap;": "⪉",
	            "&lnapprox;": "⪉",
	            "&lne;": "⪇",
	            "&lneq;": "⪇",
	            "&lneqq;": "≨",
	            "&lnsim;": "⋦",
	            "&loang;": "⟬",
	            "&loarr;": "⇽",
	            "&lobrk;": "⟦",
	            "&longleftarrow;": "⟵",
	            "&longleftrightarrow;": "⟷",
	            "&longmapsto;": "⟼",
	            "&longrightarrow;": "⟶",
	            "&looparrowleft;": "↫",
	            "&looparrowright;": "↬",
	            "&lopar;": "⦅",
	            "&lopf;": "𝕝",
	            "&loplus;": "⨭",
	            "&lotimes;": "⨴",
	            "&lowast;": "∗",
	            "&lowbar;": "_",
	            "&loz;": "◊",
	            "&lozenge;": "◊",
	            "&lozf;": "⧫",
	            "&lpar;": "(",
	            "&lparlt;": "⦓",
	            "&lrarr;": "⇆",
	            "&lrcorner;": "⌟",
	            "&lrhar;": "⇋",
	            "&lrhard;": "⥭",
	            "&lrm;": "‎",
	            "&lrtri;": "⊿",
	            "&lsaquo;": "‹",
	            "&lscr;": "𝓁",
	            "&lsh;": "↰",
	            "&lsim;": "≲",
	            "&lsime;": "⪍",
	            "&lsimg;": "⪏",
	            "&lsqb;": "[",
	            "&lsquo;": "‘",
	            "&lsquor;": "‚",
	            "&lstrok;": "ł",
	            "&lt": "<",
	            "&lt;": "<",
	            "&ltcc;": "⪦",
	            "&ltcir;": "⩹",
	            "&ltdot;": "⋖",
	            "&lthree;": "⋋",
	            "&ltimes;": "⋉",
	            "&ltlarr;": "⥶",
	            "&ltquest;": "⩻",
	            "&ltrPar;": "⦖",
	            "&ltri;": "◃",
	            "&ltrie;": "⊴",
	            "&ltrif;": "◂",
	            "&lurdshar;": "⥊",
	            "&luruhar;": "⥦",
	            "&lvertneqq;": "≨︀",
	            "&lvnE;": "≨︀",
	            "&mDDot;": "∺",
	            "&macr": "¯",
	            "&macr;": "¯",
	            "&male;": "♂",
	            "&malt;": "✠",
	            "&maltese;": "✠",
	            "&map;": "↦",
	            "&mapsto;": "↦",
	            "&mapstodown;": "↧",
	            "&mapstoleft;": "↤",
	            "&mapstoup;": "↥",
	            "&marker;": "▮",
	            "&mcomma;": "⨩",
	            "&mcy;": "м",
	            "&mdash;": "—",
	            "&measuredangle;": "∡",
	            "&mfr;": "𝔪",
	            "&mho;": "℧",
	            "&micro": "µ",
	            "&micro;": "µ",
	            "&mid;": "∣",
	            "&midast;": "*",
	            "&midcir;": "⫰",
	            "&middot": "·",
	            "&middot;": "·",
	            "&minus;": "−",
	            "&minusb;": "⊟",
	            "&minusd;": "∸",
	            "&minusdu;": "⨪",
	            "&mlcp;": "⫛",
	            "&mldr;": "…",
	            "&mnplus;": "∓",
	            "&models;": "⊧",
	            "&mopf;": "𝕞",
	            "&mp;": "∓",
	            "&mscr;": "𝓂",
	            "&mstpos;": "∾",
	            "&mu;": "μ",
	            "&multimap;": "⊸",
	            "&mumap;": "⊸",
	            "&nGg;": "⋙̸",
	            "&nGt;": "≫⃒",
	            "&nGtv;": "≫̸",
	            "&nLeftarrow;": "⇍",
	            "&nLeftrightarrow;": "⇎",
	            "&nLl;": "⋘̸",
	            "&nLt;": "≪⃒",
	            "&nLtv;": "≪̸",
	            "&nRightarrow;": "⇏",
	            "&nVDash;": "⊯",
	            "&nVdash;": "⊮",
	            "&nabla;": "∇",
	            "&nacute;": "ń",
	            "&nang;": "∠⃒",
	            "&nap;": "≉",
	            "&napE;": "⩰̸",
	            "&napid;": "≋̸",
	            "&napos;": "ŉ",
	            "&napprox;": "≉",
	            "&natur;": "♮",
	            "&natural;": "♮",
	            "&naturals;": "ℕ",
	            "&nbsp": " ",
	            "&nbsp;": " ",
	            "&nbump;": "≎̸",
	            "&nbumpe;": "≏̸",
	            "&ncap;": "⩃",
	            "&ncaron;": "ň",
	            "&ncedil;": "ņ",
	            "&ncong;": "≇",
	            "&ncongdot;": "⩭̸",
	            "&ncup;": "⩂",
	            "&ncy;": "н",
	            "&ndash;": "–",
	            "&ne;": "≠",
	            "&neArr;": "⇗",
	            "&nearhk;": "⤤",
	            "&nearr;": "↗",
	            "&nearrow;": "↗",
	            "&nedot;": "≐̸",
	            "&nequiv;": "≢",
	            "&nesear;": "⤨",
	            "&nesim;": "≂̸",
	            "&nexist;": "∄",
	            "&nexists;": "∄",
	            "&nfr;": "𝔫",
	            "&ngE;": "≧̸",
	            "&nge;": "≱",
	            "&ngeq;": "≱",
	            "&ngeqq;": "≧̸",
	            "&ngeqslant;": "⩾̸",
	            "&nges;": "⩾̸",
	            "&ngsim;": "≵",
	            "&ngt;": "≯",
	            "&ngtr;": "≯",
	            "&nhArr;": "⇎",
	            "&nharr;": "↮",
	            "&nhpar;": "⫲",
	            "&ni;": "∋",
	            "&nis;": "⋼",
	            "&nisd;": "⋺",
	            "&niv;": "∋",
	            "&njcy;": "њ",
	            "&nlArr;": "⇍",
	            "&nlE;": "≦̸",
	            "&nlarr;": "↚",
	            "&nldr;": "‥",
	            "&nle;": "≰",
	            "&nleftarrow;": "↚",
	            "&nleftrightarrow;": "↮",
	            "&nleq;": "≰",
	            "&nleqq;": "≦̸",
	            "&nleqslant;": "⩽̸",
	            "&nles;": "⩽̸",
	            "&nless;": "≮",
	            "&nlsim;": "≴",
	            "&nlt;": "≮",
	            "&nltri;": "⋪",
	            "&nltrie;": "⋬",
	            "&nmid;": "∤",
	            "&nopf;": "𝕟",
	            "&not": "¬",
	            "&not;": "¬",
	            "&notin;": "∉",
	            "&notinE;": "⋹̸",
	            "&notindot;": "⋵̸",
	            "&notinva;": "∉",
	            "&notinvb;": "⋷",
	            "&notinvc;": "⋶",
	            "&notni;": "∌",
	            "&notniva;": "∌",
	            "&notnivb;": "⋾",
	            "&notnivc;": "⋽",
	            "&npar;": "∦",
	            "&nparallel;": "∦",
	            "&nparsl;": "⫽⃥",
	            "&npart;": "∂̸",
	            "&npolint;": "⨔",
	            "&npr;": "⊀",
	            "&nprcue;": "⋠",
	            "&npre;": "⪯̸",
	            "&nprec;": "⊀",
	            "&npreceq;": "⪯̸",
	            "&nrArr;": "⇏",
	            "&nrarr;": "↛",
	            "&nrarrc;": "⤳̸",
	            "&nrarrw;": "↝̸",
	            "&nrightarrow;": "↛",
	            "&nrtri;": "⋫",
	            "&nrtrie;": "⋭",
	            "&nsc;": "⊁",
	            "&nsccue;": "⋡",
	            "&nsce;": "⪰̸",
	            "&nscr;": "𝓃",
	            "&nshortmid;": "∤",
	            "&nshortparallel;": "∦",
	            "&nsim;": "≁",
	            "&nsime;": "≄",
	            "&nsimeq;": "≄",
	            "&nsmid;": "∤",
	            "&nspar;": "∦",
	            "&nsqsube;": "⋢",
	            "&nsqsupe;": "⋣",
	            "&nsub;": "⊄",
	            "&nsubE;": "⫅̸",
	            "&nsube;": "⊈",
	            "&nsubset;": "⊂⃒",
	            "&nsubseteq;": "⊈",
	            "&nsubseteqq;": "⫅̸",
	            "&nsucc;": "⊁",
	            "&nsucceq;": "⪰̸",
	            "&nsup;": "⊅",
	            "&nsupE;": "⫆̸",
	            "&nsupe;": "⊉",
	            "&nsupset;": "⊃⃒",
	            "&nsupseteq;": "⊉",
	            "&nsupseteqq;": "⫆̸",
	            "&ntgl;": "≹",
	            "&ntilde": "ñ",
	            "&ntilde;": "ñ",
	            "&ntlg;": "≸",
	            "&ntriangleleft;": "⋪",
	            "&ntrianglelefteq;": "⋬",
	            "&ntriangleright;": "⋫",
	            "&ntrianglerighteq;": "⋭",
	            "&nu;": "ν",
	            "&num;": "#",
	            "&numero;": "№",
	            "&numsp;": " ",
	            "&nvDash;": "⊭",
	            "&nvHarr;": "⤄",
	            "&nvap;": "≍⃒",
	            "&nvdash;": "⊬",
	            "&nvge;": "≥⃒",
	            "&nvgt;": ">⃒",
	            "&nvinfin;": "⧞",
	            "&nvlArr;": "⤂",
	            "&nvle;": "≤⃒",
	            "&nvlt;": "<⃒",
	            "&nvltrie;": "⊴⃒",
	            "&nvrArr;": "⤃",
	            "&nvrtrie;": "⊵⃒",
	            "&nvsim;": "∼⃒",
	            "&nwArr;": "⇖",
	            "&nwarhk;": "⤣",
	            "&nwarr;": "↖",
	            "&nwarrow;": "↖",
	            "&nwnear;": "⤧",
	            "&oS;": "Ⓢ",
	            "&oacute": "ó",
	            "&oacute;": "ó",
	            "&oast;": "⊛",
	            "&ocir;": "⊚",
	            "&ocirc": "ô",
	            "&ocirc;": "ô",
	            "&ocy;": "о",
	            "&odash;": "⊝",
	            "&odblac;": "ő",
	            "&odiv;": "⨸",
	            "&odot;": "⊙",
	            "&odsold;": "⦼",
	            "&oelig;": "œ",
	            "&ofcir;": "⦿",
	            "&ofr;": "𝔬",
	            "&ogon;": "˛",
	            "&ograve": "ò",
	            "&ograve;": "ò",
	            "&ogt;": "⧁",
	            "&ohbar;": "⦵",
	            "&ohm;": "Ω",
	            "&oint;": "∮",
	            "&olarr;": "↺",
	            "&olcir;": "⦾",
	            "&olcross;": "⦻",
	            "&oline;": "‾",
	            "&olt;": "⧀",
	            "&omacr;": "ō",
	            "&omega;": "ω",
	            "&omicron;": "ο",
	            "&omid;": "⦶",
	            "&ominus;": "⊖",
	            "&oopf;": "𝕠",
	            "&opar;": "⦷",
	            "&operp;": "⦹",
	            "&oplus;": "⊕",
	            "&or;": "∨",
	            "&orarr;": "↻",
	            "&ord;": "⩝",
	            "&order;": "ℴ",
	            "&orderof;": "ℴ",
	            "&ordf": "ª",
	            "&ordf;": "ª",
	            "&ordm": "º",
	            "&ordm;": "º",
	            "&origof;": "⊶",
	            "&oror;": "⩖",
	            "&orslope;": "⩗",
	            "&orv;": "⩛",
	            "&oscr;": "ℴ",
	            "&oslash": "ø",
	            "&oslash;": "ø",
	            "&osol;": "⊘",
	            "&otilde": "õ",
	            "&otilde;": "õ",
	            "&otimes;": "⊗",
	            "&otimesas;": "⨶",
	            "&ouml": "ö",
	            "&ouml;": "ö",
	            "&ovbar;": "⌽",
	            "&par;": "∥",
	            "&para": "¶",
	            "&para;": "¶",
	            "&parallel;": "∥",
	            "&parsim;": "⫳",
	            "&parsl;": "⫽",
	            "&part;": "∂",
	            "&pcy;": "п",
	            "&percnt;": "%",
	            "&period;": ".",
	            "&permil;": "‰",
	            "&perp;": "⊥",
	            "&pertenk;": "‱",
	            "&pfr;": "𝔭",
	            "&phi;": "φ",
	            "&phiv;": "ϕ",
	            "&phmmat;": "ℳ",
	            "&phone;": "☎",
	            "&pi;": "π",
	            "&pitchfork;": "⋔",
	            "&piv;": "ϖ",
	            "&planck;": "ℏ",
	            "&planckh;": "ℎ",
	            "&plankv;": "ℏ",
	            "&plus;": "+",
	            "&plusacir;": "⨣",
	            "&plusb;": "⊞",
	            "&pluscir;": "⨢",
	            "&plusdo;": "∔",
	            "&plusdu;": "⨥",
	            "&pluse;": "⩲",
	            "&plusmn": "±",
	            "&plusmn;": "±",
	            "&plussim;": "⨦",
	            "&plustwo;": "⨧",
	            "&pm;": "±",
	            "&pointint;": "⨕",
	            "&popf;": "𝕡",
	            "&pound": "£",
	            "&pound;": "£",
	            "&pr;": "≺",
	            "&prE;": "⪳",
	            "&prap;": "⪷",
	            "&prcue;": "≼",
	            "&pre;": "⪯",
	            "&prec;": "≺",
	            "&precapprox;": "⪷",
	            "&preccurlyeq;": "≼",
	            "&preceq;": "⪯",
	            "&precnapprox;": "⪹",
	            "&precneqq;": "⪵",
	            "&precnsim;": "⋨",
	            "&precsim;": "≾",
	            "&prime;": "′",
	            "&primes;": "ℙ",
	            "&prnE;": "⪵",
	            "&prnap;": "⪹",
	            "&prnsim;": "⋨",
	            "&prod;": "∏",
	            "&profalar;": "⌮",
	            "&profline;": "⌒",
	            "&profsurf;": "⌓",
	            "&prop;": "∝",
	            "&propto;": "∝",
	            "&prsim;": "≾",
	            "&prurel;": "⊰",
	            "&pscr;": "𝓅",
	            "&psi;": "ψ",
	            "&puncsp;": " ",
	            "&qfr;": "𝔮",
	            "&qint;": "⨌",
	            "&qopf;": "𝕢",
	            "&qprime;": "⁗",
	            "&qscr;": "𝓆",
	            "&quaternions;": "ℍ",
	            "&quatint;": "⨖",
	            "&quest;": "?",
	            "&questeq;": "≟",
	            "&quot": '"',
	            "&quot;": '"',
	            "&rAarr;": "⇛",
	            "&rArr;": "⇒",
	            "&rAtail;": "⤜",
	            "&rBarr;": "⤏",
	            "&rHar;": "⥤",
	            "&race;": "∽̱",
	            "&racute;": "ŕ",
	            "&radic;": "√",
	            "&raemptyv;": "⦳",
	            "&rang;": "⟩",
	            "&rangd;": "⦒",
	            "&range;": "⦥",
	            "&rangle;": "⟩",
	            "&raquo": "»",
	            "&raquo;": "»",
	            "&rarr;": "→",
	            "&rarrap;": "⥵",
	            "&rarrb;": "⇥",
	            "&rarrbfs;": "⤠",
	            "&rarrc;": "⤳",
	            "&rarrfs;": "⤞",
	            "&rarrhk;": "↪",
	            "&rarrlp;": "↬",
	            "&rarrpl;": "⥅",
	            "&rarrsim;": "⥴",
	            "&rarrtl;": "↣",
	            "&rarrw;": "↝",
	            "&ratail;": "⤚",
	            "&ratio;": "∶",
	            "&rationals;": "ℚ",
	            "&rbarr;": "⤍",
	            "&rbbrk;": "❳",
	            "&rbrace;": "}",
	            "&rbrack;": "]",
	            "&rbrke;": "⦌",
	            "&rbrksld;": "⦎",
	            "&rbrkslu;": "⦐",
	            "&rcaron;": "ř",
	            "&rcedil;": "ŗ",
	            "&rceil;": "⌉",
	            "&rcub;": "}",
	            "&rcy;": "р",
	            "&rdca;": "⤷",
	            "&rdldhar;": "⥩",
	            "&rdquo;": "”",
	            "&rdquor;": "”",
	            "&rdsh;": "↳",
	            "&real;": "ℜ",
	            "&realine;": "ℛ",
	            "&realpart;": "ℜ",
	            "&reals;": "ℝ",
	            "&rect;": "▭",
	            "&reg": "®",
	            "&reg;": "®",
	            "&rfisht;": "⥽",
	            "&rfloor;": "⌋",
	            "&rfr;": "𝔯",
	            "&rhard;": "⇁",
	            "&rharu;": "⇀",
	            "&rharul;": "⥬",
	            "&rho;": "ρ",
	            "&rhov;": "ϱ",
	            "&rightarrow;": "→",
	            "&rightarrowtail;": "↣",
	            "&rightharpoondown;": "⇁",
	            "&rightharpoonup;": "⇀",
	            "&rightleftarrows;": "⇄",
	            "&rightleftharpoons;": "⇌",
	            "&rightrightarrows;": "⇉",
	            "&rightsquigarrow;": "↝",
	            "&rightthreetimes;": "⋌",
	            "&ring;": "˚",
	            "&risingdotseq;": "≓",
	            "&rlarr;": "⇄",
	            "&rlhar;": "⇌",
	            "&rlm;": "‏",
	            "&rmoust;": "⎱",
	            "&rmoustache;": "⎱",
	            "&rnmid;": "⫮",
	            "&roang;": "⟭",
	            "&roarr;": "⇾",
	            "&robrk;": "⟧",
	            "&ropar;": "⦆",
	            "&ropf;": "𝕣",
	            "&roplus;": "⨮",
	            "&rotimes;": "⨵",
	            "&rpar;": ")",
	            "&rpargt;": "⦔",
	            "&rppolint;": "⨒",
	            "&rrarr;": "⇉",
	            "&rsaquo;": "›",
	            "&rscr;": "𝓇",
	            "&rsh;": "↱",
	            "&rsqb;": "]",
	            "&rsquo;": "’",
	            "&rsquor;": "’",
	            "&rthree;": "⋌",
	            "&rtimes;": "⋊",
	            "&rtri;": "▹",
	            "&rtrie;": "⊵",
	            "&rtrif;": "▸",
	            "&rtriltri;": "⧎",
	            "&ruluhar;": "⥨",
	            "&rx;": "℞",
	            "&sacute;": "ś",
	            "&sbquo;": "‚",
	            "&sc;": "≻",
	            "&scE;": "⪴",
	            "&scap;": "⪸",
	            "&scaron;": "š",
	            "&sccue;": "≽",
	            "&sce;": "⪰",
	            "&scedil;": "ş",
	            "&scirc;": "ŝ",
	            "&scnE;": "⪶",
	            "&scnap;": "⪺",
	            "&scnsim;": "⋩",
	            "&scpolint;": "⨓",
	            "&scsim;": "≿",
	            "&scy;": "с",
	            "&sdot;": "⋅",
	            "&sdotb;": "⊡",
	            "&sdote;": "⩦",
	            "&seArr;": "⇘",
	            "&searhk;": "⤥",
	            "&searr;": "↘",
	            "&searrow;": "↘",
	            "&sect": "§",
	            "&sect;": "§",
	            "&semi;": ";",
	            "&seswar;": "⤩",
	            "&setminus;": "∖",
	            "&setmn;": "∖",
	            "&sext;": "✶",
	            "&sfr;": "𝔰",
	            "&sfrown;": "⌢",
	            "&sharp;": "♯",
	            "&shchcy;": "щ",
	            "&shcy;": "ш",
	            "&shortmid;": "∣",
	            "&shortparallel;": "∥",
	            "&shy": "­",
	            "&shy;": "­",
	            "&sigma;": "σ",
	            "&sigmaf;": "ς",
	            "&sigmav;": "ς",
	            "&sim;": "∼",
	            "&simdot;": "⩪",
	            "&sime;": "≃",
	            "&simeq;": "≃",
	            "&simg;": "⪞",
	            "&simgE;": "⪠",
	            "&siml;": "⪝",
	            "&simlE;": "⪟",
	            "&simne;": "≆",
	            "&simplus;": "⨤",
	            "&simrarr;": "⥲",
	            "&slarr;": "←",
	            "&smallsetminus;": "∖",
	            "&smashp;": "⨳",
	            "&smeparsl;": "⧤",
	            "&smid;": "∣",
	            "&smile;": "⌣",
	            "&smt;": "⪪",
	            "&smte;": "⪬",
	            "&smtes;": "⪬︀",
	            "&softcy;": "ь",
	            "&sol;": "/",
	            "&solb;": "⧄",
	            "&solbar;": "⌿",
	            "&sopf;": "𝕤",
	            "&spades;": "♠",
	            "&spadesuit;": "♠",
	            "&spar;": "∥",
	            "&sqcap;": "⊓",
	            "&sqcaps;": "⊓︀",
	            "&sqcup;": "⊔",
	            "&sqcups;": "⊔︀",
	            "&sqsub;": "⊏",
	            "&sqsube;": "⊑",
	            "&sqsubset;": "⊏",
	            "&sqsubseteq;": "⊑",
	            "&sqsup;": "⊐",
	            "&sqsupe;": "⊒",
	            "&sqsupset;": "⊐",
	            "&sqsupseteq;": "⊒",
	            "&squ;": "□",
	            "&square;": "□",
	            "&squarf;": "▪",
	            "&squf;": "▪",
	            "&srarr;": "→",
	            "&sscr;": "𝓈",
	            "&ssetmn;": "∖",
	            "&ssmile;": "⌣",
	            "&sstarf;": "⋆",
	            "&star;": "☆",
	            "&starf;": "★",
	            "&straightepsilon;": "ϵ",
	            "&straightphi;": "ϕ",
	            "&strns;": "¯",
	            "&sub;": "⊂",
	            "&subE;": "⫅",
	            "&subdot;": "⪽",
	            "&sube;": "⊆",
	            "&subedot;": "⫃",
	            "&submult;": "⫁",
	            "&subnE;": "⫋",
	            "&subne;": "⊊",
	            "&subplus;": "⪿",
	            "&subrarr;": "⥹",
	            "&subset;": "⊂",
	            "&subseteq;": "⊆",
	            "&subseteqq;": "⫅",
	            "&subsetneq;": "⊊",
	            "&subsetneqq;": "⫋",
	            "&subsim;": "⫇",
	            "&subsub;": "⫕",
	            "&subsup;": "⫓",
	            "&succ;": "≻",
	            "&succapprox;": "⪸",
	            "&succcurlyeq;": "≽",
	            "&succeq;": "⪰",
	            "&succnapprox;": "⪺",
	            "&succneqq;": "⪶",
	            "&succnsim;": "⋩",
	            "&succsim;": "≿",
	            "&sum;": "∑",
	            "&sung;": "♪",
	            "&sup1": "¹",
	            "&sup1;": "¹",
	            "&sup2": "²",
	            "&sup2;": "²",
	            "&sup3": "³",
	            "&sup3;": "³",
	            "&sup;": "⊃",
	            "&supE;": "⫆",
	            "&supdot;": "⪾",
	            "&supdsub;": "⫘",
	            "&supe;": "⊇",
	            "&supedot;": "⫄",
	            "&suphsol;": "⟉",
	            "&suphsub;": "⫗",
	            "&suplarr;": "⥻",
	            "&supmult;": "⫂",
	            "&supnE;": "⫌",
	            "&supne;": "⊋",
	            "&supplus;": "⫀",
	            "&supset;": "⊃",
	            "&supseteq;": "⊇",
	            "&supseteqq;": "⫆",
	            "&supsetneq;": "⊋",
	            "&supsetneqq;": "⫌",
	            "&supsim;": "⫈",
	            "&supsub;": "⫔",
	            "&supsup;": "⫖",
	            "&swArr;": "⇙",
	            "&swarhk;": "⤦",
	            "&swarr;": "↙",
	            "&swarrow;": "↙",
	            "&swnwar;": "⤪",
	            "&szlig": "ß",
	            "&szlig;": "ß",
	            "&target;": "⌖",
	            "&tau;": "τ",
	            "&tbrk;": "⎴",
	            "&tcaron;": "ť",
	            "&tcedil;": "ţ",
	            "&tcy;": "т",
	            "&tdot;": "⃛",
	            "&telrec;": "⌕",
	            "&tfr;": "𝔱",
	            "&there4;": "∴",
	            "&therefore;": "∴",
	            "&theta;": "θ",
	            "&thetasym;": "ϑ",
	            "&thetav;": "ϑ",
	            "&thickapprox;": "≈",
	            "&thicksim;": "∼",
	            "&thinsp;": " ",
	            "&thkap;": "≈",
	            "&thksim;": "∼",
	            "&thorn": "þ",
	            "&thorn;": "þ",
	            "&tilde;": "˜",
	            "&times": "×",
	            "&times;": "×",
	            "&timesb;": "⊠",
	            "&timesbar;": "⨱",
	            "&timesd;": "⨰",
	            "&tint;": "∭",
	            "&toea;": "⤨",
	            "&top;": "⊤",
	            "&topbot;": "⌶",
	            "&topcir;": "⫱",
	            "&topf;": "𝕥",
	            "&topfork;": "⫚",
	            "&tosa;": "⤩",
	            "&tprime;": "‴",
	            "&trade;": "™",
	            "&triangle;": "▵",
	            "&triangledown;": "▿",
	            "&triangleleft;": "◃",
	            "&trianglelefteq;": "⊴",
	            "&triangleq;": "≜",
	            "&triangleright;": "▹",
	            "&trianglerighteq;": "⊵",
	            "&tridot;": "◬",
	            "&trie;": "≜",
	            "&triminus;": "⨺",
	            "&triplus;": "⨹",
	            "&trisb;": "⧍",
	            "&tritime;": "⨻",
	            "&trpezium;": "⏢",
	            "&tscr;": "𝓉",
	            "&tscy;": "ц",
	            "&tshcy;": "ћ",
	            "&tstrok;": "ŧ",
	            "&twixt;": "≬",
	            "&twoheadleftarrow;": "↞",
	            "&twoheadrightarrow;": "↠",
	            "&uArr;": "⇑",
	            "&uHar;": "⥣",
	            "&uacute": "ú",
	            "&uacute;": "ú",
	            "&uarr;": "↑",
	            "&ubrcy;": "ў",
	            "&ubreve;": "ŭ",
	            "&ucirc": "û",
	            "&ucirc;": "û",
	            "&ucy;": "у",
	            "&udarr;": "⇅",
	            "&udblac;": "ű",
	            "&udhar;": "⥮",
	            "&ufisht;": "⥾",
	            "&ufr;": "𝔲",
	            "&ugrave": "ù",
	            "&ugrave;": "ù",
	            "&uharl;": "↿",
	            "&uharr;": "↾",
	            "&uhblk;": "▀",
	            "&ulcorn;": "⌜",
	            "&ulcorner;": "⌜",
	            "&ulcrop;": "⌏",
	            "&ultri;": "◸",
	            "&umacr;": "ū",
	            "&uml": "¨",
	            "&uml;": "¨",
	            "&uogon;": "ų",
	            "&uopf;": "𝕦",
	            "&uparrow;": "↑",
	            "&updownarrow;": "↕",
	            "&upharpoonleft;": "↿",
	            "&upharpoonright;": "↾",
	            "&uplus;": "⊎",
	            "&upsi;": "υ",
	            "&upsih;": "ϒ",
	            "&upsilon;": "υ",
	            "&upuparrows;": "⇈",
	            "&urcorn;": "⌝",
	            "&urcorner;": "⌝",
	            "&urcrop;": "⌎",
	            "&uring;": "ů",
	            "&urtri;": "◹",
	            "&uscr;": "𝓊",
	            "&utdot;": "⋰",
	            "&utilde;": "ũ",
	            "&utri;": "▵",
	            "&utrif;": "▴",
	            "&uuarr;": "⇈",
	            "&uuml": "ü",
	            "&uuml;": "ü",
	            "&uwangle;": "⦧",
	            "&vArr;": "⇕",
	            "&vBar;": "⫨",
	            "&vBarv;": "⫩",
	            "&vDash;": "⊨",
	            "&vangrt;": "⦜",
	            "&varepsilon;": "ϵ",
	            "&varkappa;": "ϰ",
	            "&varnothing;": "∅",
	            "&varphi;": "ϕ",
	            "&varpi;": "ϖ",
	            "&varpropto;": "∝",
	            "&varr;": "↕",
	            "&varrho;": "ϱ",
	            "&varsigma;": "ς",
	            "&varsubsetneq;": "⊊︀",
	            "&varsubsetneqq;": "⫋︀",
	            "&varsupsetneq;": "⊋︀",
	            "&varsupsetneqq;": "⫌︀",
	            "&vartheta;": "ϑ",
	            "&vartriangleleft;": "⊲",
	            "&vartriangleright;": "⊳",
	            "&vcy;": "в",
	            "&vdash;": "⊢",
	            "&vee;": "∨",
	            "&veebar;": "⊻",
	            "&veeeq;": "≚",
	            "&vellip;": "⋮",
	            "&verbar;": "|",
	            "&vert;": "|",
	            "&vfr;": "𝔳",
	            "&vltri;": "⊲",
	            "&vnsub;": "⊂⃒",
	            "&vnsup;": "⊃⃒",
	            "&vopf;": "𝕧",
	            "&vprop;": "∝",
	            "&vrtri;": "⊳",
	            "&vscr;": "𝓋",
	            "&vsubnE;": "⫋︀",
	            "&vsubne;": "⊊︀",
	            "&vsupnE;": "⫌︀",
	            "&vsupne;": "⊋︀",
	            "&vzigzag;": "⦚",
	            "&wcirc;": "ŵ",
	            "&wedbar;": "⩟",
	            "&wedge;": "∧",
	            "&wedgeq;": "≙",
	            "&weierp;": "℘",
	            "&wfr;": "𝔴",
	            "&wopf;": "𝕨",
	            "&wp;": "℘",
	            "&wr;": "≀",
	            "&wreath;": "≀",
	            "&wscr;": "𝓌",
	            "&xcap;": "⋂",
	            "&xcirc;": "◯",
	            "&xcup;": "⋃",
	            "&xdtri;": "▽",
	            "&xfr;": "𝔵",
	            "&xhArr;": "⟺",
	            "&xharr;": "⟷",
	            "&xi;": "ξ",
	            "&xlArr;": "⟸",
	            "&xlarr;": "⟵",
	            "&xmap;": "⟼",
	            "&xnis;": "⋻",
	            "&xodot;": "⨀",
	            "&xopf;": "𝕩",
	            "&xoplus;": "⨁",
	            "&xotime;": "⨂",
	            "&xrArr;": "⟹",
	            "&xrarr;": "⟶",
	            "&xscr;": "𝓍",
	            "&xsqcup;": "⨆",
	            "&xuplus;": "⨄",
	            "&xutri;": "△",
	            "&xvee;": "⋁",
	            "&xwedge;": "⋀",
	            "&yacute": "ý",
	            "&yacute;": "ý",
	            "&yacy;": "я",
	            "&ycirc;": "ŷ",
	            "&ycy;": "ы",
	            "&yen": "¥",
	            "&yen;": "¥",
	            "&yfr;": "𝔶",
	            "&yicy;": "ї",
	            "&yopf;": "𝕪",
	            "&yscr;": "𝓎",
	            "&yucy;": "ю",
	            "&yuml": "ÿ",
	            "&yuml;": "ÿ",
	            "&zacute;": "ź",
	            "&zcaron;": "ž",
	            "&zcy;": "з",
	            "&zdot;": "ż",
	            "&zeetrf;": "ℨ",
	            "&zeta;": "ζ",
	            "&zfr;": "𝔷",
	            "&zhcy;": "ж",
	            "&zigrarr;": "⇝",
	            "&zopf;": "𝕫",
	            "&zscr;": "𝓏",
	            "&zwj;": "‍",
	            "&zwnj;": "‌"
	        },
	        characters: {
	            "Æ": "&AElig;",
	            "&": "&amp;",
	            "Á": "&Aacute;",
	            "Ă": "&Abreve;",
	            "Â": "&Acirc;",
	            "А": "&Acy;",
	            "𝔄": "&Afr;",
	            "À": "&Agrave;",
	            "Α": "&Alpha;",
	            "Ā": "&Amacr;",
	            "⩓": "&And;",
	            "Ą": "&Aogon;",
	            "𝔸": "&Aopf;",
	            "⁡": "&af;",
	            "Å": "&angst;",
	            "𝒜": "&Ascr;",
	            "≔": "&coloneq;",
	            "Ã": "&Atilde;",
	            "Ä": "&Auml;",
	            "∖": "&ssetmn;",
	            "⫧": "&Barv;",
	            "⌆": "&doublebarwedge;",
	            "Б": "&Bcy;",
	            "∵": "&because;",
	            "ℬ": "&bernou;",
	            "Β": "&Beta;",
	            "𝔅": "&Bfr;",
	            "𝔹": "&Bopf;",
	            "˘": "&breve;",
	            "≎": "&bump;",
	            "Ч": "&CHcy;",
	            "©": "&copy;",
	            "Ć": "&Cacute;",
	            "⋒": "&Cap;",
	            "ⅅ": "&DD;",
	            "ℭ": "&Cfr;",
	            "Č": "&Ccaron;",
	            "Ç": "&Ccedil;",
	            "Ĉ": "&Ccirc;",
	            "∰": "&Cconint;",
	            "Ċ": "&Cdot;",
	            "¸": "&cedil;",
	            "·": "&middot;",
	            "Χ": "&Chi;",
	            "⊙": "&odot;",
	            "⊖": "&ominus;",
	            "⊕": "&oplus;",
	            "⊗": "&otimes;",
	            "∲": "&cwconint;",
	            "”": "&rdquor;",
	            "’": "&rsquor;",
	            "∷": "&Proportion;",
	            "⩴": "&Colone;",
	            "≡": "&equiv;",
	            "∯": "&DoubleContourIntegral;",
	            "∮": "&oint;",
	            "ℂ": "&complexes;",
	            "∐": "&coprod;",
	            "∳": "&awconint;",
	            "⨯": "&Cross;",
	            "𝒞": "&Cscr;",
	            "⋓": "&Cup;",
	            "≍": "&asympeq;",
	            "⤑": "&DDotrahd;",
	            "Ђ": "&DJcy;",
	            "Ѕ": "&DScy;",
	            "Џ": "&DZcy;",
	            "‡": "&ddagger;",
	            "↡": "&Darr;",
	            "⫤": "&DoubleLeftTee;",
	            "Ď": "&Dcaron;",
	            "Д": "&Dcy;",
	            "∇": "&nabla;",
	            "Δ": "&Delta;",
	            "𝔇": "&Dfr;",
	            "´": "&acute;",
	            "˙": "&dot;",
	            "˝": "&dblac;",
	            "`": "&grave;",
	            "˜": "&tilde;",
	            "⋄": "&diamond;",
	            "ⅆ": "&dd;",
	            "𝔻": "&Dopf;",
	            "¨": "&uml;",
	            "⃜": "&DotDot;",
	            "≐": "&esdot;",
	            "⇓": "&dArr;",
	            "⇐": "&lArr;",
	            "⇔": "&iff;",
	            "⟸": "&xlArr;",
	            "⟺": "&xhArr;",
	            "⟹": "&xrArr;",
	            "⇒": "&rArr;",
	            "⊨": "&vDash;",
	            "⇑": "&uArr;",
	            "⇕": "&vArr;",
	            "∥": "&spar;",
	            "↓": "&downarrow;",
	            "⤓": "&DownArrowBar;",
	            "⇵": "&duarr;",
	            "̑": "&DownBreve;",
	            "⥐": "&DownLeftRightVector;",
	            "⥞": "&DownLeftTeeVector;",
	            "↽": "&lhard;",
	            "⥖": "&DownLeftVectorBar;",
	            "⥟": "&DownRightTeeVector;",
	            "⇁": "&rightharpoondown;",
	            "⥗": "&DownRightVectorBar;",
	            "⊤": "&top;",
	            "↧": "&mapstodown;",
	            "𝒟": "&Dscr;",
	            "Đ": "&Dstrok;",
	            "Ŋ": "&ENG;",
	            "Ð": "&ETH;",
	            "É": "&Eacute;",
	            "Ě": "&Ecaron;",
	            "Ê": "&Ecirc;",
	            "Э": "&Ecy;",
	            "Ė": "&Edot;",
	            "𝔈": "&Efr;",
	            "È": "&Egrave;",
	            "∈": "&isinv;",
	            "Ē": "&Emacr;",
	            "◻": "&EmptySmallSquare;",
	            "▫": "&EmptyVerySmallSquare;",
	            "Ę": "&Eogon;",
	            "𝔼": "&Eopf;",
	            "Ε": "&Epsilon;",
	            "⩵": "&Equal;",
	            "≂": "&esim;",
	            "⇌": "&rlhar;",
	            "ℰ": "&expectation;",
	            "⩳": "&Esim;",
	            "Η": "&Eta;",
	            "Ë": "&Euml;",
	            "∃": "&exist;",
	            "ⅇ": "&exponentiale;",
	            "Ф": "&Fcy;",
	            "𝔉": "&Ffr;",
	            "◼": "&FilledSmallSquare;",
	            "▪": "&squf;",
	            "𝔽": "&Fopf;",
	            "∀": "&forall;",
	            "ℱ": "&Fscr;",
	            "Ѓ": "&GJcy;",
	            ">": "&gt;",
	            "Γ": "&Gamma;",
	            "Ϝ": "&Gammad;",
	            "Ğ": "&Gbreve;",
	            "Ģ": "&Gcedil;",
	            "Ĝ": "&Gcirc;",
	            "Г": "&Gcy;",
	            "Ġ": "&Gdot;",
	            "𝔊": "&Gfr;",
	            "⋙": "&ggg;",
	            "𝔾": "&Gopf;",
	            "≥": "&geq;",
	            "⋛": "&gtreqless;",
	            "≧": "&geqq;",
	            "⪢": "&GreaterGreater;",
	            "≷": "&gtrless;",
	            "⩾": "&ges;",
	            "≳": "&gtrsim;",
	            "𝒢": "&Gscr;",
	            "≫": "&gg;",
	            "Ъ": "&HARDcy;",
	            "ˇ": "&caron;",
	            "^": "&Hat;",
	            "Ĥ": "&Hcirc;",
	            "ℌ": "&Poincareplane;",
	            "ℋ": "&hamilt;",
	            "ℍ": "&quaternions;",
	            "─": "&boxh;",
	            "Ħ": "&Hstrok;",
	            "≏": "&bumpeq;",
	            "Е": "&IEcy;",
	            "Ĳ": "&IJlig;",
	            "Ё": "&IOcy;",
	            "Í": "&Iacute;",
	            "Î": "&Icirc;",
	            "И": "&Icy;",
	            "İ": "&Idot;",
	            "ℑ": "&imagpart;",
	            "Ì": "&Igrave;",
	            "Ī": "&Imacr;",
	            "ⅈ": "&ii;",
	            "∬": "&Int;",
	            "∫": "&int;",
	            "⋂": "&xcap;",
	            "⁣": "&ic;",
	            "⁢": "&it;",
	            "Į": "&Iogon;",
	            "𝕀": "&Iopf;",
	            "Ι": "&Iota;",
	            "ℐ": "&imagline;",
	            "Ĩ": "&Itilde;",
	            "І": "&Iukcy;",
	            "Ï": "&Iuml;",
	            "Ĵ": "&Jcirc;",
	            "Й": "&Jcy;",
	            "𝔍": "&Jfr;",
	            "𝕁": "&Jopf;",
	            "𝒥": "&Jscr;",
	            "Ј": "&Jsercy;",
	            "Є": "&Jukcy;",
	            "Х": "&KHcy;",
	            "Ќ": "&KJcy;",
	            "Κ": "&Kappa;",
	            "Ķ": "&Kcedil;",
	            "К": "&Kcy;",
	            "𝔎": "&Kfr;",
	            "𝕂": "&Kopf;",
	            "𝒦": "&Kscr;",
	            "Љ": "&LJcy;",
	            "<": "&lt;",
	            "Ĺ": "&Lacute;",
	            "Λ": "&Lambda;",
	            "⟪": "&Lang;",
	            "ℒ": "&lagran;",
	            "↞": "&twoheadleftarrow;",
	            "Ľ": "&Lcaron;",
	            "Ļ": "&Lcedil;",
	            "Л": "&Lcy;",
	            "⟨": "&langle;",
	            "←": "&slarr;",
	            "⇤": "&larrb;",
	            "⇆": "&lrarr;",
	            "⌈": "&lceil;",
	            "⟦": "&lobrk;",
	            "⥡": "&LeftDownTeeVector;",
	            "⇃": "&downharpoonleft;",
	            "⥙": "&LeftDownVectorBar;",
	            "⌊": "&lfloor;",
	            "↔": "&leftrightarrow;",
	            "⥎": "&LeftRightVector;",
	            "⊣": "&dashv;",
	            "↤": "&mapstoleft;",
	            "⥚": "&LeftTeeVector;",
	            "⊲": "&vltri;",
	            "⧏": "&LeftTriangleBar;",
	            "⊴": "&trianglelefteq;",
	            "⥑": "&LeftUpDownVector;",
	            "⥠": "&LeftUpTeeVector;",
	            "↿": "&upharpoonleft;",
	            "⥘": "&LeftUpVectorBar;",
	            "↼": "&lharu;",
	            "⥒": "&LeftVectorBar;",
	            "⋚": "&lesseqgtr;",
	            "≦": "&leqq;",
	            "≶": "&lg;",
	            "⪡": "&LessLess;",
	            "⩽": "&les;",
	            "≲": "&lsim;",
	            "𝔏": "&Lfr;",
	            "⋘": "&Ll;",
	            "⇚": "&lAarr;",
	            "Ŀ": "&Lmidot;",
	            "⟵": "&xlarr;",
	            "⟷": "&xharr;",
	            "⟶": "&xrarr;",
	            "𝕃": "&Lopf;",
	            "↙": "&swarrow;",
	            "↘": "&searrow;",
	            "↰": "&lsh;",
	            "Ł": "&Lstrok;",
	            "≪": "&ll;",
	            "⤅": "&Map;",
	            "М": "&Mcy;",
	            " ": "&MediumSpace;",
	            "ℳ": "&phmmat;",
	            "𝔐": "&Mfr;",
	            "∓": "&mp;",
	            "𝕄": "&Mopf;",
	            "Μ": "&Mu;",
	            "Њ": "&NJcy;",
	            "Ń": "&Nacute;",
	            "Ň": "&Ncaron;",
	            "Ņ": "&Ncedil;",
	            "Н": "&Ncy;",
	            "​": "&ZeroWidthSpace;",
	            "\n": "&NewLine;",
	            "𝔑": "&Nfr;",
	            "⁠": "&NoBreak;",
	            " ": "&nbsp;",
	            "ℕ": "&naturals;",
	            "⫬": "&Not;",
	            "≢": "&nequiv;",
	            "≭": "&NotCupCap;",
	            "∦": "&nspar;",
	            "∉": "&notinva;",
	            "≠": "&ne;",
	            "≂̸": "&nesim;",
	            "∄": "&nexists;",
	            "≯": "&ngtr;",
	            "≱": "&ngeq;",
	            "≧̸": "&ngeqq;",
	            "≫̸": "&nGtv;",
	            "≹": "&ntgl;",
	            "⩾̸": "&nges;",
	            "≵": "&ngsim;",
	            "≎̸": "&nbump;",
	            "≏̸": "&nbumpe;",
	            "⋪": "&ntriangleleft;",
	            "⧏̸": "&NotLeftTriangleBar;",
	            "⋬": "&ntrianglelefteq;",
	            "≮": "&nlt;",
	            "≰": "&nleq;",
	            "≸": "&ntlg;",
	            "≪̸": "&nLtv;",
	            "⩽̸": "&nles;",
	            "≴": "&nlsim;",
	            "⪢̸": "&NotNestedGreaterGreater;",
	            "⪡̸": "&NotNestedLessLess;",
	            "⊀": "&nprec;",
	            "⪯̸": "&npreceq;",
	            "⋠": "&nprcue;",
	            "∌": "&notniva;",
	            "⋫": "&ntriangleright;",
	            "⧐̸": "&NotRightTriangleBar;",
	            "⋭": "&ntrianglerighteq;",
	            "⊏̸": "&NotSquareSubset;",
	            "⋢": "&nsqsube;",
	            "⊐̸": "&NotSquareSuperset;",
	            "⋣": "&nsqsupe;",
	            "⊂⃒": "&vnsub;",
	            "⊈": "&nsubseteq;",
	            "⊁": "&nsucc;",
	            "⪰̸": "&nsucceq;",
	            "⋡": "&nsccue;",
	            "≿̸": "&NotSucceedsTilde;",
	            "⊃⃒": "&vnsup;",
	            "⊉": "&nsupseteq;",
	            "≁": "&nsim;",
	            "≄": "&nsimeq;",
	            "≇": "&ncong;",
	            "≉": "&napprox;",
	            "∤": "&nsmid;",
	            "𝒩": "&Nscr;",
	            "Ñ": "&Ntilde;",
	            "Ν": "&Nu;",
	            "Œ": "&OElig;",
	            "Ó": "&Oacute;",
	            "Ô": "&Ocirc;",
	            "О": "&Ocy;",
	            "Ő": "&Odblac;",
	            "𝔒": "&Ofr;",
	            "Ò": "&Ograve;",
	            "Ō": "&Omacr;",
	            "Ω": "&ohm;",
	            "Ο": "&Omicron;",
	            "𝕆": "&Oopf;",
	            "“": "&ldquo;",
	            "‘": "&lsquo;",
	            "⩔": "&Or;",
	            "𝒪": "&Oscr;",
	            "Ø": "&Oslash;",
	            "Õ": "&Otilde;",
	            "⨷": "&Otimes;",
	            "Ö": "&Ouml;",
	            "‾": "&oline;",
	            "⏞": "&OverBrace;",
	            "⎴": "&tbrk;",
	            "⏜": "&OverParenthesis;",
	            "∂": "&part;",
	            "П": "&Pcy;",
	            "𝔓": "&Pfr;",
	            "Φ": "&Phi;",
	            "Π": "&Pi;",
	            "±": "&pm;",
	            "ℙ": "&primes;",
	            "⪻": "&Pr;",
	            "≺": "&prec;",
	            "⪯": "&preceq;",
	            "≼": "&preccurlyeq;",
	            "≾": "&prsim;",
	            "″": "&Prime;",
	            "∏": "&prod;",
	            "∝": "&vprop;",
	            "𝒫": "&Pscr;",
	            "Ψ": "&Psi;",
	            '"': "&quot;",
	            "𝔔": "&Qfr;",
	            "ℚ": "&rationals;",
	            "𝒬": "&Qscr;",
	            "⤐": "&drbkarow;",
	            "®": "&reg;",
	            "Ŕ": "&Racute;",
	            "⟫": "&Rang;",
	            "↠": "&twoheadrightarrow;",
	            "⤖": "&Rarrtl;",
	            "Ř": "&Rcaron;",
	            "Ŗ": "&Rcedil;",
	            "Р": "&Rcy;",
	            "ℜ": "&realpart;",
	            "∋": "&niv;",
	            "⇋": "&lrhar;",
	            "⥯": "&duhar;",
	            "Ρ": "&Rho;",
	            "⟩": "&rangle;",
	            "→": "&srarr;",
	            "⇥": "&rarrb;",
	            "⇄": "&rlarr;",
	            "⌉": "&rceil;",
	            "⟧": "&robrk;",
	            "⥝": "&RightDownTeeVector;",
	            "⇂": "&downharpoonright;",
	            "⥕": "&RightDownVectorBar;",
	            "⌋": "&rfloor;",
	            "⊢": "&vdash;",
	            "↦": "&mapsto;",
	            "⥛": "&RightTeeVector;",
	            "⊳": "&vrtri;",
	            "⧐": "&RightTriangleBar;",
	            "⊵": "&trianglerighteq;",
	            "⥏": "&RightUpDownVector;",
	            "⥜": "&RightUpTeeVector;",
	            "↾": "&upharpoonright;",
	            "⥔": "&RightUpVectorBar;",
	            "⇀": "&rightharpoonup;",
	            "⥓": "&RightVectorBar;",
	            "ℝ": "&reals;",
	            "⥰": "&RoundImplies;",
	            "⇛": "&rAarr;",
	            "ℛ": "&realine;",
	            "↱": "&rsh;",
	            "⧴": "&RuleDelayed;",
	            "Щ": "&SHCHcy;",
	            "Ш": "&SHcy;",
	            "Ь": "&SOFTcy;",
	            "Ś": "&Sacute;",
	            "⪼": "&Sc;",
	            "Š": "&Scaron;",
	            "Ş": "&Scedil;",
	            "Ŝ": "&Scirc;",
	            "С": "&Scy;",
	            "𝔖": "&Sfr;",
	            "↑": "&uparrow;",
	            "Σ": "&Sigma;",
	            "∘": "&compfn;",
	            "𝕊": "&Sopf;",
	            "√": "&radic;",
	            "□": "&square;",
	            "⊓": "&sqcap;",
	            "⊏": "&sqsubset;",
	            "⊑": "&sqsubseteq;",
	            "⊐": "&sqsupset;",
	            "⊒": "&sqsupseteq;",
	            "⊔": "&sqcup;",
	            "𝒮": "&Sscr;",
	            "⋆": "&sstarf;",
	            "⋐": "&Subset;",
	            "⊆": "&subseteq;",
	            "≻": "&succ;",
	            "⪰": "&succeq;",
	            "≽": "&succcurlyeq;",
	            "≿": "&succsim;",
	            "∑": "&sum;",
	            "⋑": "&Supset;",
	            "⊃": "&supset;",
	            "⊇": "&supseteq;",
	            "Þ": "&THORN;",
	            "™": "&trade;",
	            "Ћ": "&TSHcy;",
	            "Ц": "&TScy;",
	            "\t": "&Tab;",
	            "Τ": "&Tau;",
	            "Ť": "&Tcaron;",
	            "Ţ": "&Tcedil;",
	            "Т": "&Tcy;",
	            "𝔗": "&Tfr;",
	            "∴": "&therefore;",
	            "Θ": "&Theta;",
	            "  ": "&ThickSpace;",
	            " ": "&thinsp;",
	            "∼": "&thksim;",
	            "≃": "&simeq;",
	            "≅": "&cong;",
	            "≈": "&thkap;",
	            "𝕋": "&Topf;",
	            "⃛": "&tdot;",
	            "𝒯": "&Tscr;",
	            "Ŧ": "&Tstrok;",
	            "Ú": "&Uacute;",
	            "↟": "&Uarr;",
	            "⥉": "&Uarrocir;",
	            "Ў": "&Ubrcy;",
	            "Ŭ": "&Ubreve;",
	            "Û": "&Ucirc;",
	            "У": "&Ucy;",
	            "Ű": "&Udblac;",
	            "𝔘": "&Ufr;",
	            "Ù": "&Ugrave;",
	            "Ū": "&Umacr;",
	            _: "&lowbar;",
	            "⏟": "&UnderBrace;",
	            "⎵": "&bbrk;",
	            "⏝": "&UnderParenthesis;",
	            "⋃": "&xcup;",
	            "⊎": "&uplus;",
	            "Ų": "&Uogon;",
	            "𝕌": "&Uopf;",
	            "⤒": "&UpArrowBar;",
	            "⇅": "&udarr;",
	            "↕": "&varr;",
	            "⥮": "&udhar;",
	            "⊥": "&perp;",
	            "↥": "&mapstoup;",
	            "↖": "&nwarrow;",
	            "↗": "&nearrow;",
	            "ϒ": "&upsih;",
	            "Υ": "&Upsilon;",
	            "Ů": "&Uring;",
	            "𝒰": "&Uscr;",
	            "Ũ": "&Utilde;",
	            "Ü": "&Uuml;",
	            "⊫": "&VDash;",
	            "⫫": "&Vbar;",
	            "В": "&Vcy;",
	            "⊩": "&Vdash;",
	            "⫦": "&Vdashl;",
	            "⋁": "&xvee;",
	            "‖": "&Vert;",
	            "∣": "&smid;",
	            "|": "&vert;",
	            "❘": "&VerticalSeparator;",
	            "≀": "&wreath;",
	            " ": "&hairsp;",
	            "𝔙": "&Vfr;",
	            "𝕍": "&Vopf;",
	            "𝒱": "&Vscr;",
	            "⊪": "&Vvdash;",
	            "Ŵ": "&Wcirc;",
	            "⋀": "&xwedge;",
	            "𝔚": "&Wfr;",
	            "𝕎": "&Wopf;",
	            "𝒲": "&Wscr;",
	            "𝔛": "&Xfr;",
	            "Ξ": "&Xi;",
	            "𝕏": "&Xopf;",
	            "𝒳": "&Xscr;",
	            "Я": "&YAcy;",
	            "Ї": "&YIcy;",
	            "Ю": "&YUcy;",
	            "Ý": "&Yacute;",
	            "Ŷ": "&Ycirc;",
	            "Ы": "&Ycy;",
	            "𝔜": "&Yfr;",
	            "𝕐": "&Yopf;",
	            "𝒴": "&Yscr;",
	            "Ÿ": "&Yuml;",
	            "Ж": "&ZHcy;",
	            "Ź": "&Zacute;",
	            "Ž": "&Zcaron;",
	            "З": "&Zcy;",
	            "Ż": "&Zdot;",
	            "Ζ": "&Zeta;",
	            "ℨ": "&zeetrf;",
	            "ℤ": "&integers;",
	            "𝒵": "&Zscr;",
	            "á": "&aacute;",
	            "ă": "&abreve;",
	            "∾": "&mstpos;",
	            "∾̳": "&acE;",
	            "∿": "&acd;",
	            "â": "&acirc;",
	            "а": "&acy;",
	            "æ": "&aelig;",
	            "𝔞": "&afr;",
	            "à": "&agrave;",
	            "ℵ": "&aleph;",
	            "α": "&alpha;",
	            "ā": "&amacr;",
	            "⨿": "&amalg;",
	            "∧": "&wedge;",
	            "⩕": "&andand;",
	            "⩜": "&andd;",
	            "⩘": "&andslope;",
	            "⩚": "&andv;",
	            "∠": "&angle;",
	            "⦤": "&ange;",
	            "∡": "&measuredangle;",
	            "⦨": "&angmsdaa;",
	            "⦩": "&angmsdab;",
	            "⦪": "&angmsdac;",
	            "⦫": "&angmsdad;",
	            "⦬": "&angmsdae;",
	            "⦭": "&angmsdaf;",
	            "⦮": "&angmsdag;",
	            "⦯": "&angmsdah;",
	            "∟": "&angrt;",
	            "⊾": "&angrtvb;",
	            "⦝": "&angrtvbd;",
	            "∢": "&angsph;",
	            "⍼": "&angzarr;",
	            "ą": "&aogon;",
	            "𝕒": "&aopf;",
	            "⩰": "&apE;",
	            "⩯": "&apacir;",
	            "≊": "&approxeq;",
	            "≋": "&apid;",
	            "'": "&apos;",
	            "å": "&aring;",
	            "𝒶": "&ascr;",
	            "*": "&midast;",
	            "ã": "&atilde;",
	            "ä": "&auml;",
	            "⨑": "&awint;",
	            "⫭": "&bNot;",
	            "≌": "&bcong;",
	            "϶": "&bepsi;",
	            "‵": "&bprime;",
	            "∽": "&bsim;",
	            "⋍": "&bsime;",
	            "⊽": "&barvee;",
	            "⌅": "&barwedge;",
	            "⎶": "&bbrktbrk;",
	            "б": "&bcy;",
	            "„": "&ldquor;",
	            "⦰": "&bemptyv;",
	            "β": "&beta;",
	            "ℶ": "&beth;",
	            "≬": "&twixt;",
	            "𝔟": "&bfr;",
	            "◯": "&xcirc;",
	            "⨀": "&xodot;",
	            "⨁": "&xoplus;",
	            "⨂": "&xotime;",
	            "⨆": "&xsqcup;",
	            "★": "&starf;",
	            "▽": "&xdtri;",
	            "△": "&xutri;",
	            "⨄": "&xuplus;",
	            "⤍": "&rbarr;",
	            "⧫": "&lozf;",
	            "▴": "&utrif;",
	            "▾": "&dtrif;",
	            "◂": "&ltrif;",
	            "▸": "&rtrif;",
	            "␣": "&blank;",
	            "▒": "&blk12;",
	            "░": "&blk14;",
	            "▓": "&blk34;",
	            "█": "&block;",
	            "=⃥": "&bne;",
	            "≡⃥": "&bnequiv;",
	            "⌐": "&bnot;",
	            "𝕓": "&bopf;",
	            "⋈": "&bowtie;",
	            "╗": "&boxDL;",
	            "╔": "&boxDR;",
	            "╖": "&boxDl;",
	            "╓": "&boxDr;",
	            "═": "&boxH;",
	            "╦": "&boxHD;",
	            "╩": "&boxHU;",
	            "╤": "&boxHd;",
	            "╧": "&boxHu;",
	            "╝": "&boxUL;",
	            "╚": "&boxUR;",
	            "╜": "&boxUl;",
	            "╙": "&boxUr;",
	            "║": "&boxV;",
	            "╬": "&boxVH;",
	            "╣": "&boxVL;",
	            "╠": "&boxVR;",
	            "╫": "&boxVh;",
	            "╢": "&boxVl;",
	            "╟": "&boxVr;",
	            "⧉": "&boxbox;",
	            "╕": "&boxdL;",
	            "╒": "&boxdR;",
	            "┐": "&boxdl;",
	            "┌": "&boxdr;",
	            "╥": "&boxhD;",
	            "╨": "&boxhU;",
	            "┬": "&boxhd;",
	            "┴": "&boxhu;",
	            "⊟": "&minusb;",
	            "⊞": "&plusb;",
	            "⊠": "&timesb;",
	            "╛": "&boxuL;",
	            "╘": "&boxuR;",
	            "┘": "&boxul;",
	            "└": "&boxur;",
	            "│": "&boxv;",
	            "╪": "&boxvH;",
	            "╡": "&boxvL;",
	            "╞": "&boxvR;",
	            "┼": "&boxvh;",
	            "┤": "&boxvl;",
	            "├": "&boxvr;",
	            "¦": "&brvbar;",
	            "𝒷": "&bscr;",
	            "⁏": "&bsemi;",
	            "\\": "&bsol;",
	            "⧅": "&bsolb;",
	            "⟈": "&bsolhsub;",
	            "•": "&bullet;",
	            "⪮": "&bumpE;",
	            "ć": "&cacute;",
	            "∩": "&cap;",
	            "⩄": "&capand;",
	            "⩉": "&capbrcup;",
	            "⩋": "&capcap;",
	            "⩇": "&capcup;",
	            "⩀": "&capdot;",
	            "∩︀": "&caps;",
	            "⁁": "&caret;",
	            "⩍": "&ccaps;",
	            "č": "&ccaron;",
	            "ç": "&ccedil;",
	            "ĉ": "&ccirc;",
	            "⩌": "&ccups;",
	            "⩐": "&ccupssm;",
	            "ċ": "&cdot;",
	            "⦲": "&cemptyv;",
	            "¢": "&cent;",
	            "𝔠": "&cfr;",
	            "ч": "&chcy;",
	            "✓": "&checkmark;",
	            "χ": "&chi;",
	            "○": "&cir;",
	            "⧃": "&cirE;",
	            "ˆ": "&circ;",
	            "≗": "&cire;",
	            "↺": "&olarr;",
	            "↻": "&orarr;",
	            "Ⓢ": "&oS;",
	            "⊛": "&oast;",
	            "⊚": "&ocir;",
	            "⊝": "&odash;",
	            "⨐": "&cirfnint;",
	            "⫯": "&cirmid;",
	            "⧂": "&cirscir;",
	            "♣": "&clubsuit;",
	            ":": "&colon;",
	            ",": "&comma;",
	            "@": "&commat;",
	            "∁": "&complement;",
	            "⩭": "&congdot;",
	            "𝕔": "&copf;",
	            "℗": "&copysr;",
	            "↵": "&crarr;",
	            "✗": "&cross;",
	            "𝒸": "&cscr;",
	            "⫏": "&csub;",
	            "⫑": "&csube;",
	            "⫐": "&csup;",
	            "⫒": "&csupe;",
	            "⋯": "&ctdot;",
	            "⤸": "&cudarrl;",
	            "⤵": "&cudarrr;",
	            "⋞": "&curlyeqprec;",
	            "⋟": "&curlyeqsucc;",
	            "↶": "&curvearrowleft;",
	            "⤽": "&cularrp;",
	            "∪": "&cup;",
	            "⩈": "&cupbrcap;",
	            "⩆": "&cupcap;",
	            "⩊": "&cupcup;",
	            "⊍": "&cupdot;",
	            "⩅": "&cupor;",
	            "∪︀": "&cups;",
	            "↷": "&curvearrowright;",
	            "⤼": "&curarrm;",
	            "⋎": "&cuvee;",
	            "⋏": "&cuwed;",
	            "¤": "&curren;",
	            "∱": "&cwint;",
	            "⌭": "&cylcty;",
	            "⥥": "&dHar;",
	            "†": "&dagger;",
	            "ℸ": "&daleth;",
	            "‐": "&hyphen;",
	            "⤏": "&rBarr;",
	            "ď": "&dcaron;",
	            "д": "&dcy;",
	            "⇊": "&downdownarrows;",
	            "⩷": "&eDDot;",
	            "°": "&deg;",
	            "δ": "&delta;",
	            "⦱": "&demptyv;",
	            "⥿": "&dfisht;",
	            "𝔡": "&dfr;",
	            "♦": "&diams;",
	            "ϝ": "&gammad;",
	            "⋲": "&disin;",
	            "÷": "&divide;",
	            "⋇": "&divonx;",
	            "ђ": "&djcy;",
	            "⌞": "&llcorner;",
	            "⌍": "&dlcrop;",
	            $: "&dollar;",
	            "𝕕": "&dopf;",
	            "≑": "&eDot;",
	            "∸": "&minusd;",
	            "∔": "&plusdo;",
	            "⊡": "&sdotb;",
	            "⌟": "&lrcorner;",
	            "⌌": "&drcrop;",
	            "𝒹": "&dscr;",
	            "ѕ": "&dscy;",
	            "⧶": "&dsol;",
	            "đ": "&dstrok;",
	            "⋱": "&dtdot;",
	            "▿": "&triangledown;",
	            "⦦": "&dwangle;",
	            "џ": "&dzcy;",
	            "⟿": "&dzigrarr;",
	            "é": "&eacute;",
	            "⩮": "&easter;",
	            "ě": "&ecaron;",
	            "≖": "&eqcirc;",
	            "ê": "&ecirc;",
	            "≕": "&eqcolon;",
	            "э": "&ecy;",
	            "ė": "&edot;",
	            "≒": "&fallingdotseq;",
	            "𝔢": "&efr;",
	            "⪚": "&eg;",
	            "è": "&egrave;",
	            "⪖": "&eqslantgtr;",
	            "⪘": "&egsdot;",
	            "⪙": "&el;",
	            "⏧": "&elinters;",
	            "ℓ": "&ell;",
	            "⪕": "&eqslantless;",
	            "⪗": "&elsdot;",
	            "ē": "&emacr;",
	            "∅": "&varnothing;",
	            " ": "&emsp13;",
	            " ": "&emsp14;",
	            " ": "&emsp;",
	            "ŋ": "&eng;",
	            " ": "&ensp;",
	            "ę": "&eogon;",
	            "𝕖": "&eopf;",
	            "⋕": "&epar;",
	            "⧣": "&eparsl;",
	            "⩱": "&eplus;",
	            "ε": "&epsilon;",
	            "ϵ": "&varepsilon;",
	            "=": "&equals;",
	            "≟": "&questeq;",
	            "⩸": "&equivDD;",
	            "⧥": "&eqvparsl;",
	            "≓": "&risingdotseq;",
	            "⥱": "&erarr;",
	            "ℯ": "&escr;",
	            "η": "&eta;",
	            "ð": "&eth;",
	            "ë": "&euml;",
	            "€": "&euro;",
	            "!": "&excl;",
	            "ф": "&fcy;",
	            "♀": "&female;",
	            "ﬃ": "&ffilig;",
	            "ﬀ": "&fflig;",
	            "ﬄ": "&ffllig;",
	            "𝔣": "&ffr;",
	            "ﬁ": "&filig;",
	            fj: "&fjlig;",
	            "♭": "&flat;",
	            "ﬂ": "&fllig;",
	            "▱": "&fltns;",
	            "ƒ": "&fnof;",
	            "𝕗": "&fopf;",
	            "⋔": "&pitchfork;",
	            "⫙": "&forkv;",
	            "⨍": "&fpartint;",
	            "½": "&half;",
	            "⅓": "&frac13;",
	            "¼": "&frac14;",
	            "⅕": "&frac15;",
	            "⅙": "&frac16;",
	            "⅛": "&frac18;",
	            "⅔": "&frac23;",
	            "⅖": "&frac25;",
	            "¾": "&frac34;",
	            "⅗": "&frac35;",
	            "⅜": "&frac38;",
	            "⅘": "&frac45;",
	            "⅚": "&frac56;",
	            "⅝": "&frac58;",
	            "⅞": "&frac78;",
	            "⁄": "&frasl;",
	            "⌢": "&sfrown;",
	            "𝒻": "&fscr;",
	            "⪌": "&gtreqqless;",
	            "ǵ": "&gacute;",
	            "γ": "&gamma;",
	            "⪆": "&gtrapprox;",
	            "ğ": "&gbreve;",
	            "ĝ": "&gcirc;",
	            "г": "&gcy;",
	            "ġ": "&gdot;",
	            "⪩": "&gescc;",
	            "⪀": "&gesdot;",
	            "⪂": "&gesdoto;",
	            "⪄": "&gesdotol;",
	            "⋛︀": "&gesl;",
	            "⪔": "&gesles;",
	            "𝔤": "&gfr;",
	            "ℷ": "&gimel;",
	            "ѓ": "&gjcy;",
	            "⪒": "&glE;",
	            "⪥": "&gla;",
	            "⪤": "&glj;",
	            "≩": "&gneqq;",
	            "⪊": "&gnapprox;",
	            "⪈": "&gneq;",
	            "⋧": "&gnsim;",
	            "𝕘": "&gopf;",
	            "ℊ": "&gscr;",
	            "⪎": "&gsime;",
	            "⪐": "&gsiml;",
	            "⪧": "&gtcc;",
	            "⩺": "&gtcir;",
	            "⋗": "&gtrdot;",
	            "⦕": "&gtlPar;",
	            "⩼": "&gtquest;",
	            "⥸": "&gtrarr;",
	            "≩︀": "&gvnE;",
	            "ъ": "&hardcy;",
	            "⥈": "&harrcir;",
	            "↭": "&leftrightsquigarrow;",
	            "ℏ": "&plankv;",
	            "ĥ": "&hcirc;",
	            "♥": "&heartsuit;",
	            "…": "&mldr;",
	            "⊹": "&hercon;",
	            "𝔥": "&hfr;",
	            "⤥": "&searhk;",
	            "⤦": "&swarhk;",
	            "⇿": "&hoarr;",
	            "∻": "&homtht;",
	            "↩": "&larrhk;",
	            "↪": "&rarrhk;",
	            "𝕙": "&hopf;",
	            "―": "&horbar;",
	            "𝒽": "&hscr;",
	            "ħ": "&hstrok;",
	            "⁃": "&hybull;",
	            "í": "&iacute;",
	            "î": "&icirc;",
	            "и": "&icy;",
	            "е": "&iecy;",
	            "¡": "&iexcl;",
	            "𝔦": "&ifr;",
	            "ì": "&igrave;",
	            "⨌": "&qint;",
	            "∭": "&tint;",
	            "⧜": "&iinfin;",
	            "℩": "&iiota;",
	            "ĳ": "&ijlig;",
	            "ī": "&imacr;",
	            "ı": "&inodot;",
	            "⊷": "&imof;",
	            "Ƶ": "&imped;",
	            "℅": "&incare;",
	            "∞": "&infin;",
	            "⧝": "&infintie;",
	            "⊺": "&intercal;",
	            "⨗": "&intlarhk;",
	            "⨼": "&iprod;",
	            "ё": "&iocy;",
	            "į": "&iogon;",
	            "𝕚": "&iopf;",
	            "ι": "&iota;",
	            "¿": "&iquest;",
	            "𝒾": "&iscr;",
	            "⋹": "&isinE;",
	            "⋵": "&isindot;",
	            "⋴": "&isins;",
	            "⋳": "&isinsv;",
	            "ĩ": "&itilde;",
	            "і": "&iukcy;",
	            "ï": "&iuml;",
	            "ĵ": "&jcirc;",
	            "й": "&jcy;",
	            "𝔧": "&jfr;",
	            "ȷ": "&jmath;",
	            "𝕛": "&jopf;",
	            "𝒿": "&jscr;",
	            "ј": "&jsercy;",
	            "є": "&jukcy;",
	            "κ": "&kappa;",
	            "ϰ": "&varkappa;",
	            "ķ": "&kcedil;",
	            "к": "&kcy;",
	            "𝔨": "&kfr;",
	            "ĸ": "&kgreen;",
	            "х": "&khcy;",
	            "ќ": "&kjcy;",
	            "𝕜": "&kopf;",
	            "𝓀": "&kscr;",
	            "⤛": "&lAtail;",
	            "⤎": "&lBarr;",
	            "⪋": "&lesseqqgtr;",
	            "⥢": "&lHar;",
	            "ĺ": "&lacute;",
	            "⦴": "&laemptyv;",
	            "λ": "&lambda;",
	            "⦑": "&langd;",
	            "⪅": "&lessapprox;",
	            "«": "&laquo;",
	            "⤟": "&larrbfs;",
	            "⤝": "&larrfs;",
	            "↫": "&looparrowleft;",
	            "⤹": "&larrpl;",
	            "⥳": "&larrsim;",
	            "↢": "&leftarrowtail;",
	            "⪫": "&lat;",
	            "⤙": "&latail;",
	            "⪭": "&late;",
	            "⪭︀": "&lates;",
	            "⤌": "&lbarr;",
	            "❲": "&lbbrk;",
	            "{": "&lcub;",
	            "[": "&lsqb;",
	            "⦋": "&lbrke;",
	            "⦏": "&lbrksld;",
	            "⦍": "&lbrkslu;",
	            "ľ": "&lcaron;",
	            "ļ": "&lcedil;",
	            "л": "&lcy;",
	            "⤶": "&ldca;",
	            "⥧": "&ldrdhar;",
	            "⥋": "&ldrushar;",
	            "↲": "&ldsh;",
	            "≤": "&leq;",
	            "⇇": "&llarr;",
	            "⋋": "&lthree;",
	            "⪨": "&lescc;",
	            "⩿": "&lesdot;",
	            "⪁": "&lesdoto;",
	            "⪃": "&lesdotor;",
	            "⋚︀": "&lesg;",
	            "⪓": "&lesges;",
	            "⋖": "&ltdot;",
	            "⥼": "&lfisht;",
	            "𝔩": "&lfr;",
	            "⪑": "&lgE;",
	            "⥪": "&lharul;",
	            "▄": "&lhblk;",
	            "љ": "&ljcy;",
	            "⥫": "&llhard;",
	            "◺": "&lltri;",
	            "ŀ": "&lmidot;",
	            "⎰": "&lmoustache;",
	            "≨": "&lneqq;",
	            "⪉": "&lnapprox;",
	            "⪇": "&lneq;",
	            "⋦": "&lnsim;",
	            "⟬": "&loang;",
	            "⇽": "&loarr;",
	            "⟼": "&xmap;",
	            "↬": "&rarrlp;",
	            "⦅": "&lopar;",
	            "𝕝": "&lopf;",
	            "⨭": "&loplus;",
	            "⨴": "&lotimes;",
	            "∗": "&lowast;",
	            "◊": "&lozenge;",
	            "(": "&lpar;",
	            "⦓": "&lparlt;",
	            "⥭": "&lrhard;",
	            "‎": "&lrm;",
	            "⊿": "&lrtri;",
	            "‹": "&lsaquo;",
	            "𝓁": "&lscr;",
	            "⪍": "&lsime;",
	            "⪏": "&lsimg;",
	            "‚": "&sbquo;",
	            "ł": "&lstrok;",
	            "⪦": "&ltcc;",
	            "⩹": "&ltcir;",
	            "⋉": "&ltimes;",
	            "⥶": "&ltlarr;",
	            "⩻": "&ltquest;",
	            "⦖": "&ltrPar;",
	            "◃": "&triangleleft;",
	            "⥊": "&lurdshar;",
	            "⥦": "&luruhar;",
	            "≨︀": "&lvnE;",
	            "∺": "&mDDot;",
	            "¯": "&strns;",
	            "♂": "&male;",
	            "✠": "&maltese;",
	            "▮": "&marker;",
	            "⨩": "&mcomma;",
	            "м": "&mcy;",
	            "—": "&mdash;",
	            "𝔪": "&mfr;",
	            "℧": "&mho;",
	            "µ": "&micro;",
	            "⫰": "&midcir;",
	            "−": "&minus;",
	            "⨪": "&minusdu;",
	            "⫛": "&mlcp;",
	            "⊧": "&models;",
	            "𝕞": "&mopf;",
	            "𝓂": "&mscr;",
	            "μ": "&mu;",
	            "⊸": "&mumap;",
	            "⋙̸": "&nGg;",
	            "≫⃒": "&nGt;",
	            "⇍": "&nlArr;",
	            "⇎": "&nhArr;",
	            "⋘̸": "&nLl;",
	            "≪⃒": "&nLt;",
	            "⇏": "&nrArr;",
	            "⊯": "&nVDash;",
	            "⊮": "&nVdash;",
	            "ń": "&nacute;",
	            "∠⃒": "&nang;",
	            "⩰̸": "&napE;",
	            "≋̸": "&napid;",
	            "ŉ": "&napos;",
	            "♮": "&natural;",
	            "⩃": "&ncap;",
	            "ň": "&ncaron;",
	            "ņ": "&ncedil;",
	            "⩭̸": "&ncongdot;",
	            "⩂": "&ncup;",
	            "н": "&ncy;",
	            "–": "&ndash;",
	            "⇗": "&neArr;",
	            "⤤": "&nearhk;",
	            "≐̸": "&nedot;",
	            "⤨": "&toea;",
	            "𝔫": "&nfr;",
	            "↮": "&nleftrightarrow;",
	            "⫲": "&nhpar;",
	            "⋼": "&nis;",
	            "⋺": "&nisd;",
	            "њ": "&njcy;",
	            "≦̸": "&nleqq;",
	            "↚": "&nleftarrow;",
	            "‥": "&nldr;",
	            "𝕟": "&nopf;",
	            "¬": "&not;",
	            "⋹̸": "&notinE;",
	            "⋵̸": "&notindot;",
	            "⋷": "&notinvb;",
	            "⋶": "&notinvc;",
	            "⋾": "&notnivb;",
	            "⋽": "&notnivc;",
	            "⫽⃥": "&nparsl;",
	            "∂̸": "&npart;",
	            "⨔": "&npolint;",
	            "↛": "&nrightarrow;",
	            "⤳̸": "&nrarrc;",
	            "↝̸": "&nrarrw;",
	            "𝓃": "&nscr;",
	            "⊄": "&nsub;",
	            "⫅̸": "&nsubseteqq;",
	            "⊅": "&nsup;",
	            "⫆̸": "&nsupseteqq;",
	            "ñ": "&ntilde;",
	            "ν": "&nu;",
	            "#": "&num;",
	            "№": "&numero;",
	            " ": "&numsp;",
	            "⊭": "&nvDash;",
	            "⤄": "&nvHarr;",
	            "≍⃒": "&nvap;",
	            "⊬": "&nvdash;",
	            "≥⃒": "&nvge;",
	            ">⃒": "&nvgt;",
	            "⧞": "&nvinfin;",
	            "⤂": "&nvlArr;",
	            "≤⃒": "&nvle;",
	            "<⃒": "&nvlt;",
	            "⊴⃒": "&nvltrie;",
	            "⤃": "&nvrArr;",
	            "⊵⃒": "&nvrtrie;",
	            "∼⃒": "&nvsim;",
	            "⇖": "&nwArr;",
	            "⤣": "&nwarhk;",
	            "⤧": "&nwnear;",
	            "ó": "&oacute;",
	            "ô": "&ocirc;",
	            "о": "&ocy;",
	            "ő": "&odblac;",
	            "⨸": "&odiv;",
	            "⦼": "&odsold;",
	            "œ": "&oelig;",
	            "⦿": "&ofcir;",
	            "𝔬": "&ofr;",
	            "˛": "&ogon;",
	            "ò": "&ograve;",
	            "⧁": "&ogt;",
	            "⦵": "&ohbar;",
	            "⦾": "&olcir;",
	            "⦻": "&olcross;",
	            "⧀": "&olt;",
	            "ō": "&omacr;",
	            "ω": "&omega;",
	            "ο": "&omicron;",
	            "⦶": "&omid;",
	            "𝕠": "&oopf;",
	            "⦷": "&opar;",
	            "⦹": "&operp;",
	            "∨": "&vee;",
	            "⩝": "&ord;",
	            "ℴ": "&oscr;",
	            "ª": "&ordf;",
	            "º": "&ordm;",
	            "⊶": "&origof;",
	            "⩖": "&oror;",
	            "⩗": "&orslope;",
	            "⩛": "&orv;",
	            "ø": "&oslash;",
	            "⊘": "&osol;",
	            "õ": "&otilde;",
	            "⨶": "&otimesas;",
	            "ö": "&ouml;",
	            "⌽": "&ovbar;",
	            "¶": "&para;",
	            "⫳": "&parsim;",
	            "⫽": "&parsl;",
	            "п": "&pcy;",
	            "%": "&percnt;",
	            ".": "&period;",
	            "‰": "&permil;",
	            "‱": "&pertenk;",
	            "𝔭": "&pfr;",
	            "φ": "&phi;",
	            "ϕ": "&varphi;",
	            "☎": "&phone;",
	            "π": "&pi;",
	            "ϖ": "&varpi;",
	            "ℎ": "&planckh;",
	            "+": "&plus;",
	            "⨣": "&plusacir;",
	            "⨢": "&pluscir;",
	            "⨥": "&plusdu;",
	            "⩲": "&pluse;",
	            "⨦": "&plussim;",
	            "⨧": "&plustwo;",
	            "⨕": "&pointint;",
	            "𝕡": "&popf;",
	            "£": "&pound;",
	            "⪳": "&prE;",
	            "⪷": "&precapprox;",
	            "⪹": "&prnap;",
	            "⪵": "&prnE;",
	            "⋨": "&prnsim;",
	            "′": "&prime;",
	            "⌮": "&profalar;",
	            "⌒": "&profline;",
	            "⌓": "&profsurf;",
	            "⊰": "&prurel;",
	            "𝓅": "&pscr;",
	            "ψ": "&psi;",
	            " ": "&puncsp;",
	            "𝔮": "&qfr;",
	            "𝕢": "&qopf;",
	            "⁗": "&qprime;",
	            "𝓆": "&qscr;",
	            "⨖": "&quatint;",
	            "?": "&quest;",
	            "⤜": "&rAtail;",
	            "⥤": "&rHar;",
	            "∽̱": "&race;",
	            "ŕ": "&racute;",
	            "⦳": "&raemptyv;",
	            "⦒": "&rangd;",
	            "⦥": "&range;",
	            "»": "&raquo;",
	            "⥵": "&rarrap;",
	            "⤠": "&rarrbfs;",
	            "⤳": "&rarrc;",
	            "⤞": "&rarrfs;",
	            "⥅": "&rarrpl;",
	            "⥴": "&rarrsim;",
	            "↣": "&rightarrowtail;",
	            "↝": "&rightsquigarrow;",
	            "⤚": "&ratail;",
	            "∶": "&ratio;",
	            "❳": "&rbbrk;",
	            "}": "&rcub;",
	            "]": "&rsqb;",
	            "⦌": "&rbrke;",
	            "⦎": "&rbrksld;",
	            "⦐": "&rbrkslu;",
	            "ř": "&rcaron;",
	            "ŗ": "&rcedil;",
	            "р": "&rcy;",
	            "⤷": "&rdca;",
	            "⥩": "&rdldhar;",
	            "↳": "&rdsh;",
	            "▭": "&rect;",
	            "⥽": "&rfisht;",
	            "𝔯": "&rfr;",
	            "⥬": "&rharul;",
	            "ρ": "&rho;",
	            "ϱ": "&varrho;",
	            "⇉": "&rrarr;",
	            "⋌": "&rthree;",
	            "˚": "&ring;",
	            "‏": "&rlm;",
	            "⎱": "&rmoustache;",
	            "⫮": "&rnmid;",
	            "⟭": "&roang;",
	            "⇾": "&roarr;",
	            "⦆": "&ropar;",
	            "𝕣": "&ropf;",
	            "⨮": "&roplus;",
	            "⨵": "&rotimes;",
	            ")": "&rpar;",
	            "⦔": "&rpargt;",
	            "⨒": "&rppolint;",
	            "›": "&rsaquo;",
	            "𝓇": "&rscr;",
	            "⋊": "&rtimes;",
	            "▹": "&triangleright;",
	            "⧎": "&rtriltri;",
	            "⥨": "&ruluhar;",
	            "℞": "&rx;",
	            "ś": "&sacute;",
	            "⪴": "&scE;",
	            "⪸": "&succapprox;",
	            "š": "&scaron;",
	            "ş": "&scedil;",
	            "ŝ": "&scirc;",
	            "⪶": "&succneqq;",
	            "⪺": "&succnapprox;",
	            "⋩": "&succnsim;",
	            "⨓": "&scpolint;",
	            "с": "&scy;",
	            "⋅": "&sdot;",
	            "⩦": "&sdote;",
	            "⇘": "&seArr;",
	            "§": "&sect;",
	            ";": "&semi;",
	            "⤩": "&tosa;",
	            "✶": "&sext;",
	            "𝔰": "&sfr;",
	            "♯": "&sharp;",
	            "щ": "&shchcy;",
	            "ш": "&shcy;",
	            "­": "&shy;",
	            "σ": "&sigma;",
	            "ς": "&varsigma;",
	            "⩪": "&simdot;",
	            "⪞": "&simg;",
	            "⪠": "&simgE;",
	            "⪝": "&siml;",
	            "⪟": "&simlE;",
	            "≆": "&simne;",
	            "⨤": "&simplus;",
	            "⥲": "&simrarr;",
	            "⨳": "&smashp;",
	            "⧤": "&smeparsl;",
	            "⌣": "&ssmile;",
	            "⪪": "&smt;",
	            "⪬": "&smte;",
	            "⪬︀": "&smtes;",
	            "ь": "&softcy;",
	            "/": "&sol;",
	            "⧄": "&solb;",
	            "⌿": "&solbar;",
	            "𝕤": "&sopf;",
	            "♠": "&spadesuit;",
	            "⊓︀": "&sqcaps;",
	            "⊔︀": "&sqcups;",
	            "𝓈": "&sscr;",
	            "☆": "&star;",
	            "⊂": "&subset;",
	            "⫅": "&subseteqq;",
	            "⪽": "&subdot;",
	            "⫃": "&subedot;",
	            "⫁": "&submult;",
	            "⫋": "&subsetneqq;",
	            "⊊": "&subsetneq;",
	            "⪿": "&subplus;",
	            "⥹": "&subrarr;",
	            "⫇": "&subsim;",
	            "⫕": "&subsub;",
	            "⫓": "&subsup;",
	            "♪": "&sung;",
	            "¹": "&sup1;",
	            "²": "&sup2;",
	            "³": "&sup3;",
	            "⫆": "&supseteqq;",
	            "⪾": "&supdot;",
	            "⫘": "&supdsub;",
	            "⫄": "&supedot;",
	            "⟉": "&suphsol;",
	            "⫗": "&suphsub;",
	            "⥻": "&suplarr;",
	            "⫂": "&supmult;",
	            "⫌": "&supsetneqq;",
	            "⊋": "&supsetneq;",
	            "⫀": "&supplus;",
	            "⫈": "&supsim;",
	            "⫔": "&supsub;",
	            "⫖": "&supsup;",
	            "⇙": "&swArr;",
	            "⤪": "&swnwar;",
	            "ß": "&szlig;",
	            "⌖": "&target;",
	            "τ": "&tau;",
	            "ť": "&tcaron;",
	            "ţ": "&tcedil;",
	            "т": "&tcy;",
	            "⌕": "&telrec;",
	            "𝔱": "&tfr;",
	            "θ": "&theta;",
	            "ϑ": "&vartheta;",
	            "þ": "&thorn;",
	            "×": "&times;",
	            "⨱": "&timesbar;",
	            "⨰": "&timesd;",
	            "⌶": "&topbot;",
	            "⫱": "&topcir;",
	            "𝕥": "&topf;",
	            "⫚": "&topfork;",
	            "‴": "&tprime;",
	            "▵": "&utri;",
	            "≜": "&trie;",
	            "◬": "&tridot;",
	            "⨺": "&triminus;",
	            "⨹": "&triplus;",
	            "⧍": "&trisb;",
	            "⨻": "&tritime;",
	            "⏢": "&trpezium;",
	            "𝓉": "&tscr;",
	            "ц": "&tscy;",
	            "ћ": "&tshcy;",
	            "ŧ": "&tstrok;",
	            "⥣": "&uHar;",
	            "ú": "&uacute;",
	            "ў": "&ubrcy;",
	            "ŭ": "&ubreve;",
	            "û": "&ucirc;",
	            "у": "&ucy;",
	            "ű": "&udblac;",
	            "⥾": "&ufisht;",
	            "𝔲": "&ufr;",
	            "ù": "&ugrave;",
	            "▀": "&uhblk;",
	            "⌜": "&ulcorner;",
	            "⌏": "&ulcrop;",
	            "◸": "&ultri;",
	            "ū": "&umacr;",
	            "ų": "&uogon;",
	            "𝕦": "&uopf;",
	            "υ": "&upsilon;",
	            "⇈": "&uuarr;",
	            "⌝": "&urcorner;",
	            "⌎": "&urcrop;",
	            "ů": "&uring;",
	            "◹": "&urtri;",
	            "𝓊": "&uscr;",
	            "⋰": "&utdot;",
	            "ũ": "&utilde;",
	            "ü": "&uuml;",
	            "⦧": "&uwangle;",
	            "⫨": "&vBar;",
	            "⫩": "&vBarv;",
	            "⦜": "&vangrt;",
	            "⊊︀": "&vsubne;",
	            "⫋︀": "&vsubnE;",
	            "⊋︀": "&vsupne;",
	            "⫌︀": "&vsupnE;",
	            "в": "&vcy;",
	            "⊻": "&veebar;",
	            "≚": "&veeeq;",
	            "⋮": "&vellip;",
	            "𝔳": "&vfr;",
	            "𝕧": "&vopf;",
	            "𝓋": "&vscr;",
	            "⦚": "&vzigzag;",
	            "ŵ": "&wcirc;",
	            "⩟": "&wedbar;",
	            "≙": "&wedgeq;",
	            "℘": "&wp;",
	            "𝔴": "&wfr;",
	            "𝕨": "&wopf;",
	            "𝓌": "&wscr;",
	            "𝔵": "&xfr;",
	            "ξ": "&xi;",
	            "⋻": "&xnis;",
	            "𝕩": "&xopf;",
	            "𝓍": "&xscr;",
	            "ý": "&yacute;",
	            "я": "&yacy;",
	            "ŷ": "&ycirc;",
	            "ы": "&ycy;",
	            "¥": "&yen;",
	            "𝔶": "&yfr;",
	            "ї": "&yicy;",
	            "𝕪": "&yopf;",
	            "𝓎": "&yscr;",
	            "ю": "&yucy;",
	            "ÿ": "&yuml;",
	            "ź": "&zacute;",
	            "ž": "&zcaron;",
	            "з": "&zcy;",
	            "ż": "&zdot;",
	            "ζ": "&zeta;",
	            "𝔷": "&zfr;",
	            "ж": "&zhcy;",
	            "⇝": "&zigrarr;",
	            "𝕫": "&zopf;",
	            "𝓏": "&zscr;",
	            "‍": "&zwj;",
	            "‌": "&zwnj;"
	        }
	    }
	}; 
	return namedReferences;
}

var numericUnicodeMap = {};

var hasRequiredNumericUnicodeMap;

function requireNumericUnicodeMap () {
	if (hasRequiredNumericUnicodeMap) return numericUnicodeMap;
	hasRequiredNumericUnicodeMap = 1;
	Object.defineProperty(numericUnicodeMap, "__esModule", {
	    value: true
	});
	numericUnicodeMap.numericUnicodeMap = {
	    0: 65533,
	    128: 8364,
	    130: 8218,
	    131: 402,
	    132: 8222,
	    133: 8230,
	    134: 8224,
	    135: 8225,
	    136: 710,
	    137: 8240,
	    138: 352,
	    139: 8249,
	    140: 338,
	    142: 381,
	    145: 8216,
	    146: 8217,
	    147: 8220,
	    148: 8221,
	    149: 8226,
	    150: 8211,
	    151: 8212,
	    152: 732,
	    153: 8482,
	    154: 353,
	    155: 8250,
	    156: 339,
	    158: 382,
	    159: 376
	}; 
	return numericUnicodeMap;
}

var surrogatePairs = {};

var hasRequiredSurrogatePairs;

function requireSurrogatePairs () {
	if (hasRequiredSurrogatePairs) return surrogatePairs;
	hasRequiredSurrogatePairs = 1;
	Object.defineProperty(surrogatePairs, "__esModule", {
	    value: true
	});
	surrogatePairs.fromCodePoint = String.fromCodePoint || function(astralCodePoint) {
	    return String.fromCharCode(Math.floor((astralCodePoint - 65536) / 1024) + 55296, (astralCodePoint - 65536) % 1024 + 56320);
	};
	surrogatePairs.getCodePoint = String.prototype.codePointAt ? function(input, position) {
	    return input.codePointAt(position);
	} : function(input, position) {
	    return (input.charCodeAt(position) - 55296) * 1024 + input.charCodeAt(position + 1) - 56320 + 65536;
	};
	surrogatePairs.highSurrogateFrom = 55296;
	surrogatePairs.highSurrogateTo = 56319; 
	return surrogatePairs;
}

var hasRequiredLib;

function requireLib () {
	if (hasRequiredLib) return lib;
	hasRequiredLib = 1;
	var __assign = lib && lib.__assign || function() {
	    __assign = Object.assign || function(t) {
	        for(var s, i = 1, n = arguments.length; i < n; i++){
	            s = arguments[i];
	            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
	        }
	        return t;
	    };
	    return __assign.apply(this, arguments);
	};
	Object.defineProperty(lib, "__esModule", {
	    value: true
	});
	var named_references_1 = requireNamedReferences();
	var numeric_unicode_map_1 = requireNumericUnicodeMap();
	var surrogate_pairs_1 = requireSurrogatePairs();
	var allNamedReferences = __assign(__assign({}, named_references_1.namedReferences), {
	    all: named_references_1.namedReferences.html5
	});
	function replaceUsingRegExp(macroText, macroRegExp, macroReplacer) {
	    macroRegExp.lastIndex = 0;
	    var replaceMatch = macroRegExp.exec(macroText);
	    var replaceResult;
	    if (replaceMatch) {
	        replaceResult = "";
	        var replaceLastIndex = 0;
	        do {
	            if (replaceLastIndex !== replaceMatch.index) {
	                replaceResult += macroText.substring(replaceLastIndex, replaceMatch.index);
	            }
	            var replaceInput = replaceMatch[0];
	            replaceResult += macroReplacer(replaceInput);
	            replaceLastIndex = replaceMatch.index + replaceInput.length;
	        }while (replaceMatch = macroRegExp.exec(macroText))
	        if (replaceLastIndex !== macroText.length) {
	            replaceResult += macroText.substring(replaceLastIndex);
	        }
	    } else {
	        replaceResult = macroText;
	    }
	    return replaceResult;
	}
	var encodeRegExps = {
	    specialChars: /[<>'"&]/g,
	    nonAscii: /[<>'"&\u0080-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,
	    nonAsciiPrintable: /[<>'"&\x01-\x08\x11-\x15\x17-\x1F\x7f-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,
	    nonAsciiPrintableOnly: /[\x01-\x08\x11-\x15\x17-\x1F\x7f-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,
	    extensive: /[\x01-\x0c\x0e-\x1f\x21-\x2c\x2e-\x2f\x3a-\x40\x5b-\x60\x7b-\x7d\x7f-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g
	};
	var defaultEncodeOptions = {
	    mode: "specialChars",
	    level: "all",
	    numeric: "decimal"
	};
	function encode(text, _a) {
	    var _b = _a === void 0 ? defaultEncodeOptions : _a, _c = _b.mode, mode = _c === void 0 ? "specialChars" : _c, _d = _b.numeric, numeric = _d === void 0 ? "decimal" : _d, _e = _b.level, level = _e === void 0 ? "all" : _e;
	    if (!text) {
	        return "";
	    }
	    var encodeRegExp = encodeRegExps[mode];
	    var references = allNamedReferences[level].characters;
	    var isHex = numeric === "hexadecimal";
	    return replaceUsingRegExp(text, encodeRegExp, function(input) {
	        var result = references[input];
	        if (!result) {
	            var code = input.length > 1 ? surrogate_pairs_1.getCodePoint(input, 0) : input.charCodeAt(0);
	            result = (isHex ? "&#x" + code.toString(16) : "&#" + code) + ";";
	        }
	        return result;
	    });
	}
	lib.encode = encode;
	var defaultDecodeOptions = {
	    scope: "body",
	    level: "all"
	};
	var strict = /&(?:#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+);/g;
	var attribute = /&(?:#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+)[;=]?/g;
	var baseDecodeRegExps = {
	    xml: {
	        strict: strict,
	        attribute: attribute,
	        body: named_references_1.bodyRegExps.xml
	    },
	    html4: {
	        strict: strict,
	        attribute: attribute,
	        body: named_references_1.bodyRegExps.html4
	    },
	    html5: {
	        strict: strict,
	        attribute: attribute,
	        body: named_references_1.bodyRegExps.html5
	    }
	};
	var decodeRegExps = __assign(__assign({}, baseDecodeRegExps), {
	    all: baseDecodeRegExps.html5
	});
	var fromCharCode = String.fromCharCode;
	var outOfBoundsChar = fromCharCode(65533);
	var defaultDecodeEntityOptions = {
	    level: "all"
	};
	function getDecodedEntity(entity, references, isAttribute, isStrict) {
	    var decodeResult = entity;
	    var decodeEntityLastChar = entity[entity.length - 1];
	    if (isAttribute && decodeEntityLastChar === "=") {
	        decodeResult = entity;
	    } else if (isStrict && decodeEntityLastChar !== ";") {
	        decodeResult = entity;
	    } else {
	        var decodeResultByReference = references[entity];
	        if (decodeResultByReference) {
	            decodeResult = decodeResultByReference;
	        } else if (entity[0] === "&" && entity[1] === "#") {
	            var decodeSecondChar = entity[2];
	            var decodeCode = decodeSecondChar == "x" || decodeSecondChar == "X" ? parseInt(entity.substr(3), 16) : parseInt(entity.substr(2));
	            decodeResult = decodeCode >= 1114111 ? outOfBoundsChar : decodeCode > 65535 ? surrogate_pairs_1.fromCodePoint(decodeCode) : fromCharCode(numeric_unicode_map_1.numericUnicodeMap[decodeCode] || decodeCode);
	        }
	    }
	    return decodeResult;
	}
	function decodeEntity(entity, _a) {
	    var _b = (_a === void 0 ? defaultDecodeEntityOptions : _a).level, level = _b === void 0 ? "all" : _b;
	    if (!entity) {
	        return "";
	    }
	    return getDecodedEntity(entity, allNamedReferences[level].entities, false, false);
	}
	lib.decodeEntity = decodeEntity;
	function decode(text, _a) {
	    var _b = _a === void 0 ? defaultDecodeOptions : _a, _c = _b.level, level = _c === void 0 ? "all" : _c, _d = _b.scope, scope = _d === void 0 ? level === "xml" ? "strict" : "body" : _d;
	    if (!text) {
	        return "";
	    }
	    var decodeRegExp = decodeRegExps[level][scope];
	    var references = allNamedReferences[level].entities;
	    var isAttribute = scope === "attribute";
	    var isStrict = scope === "strict";
	    return replaceUsingRegExp(text, decodeRegExp, function(entity) {
	        return getDecodedEntity(entity, references, isAttribute, isStrict);
	    });
	}
	lib.decode = decode; 
	return lib;
}

var libExports = /*@__PURE__*/ requireLib();

var d$1 = {
    strictlyTwoElementsInRangeArrays: false,
    progressFn: null
};
function g(t, u) {
    if (!Array.isArray(t) || !t.length) return t;
    let n = {
        ...d$1,
        ...u
    }, s, o;
    if (n.strictlyTwoElementsInRangeArrays && !t.every((e, r)=>!Array.isArray(e) || e.length !== 2 ? (s = r, o = e.length, false) : true)) throw new TypeError(`ranges-sort: [THROW_ID_03] The first argument should be an array and must consist of arrays which are natural number indexes representing TWO string index ranges. However, ${s}th range (${JSON.stringify(t[s], null, 4)}) has not two but ${o} elements!`);
    if (!t.every((e, r)=>!Array.isArray(e) || !Number.isInteger(e[0]) || e[0] < 0 || !Number.isInteger(e[1]) || e[1] < 0 ? (s = r, false) : true)) throw new TypeError(`ranges-sort: [THROW_ID_04] The first argument should be an array and must consist of arrays which are natural number indexes representing string index ranges. However, ${s}th range (${JSON.stringify(t[s], null, 4)}) does not consist of only natural numbers!`);
    let p = t.length ** 2, i = 0;
    return Array.from(t).sort((e, r)=>(n.progressFn && (i += 1, n.progressFn(Math.floor(i * 100 / p))), e[0] === r[0] ? e[1] < r[1] ? -1 : e[1] > r[1] ? 1 : 0 : e[0] < r[0] ? -1 : 1));
}

var d = {
    mergeType: 1,
    progressFn: null,
    joinRangesThatTouchEdges: true
};
function b$1(i, r) {
    function l(e) {
        return !!e && typeof e == "object" && !Array.isArray(e);
    }
    if (!Array.isArray(i) || !i.length) return null;
    let n;
    if (r) if (l(r)) {
        if (n = {
            ...d,
            ...r
        }, n.progressFn && l(n.progressFn) && !Object.keys(n.progressFn).length) n.progressFn = null;
        else if (n.progressFn && typeof n.progressFn != "function") throw new Error(`ranges-merge: [THROW_ID_01] opts.progressFn must be a function! It was given of a type: "${typeof n.progressFn}", equal to ${JSON.stringify(n.progressFn, null, 4)}`);
        if (![
            1,
            2,
            "1",
            "2"
        ].includes(n.mergeType)) throw new Error(`ranges-merge: [THROW_ID_02] opts.mergeType was customised to a wrong thing! It was given of a type: "${typeof n.mergeType}", equal to ${JSON.stringify(n.mergeType, null, 4)}`);
        if (typeof n.joinRangesThatTouchEdges != "boolean") throw new Error(`ranges-merge: [THROW_ID_04] opts.joinRangesThatTouchEdges was customised to a wrong thing! It was given of a type: "${typeof n.joinRangesThatTouchEdges}", equal to ${JSON.stringify(n.joinRangesThatTouchEdges, null, 4)}`);
    } else throw new Error(`emlint: [THROW_ID_03] the second input argument must be a plain object. It was given as:
${JSON.stringify(r, null, 4)} (type ${typeof r})`);
    else n = {
        ...d
    };
    let g$1 = i.filter((e)=>Array.isArray(e)).map((e)=>[
            ...e
        ]).filter((e)=>e[2] !== void 0 || e[0] !== e[1]), s, o, t;
    n.progressFn ? s = g(g$1, {
        progressFn: (e)=>{
            t = Math.floor(e / 5), t !== o && (o = t, n.progressFn(t));
        }
    }) : s = g(g$1);
    let a = s.length - 1;
    for(let e = a; e > 0; e--)n.progressFn && (t = Math.floor((1 - e / a) * 78) + 21, t !== o && t > o && (o = t, n.progressFn(t))), (s[e][0] <= s[e - 1][0] || !n.joinRangesThatTouchEdges && s[e][0] < s[e - 1][1] || n.joinRangesThatTouchEdges && s[e][0] <= s[e - 1][1]) && (s[e - 1][0] = Math.min(s[e][0], s[e - 1][0]), s[e - 1][1] = Math.max(s[e][1], s[e - 1][1]), s[e][2] !== void 0 && (s[e - 1][0] >= s[e][0] || s[e - 1][1] <= s[e][1]) && s[e - 1][2] !== null && (s[e][2] === null && s[e - 1][2] !== null ? s[e - 1][2] = null : s[e - 1][2] != null ? +n.mergeType == 2 && s[e - 1][0] === s[e][0] ? s[e - 1][2] = s[e][2] : s[e - 1][2] += s[e][2] : s[e - 1][2] = s[e][2]), s.splice(e, 1), e = s.length);
    return s.length ? s : null;
}

var isProduction = process.env.NODE_ENV === 'production';
var prefix = 'Invariant failed';
function invariant(condition, message) {
    if (condition) {
        return;
    }
    if (isProduction) {
        throw new Error(prefix);
    }
    var value = prefix;
    throw new Error(value);
}

function _(s, n, r) {
    if (arguments.length === 0) throw new Error("ranges-apply: [THROW_ID_01] inputs missing!");
    if (typeof s != "string") throw new TypeError(`ranges-apply: [THROW_ID_02] first input argument must be a string! Currently it's: ${typeof s}, equal to: ${JSON.stringify(s, null, 4)}`);
    if (n && !Array.isArray(n)) throw new TypeError(`ranges-apply: [THROW_ID_03] second input argument must be an array (or null)! Currently it's: ${typeof n}, equal to: ${JSON.stringify(n, null, 4)}`);
    if (!n?.filter((e)=>e).length) return s;
    let i;
    Array.isArray(n) && Number.isInteger(n[0]) && Number.isInteger(n[1]) ? i = [
        Array.from(n)
    ] : i = Array.from(n);
    i.length;
    i.filter((e)=>e).forEach((e, a)=>{
        if (!Array.isArray(e)) throw new TypeError(`ranges-apply: [THROW_ID_05] ranges array, second input arg., has ${a}th element not an array: ${JSON.stringify(e, null, 4)}, which is ${typeof e}`);
        if (!Number.isInteger(e[0])) {
            if (!Number.isInteger(+e[0]) || +e[0] < 0) throw new TypeError(`ranges-apply: [THROW_ID_06] ranges array, second input arg. has ${a}th element, array ${JSON.stringify(e, null, 0)}. Its first element is not an integer, string index, but ${typeof e[0]}, equal to: ${JSON.stringify(e[0], null, 4)}.`);
            i[a][0] = +i[a][0];
        }
        if (!Number.isInteger(e[1])) {
            if (!Number.isInteger(+e[1]) || +e[1] < 0) throw new TypeError(`ranges-apply: [THROW_ID_07] ranges array, second input arg. has ${a}th element, array ${JSON.stringify(e, null, 0)}. Its second element is not an integer, string index, but ${typeof e[1]}, equal to: ${JSON.stringify(e[1], null, 4)}.`);
            i[a][1] = +i[a][1];
        }
    });
    let o = b$1(i, {
        progressFn: (e)=>{
        }
    });
    invariant(o);
    let u = o.length;
    if (u > 0) {
        let e = s.slice(o[u - 1][1]);
        s = o.reduce((a, $, l, y)=>{
            let g = l === 0 ? 0 : y[l - 1][1], d = y[l][0];
            return `${a}${s.slice(g, d)}${y[l][2] || ""}`;
        }, ""), s += e;
    }
    return s;
}

function D$1(n, u = 1) {
    let $ = "\xA0";
    function g(e) {
        return Array.from(e).reverse().join("");
    }
    function p(e, s, r) {
        let t = r ? `
` : "\r", i = r ? "\r" : `
`;
        if (!e) return e;
        let c = 0, o = "";
        for(let l = 0, f = e.length; l < f; l++)(e[l] === t || e[l] === i && e[l - 1] !== t) && c++, `\r
`.includes(e[l]) || e[l] === $ ? (e[l] === $ ? o += e[l] : e[l] === t ? c <= s && (o += e[l], e[l + 1] === i && (o += e[l + 1], l++)) : e[l] === i && e?.[l - 1] !== t && c <= s && (o += e[l])) : (!e[l + 1] && !c && (o += " "));
        return o;
    }
    if (typeof n == "string" && n.length) {
        let e = 1;
        typeof +u == "number" && Number.isInteger(+u) && +u >= 0 && (e = +u);
        let s = "", r = "";
        if (!n.trim()) s = n;
        else if (!n[0].trim()) {
            for(let t = 0, i = n.length; t < i; t++)if (n[t].trim()) {
                s = n.slice(0, t);
                break;
            }
        }
        if (n.trim() && (n.slice(-1).trim() === "" || n.slice(-1) === $)) {
            for(let t = n.length; t--;)if (n[t].trim()) {
                r = n.slice(t + 1);
                break;
            }
        }
        return `${p(s, e, false)}${n.trim()}${g(p(g(r), e, true))}`;
    }
    return n;
}

var y = {
    mergeType: 1,
    progressFn: null,
    joinRangesThatTouchEdges: true
};
function f(p, t) {
    function r(e) {
        return !!e && typeof e == "object" && !Array.isArray(e);
    }
    if (!Array.isArray(p) || !p.length) return null;
    let s;
    if (t) if (r(t)) {
        if (s = {
            ...y,
            ...t
        }, s.progressFn && r(s.progressFn) && !Object.keys(s.progressFn).length) s.progressFn = null;
        else if (s.progressFn && typeof s.progressFn != "function") throw new Error(`ranges-merge: [THROW_ID_01] resolvedOpts.progressFn must be a function! It was given of a type: "${typeof s.progressFn}", equal to ${JSON.stringify(s.progressFn, null, 4)}`);
        if (![
            1,
            2,
            "1",
            "2"
        ].includes(s.mergeType)) throw new Error(`ranges-merge: [THROW_ID_02] resolvedOpts.mergeType was customised to a wrong thing! It was given of a type: "${typeof s.mergeType}", equal to ${JSON.stringify(s.mergeType, null, 4)}`);
        if (typeof s.joinRangesThatTouchEdges != "boolean") throw new Error(`ranges-merge: [THROW_ID_04] resolvedOpts.joinRangesThatTouchEdges was customised to a wrong thing! It was given of a type: "${typeof s.joinRangesThatTouchEdges}", equal to ${JSON.stringify(s.joinRangesThatTouchEdges, null, 4)}`);
    } else throw new Error(`ranges-merge: [THROW_ID_03] the second input argument must be a plain object. It was given as:
${JSON.stringify(t, null, 4)} (type ${typeof t})`);
    else s = {
        ...y
    };
    let i = p.filter((e)=>Array.isArray(e)).map((e)=>[
            ...e
        ]).filter((e)=>e[2] !== void 0 || e[0] !== e[1]), n, o, l;
    s.progressFn ? n = g(i, {
        progressFn: (e)=>{
            l = Math.floor(e / 5), l !== o && (o = l, s.progressFn != null && s.progressFn(l));
        }
    }) : n = g(i);
    let h = n.length - 1;
    for(let e = h; e > 0; e--)s.progressFn && (l = Math.floor((1 - e / h) * 78) + 21, l !== o && l > o && (o = l, s.progressFn(l))), (n[e][0] <= n[e - 1][0] || !s.joinRangesThatTouchEdges && n[e][0] < n[e - 1][1] || s.joinRangesThatTouchEdges && n[e][0] <= n[e - 1][1]) && (n[e - 1][0] = Math.min(n[e][0], n[e - 1][0]), n[e - 1][1] = Math.max(n[e][1], n[e - 1][1]), n[e][2] !== void 0 && (n[e - 1][0] >= n[e][0] || n[e - 1][1] <= n[e][1]) && n[e - 1][2] !== null && (n[e][2] === null && n[e - 1][2] !== null ? n[e - 1][2] = null : n[e - 1][2] != null ? +(s || {})?.mergeType == 2 && n[e - 1][0] === n[e][0] ? n[e - 1][2] = n[e][2] : n[e - 1][2] += n[e][2] : n[e - 1][2] = n[e][2]), n.splice(e, 1), e = n.length);
    return n.length ? n : null;
}
var b = {
    limitToBeAddedWhitespace: false,
    limitLinebreaksCount: 1,
    mergeType: 1
}, $ = class {
    constructor(t){
        let r$1 = {
            ...b,
            ...t
        };
        if (r$1.mergeType && r$1.mergeType !== 1 && r$1.mergeType !== 2) if (r(r$1.mergeType) && r$1.mergeType.trim() === "1") r$1.mergeType = 1;
        else if (r(r$1.mergeType) && r$1.mergeType.trim() === "2") r$1.mergeType = 2;
        else throw new Error(`ranges-push: [THROW_ID_02] opts.mergeType was customised to a wrong thing! It was given of a type: "${typeof r$1.mergeType}", equal to ${JSON.stringify(r$1.mergeType, null, 4)}`);
        this.opts = r$1, this.ranges = [];
    }
    add(t, r$1, s) {
        if (t == null && r$1 == null) return;
        if (G$1(t) && !G$1(r$1)) {
            if (Array.isArray(t)) {
                if (t.length) {
                    if (t.some((o)=>Array.isArray(o))) {
                        t.forEach((o)=>{
                            Array.isArray(o) && this.add(...o);
                        });
                        return;
                    }
                    t.length && q$1(+t[0]) && q$1(+t[1]) && this.add(...t);
                }
                return;
            }
            throw new TypeError(`ranges-push/Ranges/add(): [THROW_ID_12] the first input argument, "from" is set (${JSON.stringify(t, null, 0)}) but second-one, "to" is not (${JSON.stringify(r$1, null, 0)})`);
        } else if (!G$1(t) && G$1(r$1)) throw new TypeError(`ranges-push/Ranges/add(): [THROW_ID_13] the second input argument, "to" is set (${JSON.stringify(r$1, null, 0)}) but first-one, "from" is not (${JSON.stringify(t, null, 0)})`);
        let i = +t, n = +r$1;
        if (q$1(s) && (s = String(s)), q$1(i) && q$1(n)) {
            if (G$1(s) && !r(s) && !q$1(s)) throw new TypeError(`ranges-push/Ranges/add(): [THROW_ID_08] The third argument, the value to add, was given not as string but ${typeof s}, equal to:
${JSON.stringify(s, null, 4)}`);
            if (G$1(this.ranges) && Array.isArray(this.last()) && i === this.last()[1]) {
                if (this.last()[1] = n, this.last()[2], this.last()[2] !== null && G$1(s)) {
                    let o = this.last()[2] && this.last()[2].length && (!this.opts?.mergeType || this.opts.mergeType === 1) ? `${this.last()[2]}${s}` : s;
                    this.opts.limitToBeAddedWhitespace && (o = D$1(o, this.opts.limitLinebreaksCount)), r(o) && !o.length || (this.last()[2] = o);
                }
            } else {
                this.ranges || (this.ranges = []);
                let o = s !== void 0 && !(r(s) && !s.length) ? [
                    i,
                    n,
                    s && this.opts.limitToBeAddedWhitespace ? D$1(s, this.opts.limitLinebreaksCount) : s
                ] : [
                    i,
                    n
                ];
                this.ranges.push(o);
            }
        } else throw q$1(i) && i >= 0 ? new TypeError(`ranges-push/Ranges/add(): [THROW_ID_10] "to" value, the second input argument, must be a natural number or zero! Currently it's of a type "${typeof n}" equal to: ${JSON.stringify(n, null, 4)}`) : new TypeError(`ranges-push/Ranges/add(): [THROW_ID_09] "from" value, the first input argument, must be a natural number or zero! Currently it's of a type "${typeof i}" equal to: ${JSON.stringify(i, null, 4)}`);
    }
    push(t, r, s) {
        this.add(t, r, s);
    }
    current() {
        return Array.isArray(this.ranges) && this.ranges.length ? (this.ranges = f(this.ranges, {
            mergeType: this.opts.mergeType
        }), this.ranges && this.opts.limitToBeAddedWhitespace ? this.ranges.map((t)=>G$1(t[2]) ? [
                t[0],
                t[1],
                D$1(t[2], this.opts.limitLinebreaksCount)
            ] : t) : this.ranges) : null;
    }
    wipe() {
        this.ranges = [];
    }
    replace(t) {
        if (Array.isArray(t) && t.length) if (Array.isArray(t[0]) && q$1(t[0][0])) this.ranges = Array.from(t);
        else throw new Error(`ranges-push/Ranges/replace(): [THROW_ID_11] Single range was given but we expected array of arrays! The first element, ${JSON.stringify(t[0], null, 4)} should be an array and its first element should be an integer, a string index.`);
        else this.ranges = [];
    }
    last() {
        return Array.isArray(this.ranges) && this.ranges.length ? this.ranges[this.ranges.length - 1] : null;
    }
};

I(); var c = "\xA0";
function D({ str: n, idx: e = 0, stopAtNewlines: l = false, stopAtRawNbsp: o = false }) {
    if (typeof n != "string" || !n.length || ((!e || typeof e != "number") && (e = 0), !n[e + 1])) return null;
    if (n[e + 1] && (n[e + 1].trim() || l && `
\r`.includes(n[e + 1]) || o && n[e + 1] === c)) return e + 1;
    if (n[e + 2] && (n[e + 2].trim() || l && `
\r`.includes(n[e + 2]) || o && n[e + 2] === c)) return e + 2;
    for(let t = e + 1, m = n.length; t < m; t++)if (n[t].trim() || l && `
\r`.includes(n[t]) || o && n[t] === c) return t;
    return null;
}
function E(n, e = 0) {
    return D({
        str: n,
        idx: e,
        stopAtNewlines: false,
        stopAtRawNbsp: false
    });
}

function ee(t) {
    return /[-_A-Za-z0-9]/.test(t);
}
function G(t, m) {
    if (!t) return [];
    if (Array.isArray(t)) return t.filter((y)=>typeof y == "string" && y.trim());
    if (typeof t == "string") return t.trim() ? [
        t
    ] : [];
    throw new TypeError(`string-strip-html/stripHtml(): [THROW_ID_05] ${m} must be array containing zero or more strings or something falsey. Currently it's equal to: ${t}, that a type of ${typeof t}.`);
}
function j(t, m, y, c) {
    for(let f = m, b = t.length; f < b; f++){
        if (t.startsWith(y, f)) return true;
        if (t.startsWith(c, f)) return false;
    }
    return false;
}
function Y(t, m, y) {
    !t?.quotes; !!t?.quotes?.value && !j(m, y + 1, t.quotes.value, ">"); t?.quotes?.next !== -1; !j(m, t?.quotes?.next - 1, t?.quotes?.value, ">");
    return !t?.quotes || !j(m, y + 1, t.quotes.value, ">") && t?.quotes?.next !== -1 && j(m, t?.quotes?.next - 1, t?.quotes?.value, ">");
}
function ne(t, m) {
    return (m.match(new RegExp(t, "g")) || []).length;
}
var P = new Set([
    "!doctype",
    "abbr",
    "address",
    "area",
    "article",
    "aside",
    "audio",
    "base",
    "bdi",
    "bdo",
    "blockquote",
    "body",
    "br",
    "button",
    "canvas",
    "caption",
    "cite",
    "code",
    "col",
    "colgroup",
    "data",
    "datalist",
    "dd",
    "del",
    "details",
    "dfn",
    "dialog",
    "div",
    "dl",
    "doctype",
    "dt",
    "em",
    "embed",
    "fieldset",
    "figcaption",
    "figure",
    "footer",
    "form",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "head",
    "header",
    "hgroup",
    "hr",
    "html",
    "iframe",
    "img",
    "input",
    "ins",
    "kbd",
    "keygen",
    "label",
    "legend",
    "li",
    "link",
    "main",
    "map",
    "mark",
    "math",
    "menu",
    "menuitem",
    "meta",
    "meter",
    "nav",
    "noscript",
    "object",
    "ol",
    "optgroup",
    "option",
    "output",
    "param",
    "picture",
    "pre",
    "progress",
    "rb",
    "rp",
    "rt",
    "rtc",
    "ruby",
    "samp",
    "script",
    "section",
    "select",
    "slot",
    "small",
    "source",
    "span",
    "strong",
    "style",
    "sub",
    "summary",
    "sup",
    "svg",
    "table",
    "tbody",
    "td",
    "template",
    "textarea",
    "tfoot",
    "th",
    "thead",
    "time",
    "title",
    "tr",
    "track",
    "ul",
    "var",
    "video",
    "wbr",
    "xml"
]), x = new Set([
    "a",
    "b",
    "i",
    "p",
    "q",
    "s",
    "u"
]), q = new Set([
    ".",
    ",",
    ";",
    "!",
    "?"
]), Q = new Set([
    ".",
    ",",
    "?",
    ";",
    ")",
    "\u2026",
    '"',
    "\xBB"
]), te = new Set([
    "a",
    "abbr",
    "acronym",
    "audio",
    "b",
    "bdi",
    "bdo",
    "big",
    "button",
    "canvas",
    "cite",
    "code",
    "data",
    "datalist",
    "del",
    "dfn",
    "em",
    "embed",
    "i",
    "iframe",
    "input",
    "ins",
    "kbd",
    "label",
    "map",
    "mark",
    "meter",
    "noscript",
    "object",
    "output",
    "picture",
    "progress",
    "q",
    "ruby",
    "s",
    "samp",
    "select",
    "slot",
    "small",
    "span",
    "strong",
    "sub",
    "sup",
    "svg",
    "template",
    "textarea",
    "time",
    "u",
    "tt",
    "var",
    "video",
    "wbr"
]);
var ae = {
    ignoreTags: [],
    ignoreTagsWithTheirContents: [],
    onlyStripTags: [],
    stripTogetherWithTheirContents: [
        "script",
        "style",
        "xml"
    ],
    skipHtmlDecoding: false,
    trimOnlySpaces: false,
    stripRecognisedHTMLOnly: false,
    dumpLinkHrefsNearby: {
        enabled: false,
        putOnNewLine: false,
        wrapHeads: "",
        wrapTails: ""
    },
    ignoreIndentations: false,
    cb: null,
    reportProgressFunc: null,
    reportProgressFuncFrom: 0,
    reportProgressFuncTo: 100
};
function fe(t, m) {
    let y = Date.now(), c = [], f = [], b = [], $$1 = [], n = {};
    function re() {
        n = {
            attributes: []
        };
    }
    re();
    let V = null, w = null, N = null, J = false, o = {}, d = {
        tagName: "",
        hrefValue: "",
        openingTagEnds: void 0
    }, E$1 = "", C = false, B = null, F = true;
    function v$1(e, s, a) {
        if (Array.isArray(s.stripTogetherWithTheirContents) && (s.stripTogetherWithTheirContents.includes(n.name) || s.stripTogetherWithTheirContents.includes("*"))) if (n.slashPresent && Array.isArray(c) && c.some((i)=>i.name === n.name)) {
            for(let i = c.length; i--;)if (c[i].name === n.name) {
                $$1 = $$1.filter(([u, p])=>(u < c[i].lastOpeningBracketAt || u >= e + 1) && (p <= c[i].lastOpeningBracketAt || p > e + 1));
                let g = e + 1;
                n.lastClosingBracketAt && (g = n.lastClosingBracketAt + 1), $$1.push([
                    c[i].lastOpeningBracketAt,
                    g
                ]), Q.has(t[e]) && s.cb ? s.cb({
                    tag: n,
                    deleteFrom: c[i].lastOpeningBracketAt,
                    deleteTo: e + 1,
                    insert: null,
                    rangesArr: a,
                    proposedReturn: [
                        c[i].lastOpeningBracketAt,
                        e,
                        null
                    ]
                }) : s.cb && s.cb({
                    tag: n,
                    deleteFrom: c[i].lastOpeningBracketAt,
                    deleteTo: e,
                    insert: "",
                    rangesArr: a,
                    proposedReturn: [
                        c[i].lastOpeningBracketAt,
                        e,
                        ""
                    ]
                }), c.splice(i, 1);
                break;
            }
        } else n.slashPresent || c.push(n);
        else Array.isArray(s.ignoreTagsWithTheirContents) && M(e, s, n) && (F = false);
    }
    function L(e, s, a, i, g, u) {
        if (Array.isArray(r.current()) && typeof a == "number" && r.current()[0][0] === 0 && r.current()[0][1] >= a) return "";
        if (t.length === i && u && !l?.dumpLinkHrefsNearby?.enabled) return null;
        let p = "";
        if (Number.isInteger(a) && a < g && (p += e.slice(a, g)), Number.isInteger(i) && i > u + 1) {
            let D = e.slice(u + 1, i);
            i && !E(t, i - 1) && (D = D.trimEnd()), D.includes(`
`) && S(i, e) ? p += " " : p += D;
        }
        let W = !Q.has(e[s]), Z = e[i - 1] !== ">" || !e[a].trim(), X = ![
            '"',
            "("
        ].includes(e[g - 1]), ge = ![
            ";",
            ".",
            ":",
            "!"
        ].includes(e[s]);
        if ((W || Z && X && ge) && (Z || X) && e[s] !== "!" && (!te.has(n.name) || typeof a == "number" && a < g || typeof i == "number" && i > u + 1)) {
            let D = p.match(/\n/g);
            return Array.isArray(D) && D.length ? D.length === 1 ? `
` : D.length === 2 ? `

` : `


` : " ";
        }
        return "";
    }
    function U(e, s) {
        if (e.dumpLinkHrefsNearby?.enabled && d.tagName && d.tagName === n.name && n.lastOpeningBracketAt && (d.openingTagEnds && n.lastOpeningBracketAt > d.openingTagEnds || !d.openingTagEnds) && (C = true), C) {
            let a = e.dumpLinkHrefsNearby?.putOnNewLine ? `

` : "";
            E$1 = `${a}${d.hrefValue}`, (typeof s != "number" || E(t, s - 1)) && (E$1 += a);
        }
    }
    function S(e, s) {
        return s ? s[e] === "<" && s[e + 1] !== "%" : t[e] === "<" && t[e + 1] !== "%";
    }
    function A(e) {
        return t[e] === ">" && t[e - 1] !== "%";
    }
    function M(e, s, a) {
        if (s.ignoreTagsWithTheirContents.includes("*")) return true;
        let i = t.indexOf(`<${a.name}`, e), g = t.indexOf(`</${a.name}`, e);
        return !a.slashPresent && g === -1 || a.slashPresent && !f.some((u)=>u.name === a.name) || g > -1 && i > -1 && i < g ? false : s.ignoreTagsWithTheirContents.includes(a.name);
    }
    if (typeof t != "string") throw new TypeError(`string-strip-html/stripHtml(): [THROW_ID_01] Input must be string! Currently it's: ${(typeof t).toLowerCase()}, equal to:
${JSON.stringify(t, null, 4)}`);
    if (m) if (u(m)) {
        if (m.reportProgressFunc && typeof m.reportProgressFunc != "function") throw new Error(`string-strip-html/stripHtml(): [THROW_ID_03] The Optional Options Object's key reportProgressFunc, callback function, should be a function but it was given as type ${typeof m.reportProgressFunc}, equal to ${JSON.stringify(m.reportProgressFunc, null, 4)}`);
        if (typeof m.dumpLinkHrefsNearby == "boolean" && m.dumpLinkHrefsNearby != null) throw new Error(`string-strip-html/stripHtml(): [THROW_ID_04] The Optional Options Object's key should be a plain object but it was given as type ${typeof m.dumpLinkHrefsNearby}, equal to ${JSON.stringify(m.dumpLinkHrefsNearby, null, 4)}`);
    } else throw new TypeError(`string-strip-html/stripHtml(): [THROW_ID_02] Optional Options Object must be a plain object! Currently it's: ${(typeof m).toLowerCase()}, equal to:
${JSON.stringify(m, null, 4)}`);
    function _$1() {
        C && (d = {
            tagName: "",
            hrefValue: "",
            openingTagEnds: void 0
        }, C = false);
    }
    let l = {
        ...ae,
        ...m,
        dumpLinkHrefsNearby: Object.assign({}, ae.dumpLinkHrefsNearby, m?.dumpLinkHrefsNearby)
    };
    if (v(l, "returnRangesOnly")) throw new TypeError("string-strip-html/stripHtml(): [THROW_ID_05] The Optional Options Object's key returnRangesOnly has been removed from the API since v.5 release.");
    if (l.reportProgressFunc) {
        if (typeof l.reportProgressFuncFrom != "number") throw new Error(`string-strip-html/stripHtml(): [THROW_ID_06] The Optional Options Object's key reportProgressFuncFrom, callback function's "from" range, should be a number but it was given as type ${typeof l.reportProgressFuncFrom}, equal to ${JSON.stringify(l.reportProgressFuncFrom, null, 4)}`);
        if (typeof l.reportProgressFuncTo != "number") throw new Error(`string-strip-html/stripHtml(): [THROW_ID_07] The Optional Options Object's key reportProgressFuncTo, callback function's "to" range, should be a number but it was given as type ${typeof l.reportProgressFuncTo}, equal to ${JSON.stringify(l.reportProgressFuncTo, null, 4)}`);
    }
    l.ignoreTags = G(l.ignoreTags, "resolvedOpts.ignoreTags"), l.onlyStripTags = G(l.onlyStripTags, "resolvedOpts.onlyStripTags");
    let z = !!l.onlyStripTags.length;
    l.onlyStripTags.length && l.ignoreTags.length && (l.onlyStripTags = without(l.onlyStripTags, ...l.ignoreTags)), l.stripTogetherWithTheirContents ? typeof l.stripTogetherWithTheirContents == "string" && l.stripTogetherWithTheirContents.length && (l.stripTogetherWithTheirContents = [
        l.stripTogetherWithTheirContents
    ]) : l.stripTogetherWithTheirContents = [];
    let I = {};
    if (l.stripTogetherWithTheirContents && Array.isArray(l.stripTogetherWithTheirContents) && l.stripTogetherWithTheirContents.length && !l.stripTogetherWithTheirContents.every((e, s)=>typeof e != "string" ? (I.el = e, I.i = s, false) : true)) throw new TypeError(`string-strip-html/stripHtml(): [THROW_ID_08] Optional Options Object's key stripTogetherWithTheirContents was set to contain not just string elements! For example, element at index ${I.i} has a value ${I.el} which is not string but ${(typeof I.el).toLowerCase()}.`);
    l.cb || (l.cb = ({ rangesArr: e, proposedReturn: s })=>{
        s && e.push(...s);
    });
    let r = new $({
        limitToBeAddedWhitespace: true,
        limitLinebreaksCount: 2
    });
    if (!l.skipHtmlDecoding) for(; t !== libExports.decode(t, {
        scope: "strict"
    });)t = libExports.decode(t, {
        scope: "strict"
    });
    let R = false, k = false, H = 0, K = 0, h = t.length, ue = Math.floor(h / 2);
    for(let e = 0; e < h; e++){
        if (l.reportProgressFunc && (h > 1e3 && h < 2e3 ? e === ue && l.reportProgressFunc(Math.floor((l.reportProgressFuncTo - l.reportProgressFuncFrom) / 2)) : h >= 2e3 && (H = l.reportProgressFuncFrom + Math.floor(e / h * (l.reportProgressFuncTo - l.reportProgressFuncFrom)), H !== K && (K = H, l.reportProgressFunc(H)))), Object.keys(n).length > 1 && n.lastClosingBracketAt && n.lastClosingBracketAt < e && t[e] !== " " && B === null && (B = e), !R && t[e] === "%" && t[e - 1] === "{" && t.includes("%}", e + 1)) {
            N = null;
            let s = t.indexOf("%}", e) - 1;
            if (s > e) {
                e = s;
                continue;
            }
        }
        if (!R && A(e) && (!n || Object.keys(n).length < 2) && e > 1) {
            for(let s = e; s--;)if (t[s - 1] === void 0 || A(s)) {
                let a = t[s - 1] === void 0 ? s : s + 1, i = t.slice(a, e + 1) || "";
                if ((i.includes("/>") || i.includes("/ >") || i.includes('="') || i.includes("='")) && t !== `<${trim(i.trim(), "/>")}>` && [
                    ...P
                ].some((g)=>trim(i.trim().split(/\s+/).filter((u)=>u.trim()).filter((u, p)=>p === 0), "/>").toLowerCase() === g) && fe(`<${i.trim()}>`, l).result === "") {
                    (!b.length || b[b.length - 1][0] !== n.lastOpeningBracketAt) && b.push([
                        a,
                        e + 1
                    ]), (!$$1.length || $$1[$$1.length - 1][0] !== n.lastOpeningBracketAt) && $$1.push([
                        a,
                        e + 1
                    ]);
                    let g = L(t, e, a, e + 1, a, e + 1), u = e + 1;
                    if (t[u] && !t[u].trim()) {
                        for(let p = u; p < h; p++)if (t[p].trim()) {
                            u = p;
                            break;
                        }
                    }
                    l.cb({
                        tag: n,
                        deleteFrom: a,
                        deleteTo: u,
                        insert: g,
                        rangesArr: r,
                        proposedReturn: [
                            a,
                            u,
                            g
                        ]
                    });
                }
                break;
            }
        }
        if (!k && t[e] === "/" && !n.quotes?.value && Number.isInteger(n.lastOpeningBracketAt) && !Number.isInteger(n.lastClosingBracketAt) && (n.slashPresent = e), t[e] === '"' || t[e] === "'") if (!k && n.nameStarts && n?.quotes?.value === t[e]) if (o.valueStarts === void 0) o = {}, delete n.quotes;
        else {
            o.valueEnds = e, o.value = t.slice(o.valueStarts, e), n.attributes.push(o), o = {}, delete n.quotes;
            let s;
            l.dumpLinkHrefsNearby?.enabled && !c.length && n.attributes.some((a)=>{
                if (typeof a.name == "string" && a.name.toLowerCase() === "href") return s = `${l.dumpLinkHrefsNearby?.wrapHeads || ""}${a.value}${l.dumpLinkHrefsNearby?.wrapTails || ""}`, true;
            }) && (d = {
                tagName: n.name,
                hrefValue: s,
                openingTagEnds: void 0
            });
        }
        else !k && !n.quotes && n.nameStarts && (n.quotes = {}, n.quotes.value = t[e], n.quotes.start = e, n.quotes.next = t.indexOf(t[e], e + 1), o.nameStarts && o.nameEnds && o.nameEnds < e && o.nameStarts < e && !o.valueStarts && (o.name = t.slice(o.nameStarts, o.nameEnds)));
        if (n.nameStarts !== void 0 && n.nameEnds === void 0 && (!t[e].trim() || !ee(t[e]))) {
            if (n.nameEnds = e, n.name = t.slice(n.nameStarts, n.nameEnds + (!A(e) && t[e] !== "/" && t[e + 1] === void 0 ? 1 : 0)), t[n.nameStarts - 1] !== "!" && !n.name.replace(/-/g, "").length || /^\d+$/.test(n.name[0])) {
                n = {};
                continue;
            }
            if (typeof n.name == "string" && n.name.toLowerCase() === "doctype" && (k = true), S(e)) {
                U(l);
                let s = L(t, e, n.leftOuterWhitespace, e, n.lastOpeningBracketAt, e);
                (l.stripTogetherWithTheirContents.includes(n.name) || l.stripTogetherWithTheirContents.includes("*")) && ($$1 = $$1.filter(([a, i])=>!(a === n.leftOuterWhitespace && i === e))), l.cb({
                    tag: n,
                    deleteFrom: n.leftOuterWhitespace,
                    deleteTo: e,
                    insert: `${s}${E$1}${s}`,
                    rangesArr: r,
                    proposedReturn: [
                        n.leftOuterWhitespace,
                        e,
                        `${s}${E$1}${s}`
                    ]
                }), _$1(), v$1(e, l, r);
            }
        }
        if (n.quotes?.start && n.quotes.start < e && !n.quotes.end && o.nameEnds && o.equalsAt && !o.valueStarts && (o.valueStarts = e), !n.quotes && o.nameEnds && t[e] === "=" && !o.valueStarts && !o.equalsAt && (o.equalsAt = e), !n.quotes && o.nameStarts && o.nameEnds && !o.valueStarts && t[e].trim() && t[e] !== "=" && (n.attributes.push(o), o = {}), !n.quotes && o.nameStarts && !o.nameEnds && (k && `'"`.includes(t[o.nameStarts]) ? o.nameStarts < e && t[e] === t[o.nameStarts] && (o.nameEnds = e + 1, o.name = t.slice(o.nameStarts, o.nameEnds)) : t[e].trim() ? t[e] === "=" ? o.equalsAt || (o.nameEnds = e, o.equalsAt = e, o.name = t.slice(o.nameStarts, o.nameEnds)) : t[e] === "/" || A(e) ? (o.nameEnds = e, o.name = t.slice(o.nameStarts, o.nameEnds), n.attributes.push(o), o = {}) : S(e) && (o.nameEnds = e, o.name = t.slice(o.nameStarts, o.nameEnds), n.attributes.push(o), o = {}) : (o.nameEnds = e, o.name = t.slice(o.nameStarts, o.nameEnds))), !n.quotes && n.nameEnds < e && !t[e - 1].trim() && t[e].trim() && !"<>/!".includes(t[e]) && !o.nameStarts && !n.lastClosingBracketAt && (o.nameStarts = e), n.lastOpeningBracketAt !== null && n.lastOpeningBracketAt < e && t[e] === "/" && n.onlyPlausible && (n.onlyPlausible = false), n.lastOpeningBracketAt !== null && n.lastOpeningBracketAt < e && t[e] !== "/" && (n.onlyPlausible === void 0 && ((!t[e].trim() || S(e)) && !n.slashPresent ? n.onlyPlausible = true : n.onlyPlausible = false), t[e].trim() && n.nameStarts === void 0 && !S(e) && t[e] !== "/" && !A(e) && t[e] !== "!" && (n.nameStarts = e, n.nameContainsLetters = false)), n.nameStarts && !n.quotes && typeof t[e] == "string" && t[e].toLowerCase() !== t[e].toUpperCase() && (n.nameContainsLetters = true), A(e) && (Y(n, t, e) || n.quotes.value && typeof n.lastOpeningBracketAt == "number" && ne(n.quotes.value, t.slice(n.lastOpeningBracketAt, e)) % 2 === 1 && !t.slice(n.lastOpeningBracketAt + 1, e).includes("<") && !t.slice(n.lastOpeningBracketAt + 1, e).includes(">")) && n.lastOpeningBracketAt !== void 0 && (n.lastClosingBracketAt = e, B = null, Object.keys(o).length && (n.attributes.push(o), o = {}), l.dumpLinkHrefsNearby?.enabled && d.tagName && !d.openingTagEnds && (d.openingTagEnds = e)), (!k || t[e] === ">") && n.lastOpeningBracketAt !== void 0) {
            if (n.lastClosingBracketAt === void 0) {
                if (n.lastOpeningBracketAt < e && !S(e) && (t[e + 1] === void 0 || S(e + 1) && !n?.quotes?.value) && n.nameContainsLetters && typeof n.nameStarts == "number") {
                    if (n.name = t.slice(n.nameStarts, n.nameEnds || e + 1).toLowerCase(), (!b.length || b[b.length - 1][0] !== n.lastOpeningBracketAt) && b.push([
                        n.lastOpeningBracketAt,
                        e + 1
                    ]), l.ignoreTags.includes(n.name) || M(e, l, n) || !P.has(n.name) && (n.onlyPlausible || l.stripRecognisedHTMLOnly)) {
                        n = {}, o = {};
                        continue;
                    }
                    if ((P.has(n.name) || x.has(n.name)) && (n.onlyPlausible === false || n.onlyPlausible === true && n.attributes.length) || t[e + 1] === void 0) {
                        U(l);
                        let s = L(t, e, n.leftOuterWhitespace, e + 1, n.lastOpeningBracketAt, n.lastClosingBracketAt);
                        R && n.name === "script" && n.slashPresent && (R = false);
                        let a;
                        s === null || E$1 === null ? a = null : a = `${s}${E$1}${s}`, l.cb({
                            tag: n,
                            deleteFrom: n.leftOuterWhitespace,
                            deleteTo: e + 1,
                            insert: a,
                            rangesArr: r,
                            proposedReturn: [
                                n.leftOuterWhitespace,
                                e + 1,
                                a
                            ]
                        }), _$1(), v$1(e, l, r);
                    }
                    if (!$$1.length || $$1[$$1.length - 1][0] !== n.lastOpeningBracketAt && $$1[$$1.length - 1][1] !== e + 1) if (l.stripTogetherWithTheirContents.includes(n.name) || l.stripTogetherWithTheirContents.includes("*")) {
                        let s;
                        for(let a = c.length; a--;)c[a].name === n.name && (s = c[a]);
                        s ? ($$1 = $$1.filter(([a])=>a !== s.lastOpeningBracketAt), $$1.push([
                            s.lastOpeningBracketAt,
                            e + 1
                        ])) : $$1.push([
                            n.lastOpeningBracketAt,
                            e + 1
                        ]);
                    } else $$1.push([
                        n.lastOpeningBracketAt,
                        e + 1
                    ]);
                }
            } else if (e > n.lastClosingBracketAt && t[e].trim() || t[e + 1] === void 0 || l.ignoreIndentations && `\r
`.includes(t[e])) {
                let s = n.lastClosingBracketAt === e ? e + 1 : e;
                l.trimOnlySpaces && s === h - 1 && B !== null && B < e && (s = B), (!b.length || b[b.length - 1][0] !== n.lastOpeningBracketAt) && b.push([
                    n.lastOpeningBracketAt,
                    n.lastClosingBracketAt + 1
                ]);
                let a = l.ignoreTags.includes(n.name), i = M(e, l, n);
                if (!F || l.stripRecognisedHTMLOnly && typeof n.name == "string" && !P.has(n.name.toLowerCase()) && !x.has(n.name.toLowerCase()) || !z && (a || i) || z && !l.onlyStripTags.includes(n.name) || l.ignoreTagsWithTheirContents.includes(n.name)) {
                    if (i) if (n.slashPresent) {
                        for(let g = f.length; g--;)if (f[g].name === n.name) {
                            f.splice(g, 1);
                            break;
                        }
                        f.length || (F = true);
                    } else F && (F = false), f.push(n);
                    l.cb({
                        tag: n,
                        deleteFrom: null,
                        deleteTo: null,
                        insert: null,
                        rangesArr: r,
                        proposedReturn: null
                    }), n = {}, o = {};
                } else if (!n.onlyPlausible || n.attributes.length === 0 && n.name && (P.has(n.name.toLowerCase()) || x.has(n.name.toLowerCase())) || n.attributes?.some((g)=>g.equalsAt)) {
                    (!$$1.length || $$1[$$1.length - 1][0] !== n.lastOpeningBracketAt) && $$1.push([
                        n.lastOpeningBracketAt,
                        n.lastClosingBracketAt + 1
                    ]);
                    let g = L(t, e, n.leftOuterWhitespace, s, n.lastOpeningBracketAt, n.lastClosingBracketAt);
                    E$1 = "", C = false, U(l, s);
                    let u;
                    typeof E$1 == "string" && E$1.length ? (u = `${g}${E$1}${g === `

` ? `
` : g}`, s === n.lastClosingBracketAt + 1 && (!t[s] || !q.has(t[s])) && (u += " "), n.leftOuterWhitespace === n.lastOpeningBracketAt && r.last() && r.last()[1] < n.lastOpeningBracketAt && (!l?.dumpLinkHrefsNearby?.putOnNewLine || !q.has(t[s])) && (u = " " + u)) : u = g, u !== null && (n.leftOuterWhitespace === 0 || !E(t, s - 1)) && (!l.dumpLinkHrefsNearby?.enabled || n.name !== "a") && (u = void 0);
                    let p = 0;
                    if (C && q.has(t[s])) {
                        l.dumpLinkHrefsNearby?.putOnNewLine && (u = `${t[s]}${u || ""}`);
                        let W = E(t, s);
                        W && u?.endsWith(`
`) ? p += W - e : (!W || W > e) && p++;
                    }
                    l.cb({
                        tag: n,
                        deleteFrom: n.leftOuterWhitespace,
                        deleteTo: s + p,
                        insert: u,
                        rangesArr: r,
                        proposedReturn: [
                            n.leftOuterWhitespace,
                            s + p,
                            u
                        ]
                    }), _$1(), v$1(e, l, r);
                } else n = {};
                A(e) || (n = {});
            }
            k && (k = false);
        }
        if ((!R || t[e] === "<" && E(t, E(t, e)) && t[E(t, e)] === "/" && t.startsWith("script", E(t, E(t, e)))) && S(e) && !S(e - 1) && !`'"`.includes(t[e + 1]) && (!`'"`.includes(t[e + 2]) || /\w/.test(t[e + 1])) && !(t[e + 1] === "c" && t[e + 2] === ":") && !(t[e + 1] === "f" && t[e + 2] === "m" && t[e + 3] === "t" && t[e + 4] === ":") && !(t[e + 1] === "s" && t[e + 2] === "q" && t[e + 3] === "l" && t[e + 4] === ":") && !(t[e + 1] === "x" && t[e + 2] === ":") && !(t[e + 1] === "f" && t[e + 2] === "n" && t[e + 3] === ":") && Y(n, t, e)) {
            if (A(E(t, e))) continue;
            if (n.nameEnds && n.nameEnds < e && !n.lastClosingBracketAt && (n.onlyPlausible === true && n.attributes?.length || n.onlyPlausible === false)) {
                let s = L(t, e, n.leftOuterWhitespace, e, n.lastOpeningBracketAt, e);
                l.cb({
                    tag: n,
                    deleteFrom: n.leftOuterWhitespace,
                    deleteTo: e,
                    insert: s,
                    rangesArr: r,
                    proposedReturn: [
                        n.leftOuterWhitespace,
                        e,
                        s
                    ]
                }), v$1(e, l, r), n = {}, o = {};
            }
            if (n.lastOpeningBracketAt !== void 0 && n.onlyPlausible && n.name && !n.quotes && (n.lastOpeningBracketAt = void 0, n.name = void 0, n.onlyPlausible = false), (n.lastOpeningBracketAt === void 0 || !n.onlyPlausible) && !n.quotes && (n.lastOpeningBracketAt = e, n.slashPresent = false, n.attributes = [], V === null ? n.leftOuterWhitespace = e : l.trimOnlySpaces && V === 0 ? n.leftOuterWhitespace = w || e : n.leftOuterWhitespace = V, `${t[e + 1]}${t[e + 2]}${t[e + 3]}` == "!--" || `${t[e + 1]}${t[e + 2]}${t[e + 3]}${t[e + 4]}${t[e + 5]}${t[e + 6]}${t[e + 7]}${t[e + 8]}` == "![CDATA[")) {
                let s = true;
                t[e + 2] === "-" && (s = false);
                let a;
                for(let i = e; i < h; i++)if ((!a && s && `${t[i - 2]}${t[i - 1]}${t[i]}` == "]]>" || !s && `${t[i - 2]}${t[i - 1]}${t[i]}` == "-->") && (a = i), a && (a < i && t[i].trim() || t[i + 1] === void 0)) {
                    let g = i;
                    (t[i + 1] === void 0 && !t[i].trim() || t[i] === ">") && (g += 1), (!b.length || b[b.length - 1][0] !== n.lastOpeningBracketAt) && b.push([
                        n.lastOpeningBracketAt,
                        a + 1
                    ]), (!$$1.length || $$1[$$1.length - 1][0] !== n.lastOpeningBracketAt) && $$1.push([
                        n.lastOpeningBracketAt,
                        a + 1
                    ]);
                    let u = L(t, i, n.leftOuterWhitespace, g, n.lastOpeningBracketAt, a);
                    l.cb({
                        tag: n,
                        deleteFrom: n.leftOuterWhitespace,
                        deleteTo: g,
                        insert: u,
                        rangesArr: r,
                        proposedReturn: [
                            n.leftOuterWhitespace,
                            g,
                            u
                        ]
                    }), e = i - 1, t[i] === ">" && (e = i), n = {}, o = {};
                    break;
                }
            }
        }
        !t[e].trim() || t[e].charCodeAt(0) === 847 ? (V === null && (V = e, n.lastOpeningBracketAt !== void 0 && n.lastOpeningBracketAt < e && n.nameStarts && n.nameStarts < n.lastOpeningBracketAt && e === n.lastOpeningBracketAt + 1 && !c.some((s)=>s.name === n.name) && (n.onlyPlausible = true, n.name = void 0, n.nameStarts = void 0)), (t[e] === `
` || t[e] === "\r") && (N = e, J && (J = false))) : (V !== null && (!n.quotes && o.equalsAt > V - 1 && o.nameEnds && o.equalsAt > o.nameEnds && t[e] !== '"' && t[e] !== "'" && (u(o) && n.attributes.push(o), o = {}, n.equalsSpottedAt = void 0), V = null), J || (J = true, F && !R && typeof N == "number" && e && N < e - 1 && (t.slice(N + 1, e).trim() ? N = null : l.ignoreIndentations || r.push([
            N + 1,
            e
        ])))), t[e] === " " ? w === null && (w = e) : w !== null && (w = null), n.name === "script" && (R = !n.slashPresent);
    }
    if (t && !l.ignoreIndentations && (l.trimOnlySpaces && t[0] === " " || !l.trimOnlySpaces && !t[0].trim())) for(let e = 0; e < h; e++)if (l.trimOnlySpaces && t[e] !== " " || !l.trimOnlySpaces && t[e].trim()) {
        r.push([
            0,
            e
        ]);
        break;
    } else t[e + 1] || r.push([
        0,
        e + 1
    ]);
    if (t && (l.trimOnlySpaces && t[~-t.length] === " " || !l.trimOnlySpaces && !t[~-t.length].trim())) {
        for(let e = t.length; e--;)if (l.trimOnlySpaces && t[e] !== " " || !l.trimOnlySpaces && t[e].trim()) {
            r.push([
                e + 1,
                h
            ]);
            break;
        }
    }
    let O = r.current();
    if (!m?.cb && O) {
        if (O[0] && !O[0][0]) {
            O[0][1];
            r.ranges[0] = [
                r.ranges[0][0],
                r.ranges[0][1]
            ];
        }
        if (O[O.length - 1] && O[O.length - 1][1] === t.length) {
            O[O.length - 1][0];
            if (r.ranges) {
                let s = r.ranges[r.ranges.length - 1][0];
                t[s - 1] && (l.trimOnlySpaces && t[s - 1] === " " || !l.trimOnlySpaces && !t[s - 1].trim()) && (s -= 1);
                let a = r.ranges[r.ranges.length - 1][2];
                r.ranges[r.ranges.length - 1] = [
                    s,
                    r.ranges[r.ranges.length - 1][1]
                ], a?.trim() && r.ranges[r.ranges.length - 1].push(a.trimEnd());
            }
        }
    }
    return {
        log: {
            timeTakenInMilliseconds: Date.now() - y
        },
        result: _(t, r.current()),
        ranges: r.current(),
        allTagLocations: b,
        filteredTagLocations: $$1
    };
}

export { ae as defaults, fe as stripHtml };
