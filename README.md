# ChatDoc - Chat with Documents AI Application

A modern web application that allows users to upload documents and chat with them using AI-powered natural language processing, powered by **Chinese AI Stack: DeepSeek + Doubao**.

## Features

- **Document Upload**: Drag-and-drop interface for .txt and .md files
- **AI-Powered Chat**: Ask questions about uploaded documents using DeepSeek's reasoning models
- **Smart Document Search**: Doubao embeddings for accurate semantic document retrieval
- **Source Attribution**: All responses include references to source documents and relevant text chunks
- **Real-time Processing**: Documents are automatically chunked, embedded, and made searchable upon upload
- **Modern UI**: Built with React, Next.js, and Tailwind CSS
- **Chinese AI Integration**: Complete Chinese AI stack for enhanced performance and accessibility

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes
- **AI/ML**: Custom RAG pipeline with document chunking and semantic search
- **LLM**: DeepSeek reasoning models for enhanced document analysis
- **Embeddings**: <PERSON><PERSON><PERSON> (ByteDance) embeddings for document vectorization
- **File Storage**: Local file system with metadata tracking

## Setup Instructions

### Prerequisites

- Node.js 18+
- npm or yarn
- DeepSeek API key (for reasoning)
- Doubao API key (for embeddings)

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
Edit `.env.local` and add your API keys:
```bash
# DeepSeek API Configuration (Reasoning)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
DEEPSEEK_CHAT_MODEL=deepseek-chat
DEEPSEEK_REASONING_MODEL=deepseek-r1-distill-qwen-32b-250120

# Doubao API Configuration (Embeddings)
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
EMBEDDING_MODEL=doubao-embedding-text-240515

# Optional: OpenAI API key (backup embedding provider)
OPENAI_API_KEY=your_openai_api_key_here
```

3. Get your API keys:
   - **DeepSeek**: Get from your DeepSeek provider (Volcengine/ByteDance)
   - **Doubao**: Visit [ByteDance Volcengine Console](https://console.volcengine.com/)

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

1. **Upload Documents**:
   - Click "Select Files" or drag and drop .txt or .md files
   - Wait for documents to be processed (status will show "Ready" when complete)

2. **Ask Questions**:
   - Type questions about your uploaded documents in the chat interface
   - Doubao embeddings find the most relevant document chunks
   - DeepSeek's reasoning models analyze the context and provide intelligent responses
   - The AI maintains conversation context for follow-up questions

3. **View Sources**:
   - Each response includes source attribution showing which documents were referenced
   - Relevant text chunks are displayed with similarity scores for transparency
   - Chinese AI stack provides more accurate semantic understanding and source identification
