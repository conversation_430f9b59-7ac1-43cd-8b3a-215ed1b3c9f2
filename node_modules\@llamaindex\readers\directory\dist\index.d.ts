import { BaseReader, FileReader, Document } from '@llamaindex/core/schema';

type ReaderCallback = (category: "file" | "directory", name: string, status: ReaderStatus, message?: string) => boolean;
declare enum ReaderStatus {
    STARTED = 0,
    COMPLETE = 1,
    ERROR = 2
}
type SimpleDirectoryReaderLoadDataParams = {
    directoryPath: string;
    defaultReader?: FileReader | null;
    fileExtToReader?: Record<string, FileReader>;
    numWorkers?: number;
    overrideReader?: FileReader;
};
declare class AbstractSimpleDirectoryReader implements BaseReader {
    private observer?;
    constructor(observer?: ReaderCallback | undefined);
    loadData(params: SimpleDirectoryReaderLoadDataParams): Promise<Document[]>;
    loadData(directoryPath: string): Promise<Document[]>;
    private processFile;
    private doObserverCheck;
}

declare const FILE_EXT_TO_READER: Record<string, FileReader>;
/**
 * Read all the documents in a directory.
 * By default, supports the list of file types
 * in the FILE_EXT_TO_READER map.
 */
declare class SimpleDirectoryReader extends AbstractSimpleDirectoryReader {
    loadData(params: SimpleDirectoryReaderLoadDataParams): Promise<Document[]>;
    loadData(directoryPath: string): Promise<Document[]>;
}

export { FILE_EXT_TO_READER, SimpleDirectoryReader };
