Object.defineProperty(exports, '__esModule', { value: true });

var agent = require('@llamaindex/core/agent');
var global = require('@llamaindex/core/global');
var decorator = require('@llamaindex/core/decorator');
var llms = require('@llamaindex/core/llms');
var utils = require('@llamaindex/core/utils');
var env = require('@llamaindex/env');
var tokenizers = require('@llamaindex/env/tokenizers');
var zod = require('openai/helpers/zod');
var schema = require('@llamaindex/core/schema');
var embeddings = require('@llamaindex/core/embeddings');

class OpenAIMessageSender {
    constructor(session){
        this.openaiSession = session;
    }
    sendTextMessage(message, role) {
        const event = {
            type: "conversation.item.create",
            item: {
                type: "message",
                role: "user",
                content: [
                    {
                        type: "input_text",
                        text: message
                    }
                ]
            }
        };
        this.openaiSession.dataChannel?.send(JSON.stringify(event));
        this.openaiSession.sendResponseCreateEvent();
    }
    sendAudioMessage(content, role) {
        const event = {
            type: "conversation.item.create",
            item: {
                type: "message",
                role: "user",
                content: [
                    {
                        type: "input_audio",
                        audio: content.data
                    }
                ]
            }
        };
        this.openaiSession.dataChannel?.send(JSON.stringify(event));
        this.openaiSession.sendResponseCreateEvent();
    }
}

class OpenAILiveSession extends llms.LiveLLMSession {
    get messageSender() {
        return new OpenAIMessageSender(this);
    }
    isResponseDoneEvent(event) {
        return event.type === "response.done";
    }
    isTextEvent(event) {
        return event.response.output && event.response?.output[0]?.type === "message" && event.response?.output[0]?.content[0]?.type === "text";
    }
    isFunctionCallEvent(event) {
        return event.response.output && event.response?.output[0]?.type === "function_call";
    }
    isAudioDeltaEvent(event) {
        return event.type === "response.audio.delta";
    }
    isInterruptedEvent(event) {
        return event.type === "output_audio_buffer.cleared";
    }
    setupAudioTracks(config) {
        if (!this.peerConnection) return;
        this.peerConnection.ontrack = (event)=>{
            if (config?.onTrack) {
                config.onTrack(event.streams[0] || null);
            }
        };
        if (config?.stream) {
            this.audioStream = config.stream;
            this.audioStream.getAudioTracks().forEach((track)=>{
                this.peerConnection?.addTrack(track, this.audioStream);
            });
        }
    }
    async handleEvents(event, toolCalls) {
        if (this.isAudioDeltaEvent(event)) {
            this.pushEventToQueue({
                type: "audio",
                data: event.delta,
                mimeType: "audio/wav"
            });
        }
        if (this.isResponseDoneEvent(event)) {
            if (this.isTextEvent(event)) {
                this.pushEventToQueue({
                    type: "text",
                    text: event.response?.output[0]?.content[0]?.text
                });
            }
            if (this.isFunctionCallEvent(event)) {
                await this.handleFunctionCall(event, toolCalls);
            }
            if (this.isInterruptedEvent(event)) {
                this.pushEventToQueue({
                    type: "interrupted"
                });
            }
        }
    }
    async sendToolCallResponses(eventToolCalls, toolCalls) {
        for (const toolCall of eventToolCalls){
            const tool = toolCalls.find((t)=>t.metadata.name === toolCall.name);
            if (tool && tool.call) {
                const response = await tool.call(toolCall.args);
                this.sendToolCallResponse(toolCall.call_id, response);
            }
        }
    }
    async sendResponseCreateEvent() {
        this.dataChannel?.send(JSON.stringify({
            type: "response.create"
        }));
    }
    sendToolCallResponse(callId, response) {
        if (!this.dataChannel) {
            throw new Error("Data channel not connected");
        }
        const event = {
            type: "conversation.item.create",
            item: {
                type: "function_call_output",
                call_id: callId,
                output: JSON.stringify(response)
            }
        };
        this.dataChannel.send(JSON.stringify(event));
        this.sendResponseCreateEvent();
    }
    async handleFunctionCall(event, toolCalls) {
        const eventToolCalls = event.response.output.map(// eslint-disable-next-line @typescript-eslint/no-explicit-any
        (output)=>{
            return {
                name: output.name,
                call_id: output.call_id,
                args: typeof output.arguments === "string" ? JSON.parse(output.arguments) : output.arguments
            };
        });
        if (eventToolCalls && eventToolCalls.length > 0) {
            await this.sendToolCallResponses(eventToolCalls, toolCalls);
        }
    }
    async disconnect() {
        if (this.dataChannel) {
            this.dataChannel.close();
            this.dataChannel = undefined;
        }
        if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = undefined;
        }
        this.closed = true;
        if (this.audioStream) {
            this.audioStream.getTracks().forEach((track)=>{
                track.stop();
            });
            this.audioStream = undefined;
        }
    }
    setPeerConnection(pc) {
        this.peerConnection = pc;
    }
    setDataChannel(dc) {
        this.dataChannel = dc;
    }
    constructor(...args){
        super(...args), this.closed = false;
    }
}

const GPT4_MODELS = {
    "chatgpt-4o-latest": {
        contextWindow: 128000
    },
    "gpt-4.5-preview": {
        contextWindow: 128000
    },
    "gpt-4.5-preview-2025-02-27": {
        contextWindow: 128000
    },
    "gpt-4": {
        contextWindow: 8192
    },
    "gpt-4-32k": {
        contextWindow: 32768
    },
    "gpt-4-32k-0613": {
        contextWindow: 32768
    },
    "gpt-4-turbo": {
        contextWindow: 128000
    },
    "gpt-4-turbo-preview": {
        contextWindow: 128000
    },
    "gpt-4-1106-preview": {
        contextWindow: 128000
    },
    "gpt-4-0125-preview": {
        contextWindow: 128000
    },
    "gpt-4-vision-preview": {
        contextWindow: 128000
    },
    "gpt-4o": {
        contextWindow: 128000
    },
    "gpt-4o-2024-05-13": {
        contextWindow: 128000
    },
    "gpt-4o-mini": {
        contextWindow: 128000
    },
    "gpt-4o-mini-2024-07-18": {
        contextWindow: 128000
    },
    "gpt-4o-2024-08-06": {
        contextWindow: 128000
    },
    "gpt-4o-2024-09-14": {
        contextWindow: 128000
    },
    "gpt-4o-2024-10-14": {
        contextWindow: 128000
    },
    "gpt-4-0613": {
        contextWindow: 128000
    },
    "gpt-4-turbo-2024-04-09": {
        contextWindow: 128000
    },
    "gpt-4-0314": {
        contextWindow: 128000
    },
    "gpt-4-32k-0314": {
        contextWindow: 32768
    },
    "gpt-4o-realtime-preview": {
        contextWindow: 128000
    },
    "gpt-4o-realtime-preview-2024-10-01": {
        contextWindow: 128000
    },
    "gpt-4o-realtime-preview-2024-12-17": {
        contextWindow: 128000
    },
    "gpt-4o-realtime-preview-2025-06-03": {
        contextWindow: 128000
    },
    "gpt-4o-audio-preview": {
        contextWindow: 128000
    },
    "gpt-4o-audio-preview-2024-10-01": {
        contextWindow: 128000
    },
    "gpt-4o-2024-11-20": {
        contextWindow: 128000
    },
    "gpt-4o-audio-preview-2024-12-17": {
        contextWindow: 128000
    },
    "gpt-4o-mini-audio-preview": {
        contextWindow: 128000
    },
    "gpt-4o-mini-audio-preview-2024-12-17": {
        contextWindow: 128000
    },
    "gpt-4o-search-preview": {
        contextWindow: 128000
    },
    "gpt-4o-mini-search-preview": {
        contextWindow: 128000
    },
    "gpt-4o-mini-realtime-preview-2024-12-17": {
        contextWindow: 128000
    },
    "gpt-4o-search-preview-2025-03-11": {
        contextWindow: 128000
    },
    "gpt-4o-mini-search-preview-2025-03-11": {
        contextWindow: 128000
    },
    "gpt-4.1": {
        contextWindow: 10 ** 6
    },
    "gpt-4.1-mini": {
        contextWindow: 10 ** 6
    },
    "gpt-4.1-nano": {
        contextWindow: 10 ** 6
    },
    "gpt-4.1-2025-04-14": {
        contextWindow: 10 ** 6
    },
    "gpt-4.1-mini-2025-04-14": {
        contextWindow: 10 ** 6
    },
    "gpt-4.1-nano-2025-04-14": {
        contextWindow: 10 ** 6
    }
};
// NOTE we don't currently support gpt-3.5-turbo-instruct and don't plan to in the near future
const GPT35_MODELS = {
    "gpt-3.5-turbo": {
        contextWindow: 16385
    },
    "gpt-3.5-turbo-0613": {
        contextWindow: 4096
    },
    "gpt-3.5-turbo-16k": {
        contextWindow: 16385
    },
    "gpt-3.5-turbo-16k-0613": {
        contextWindow: 16385
    },
    "gpt-3.5-turbo-1106": {
        contextWindow: 16385
    },
    "gpt-3.5-turbo-0125": {
        contextWindow: 16385
    },
    "gpt-3.5-turbo-0301": {
        contextWindow: 16385
    }
};
const O1_MODELS = {
    "o1-preview": {
        contextWindow: 128000
    },
    "o1-preview-2024-09-12": {
        contextWindow: 128000
    },
    "o1-mini": {
        contextWindow: 128000
    },
    "o1-mini-2024-09-12": {
        contextWindow: 128000
    },
    o1: {
        contextWindow: 128000
    },
    "o1-2024-12-17": {
        contextWindow: 128000
    }
};
const O3_MODELS = {
    "o3-mini": {
        contextWindow: 200000
    },
    "o3-mini-2025-01-31": {
        contextWindow: 200000
    },
    o3: {
        contextWindow: 200000
    },
    "o3-2025-04-16": {
        contextWindow: 200000
    }
};
const O4_MODELS = {
    "o4-mini": {
        contextWindow: 200000
    },
    "o4-mini-2025-04-16": {
        contextWindow: 200000
    }
};
/**
 * We currently support GPT-3.5 and GPT-4 models
 */ const ALL_AVAILABLE_OPENAI_MODELS = {
    ...GPT4_MODELS,
    ...GPT35_MODELS,
    ...O1_MODELS,
    ...O3_MODELS,
    ...O4_MODELS,
    "codex-mini-latest": {
        contextWindow: 200000
    }
};
function isFunctionCallingModel(llm) {
    let model;
    if (llm instanceof OpenAI) {
        model = llm.model;
    } else if ("model" in llm && typeof llm.model === "string") {
        model = llm.model;
    } else {
        return false;
    }
    const isChatModel = Object.keys(ALL_AVAILABLE_OPENAI_MODELS).includes(model);
    const isOld = model.includes("0314") || model.includes("0301");
    const isO1 = model.startsWith("o1");
    return isChatModel && !isOld && !isO1;
}
function isReasoningModel(model) {
    const isO1 = model.startsWith("o1");
    const isO3 = model.startsWith("o3");
    const isO4 = model.startsWith("o4");
    return isO1 || isO3 || isO4;
}
function isTemperatureSupported(model) {
    return !model.startsWith("o3") && !model.startsWith("o4");
}
function mapModalityToOpenAIModality(responseModalities) {
    return responseModalities === schema.ModalityType.TEXT ? "text" : responseModalities === schema.ModalityType.AUDIO ? "audio" : "video";
}
const toOpenAILiveTool = (tool)=>{
    return {
        type: "function",
        name: tool.metadata.name,
        description: tool.metadata.description,
        parameters: tool.metadata.parameters ?? {}
    };
};

const REALTIME_MODELS = [
    "gpt-4o-realtime-preview-2025-06-03",
    "gpt-4o-realtime-preview-2024-12-17",
    "gpt-4o-realtime-preview-2024-10-01"
];
class OpenAILive extends llms.LiveLLM {
    constructor(init){
        super();
        this.apiKey = init?.apiKey;
        this.model = init?.model ?? "gpt-4o-realtime-preview-2025-06-03";
        this.voiceName = init?.voiceName;
        this.baseURL = "https://api.openai.com/v1/realtime";
        if (!this.apiKey) {
            throw new Error("OPENAI_API_KEY is not set");
        }
        this.capabilities.add(llms.LiveLLMCapability.EPHEMERAL_KEY);
        this.capabilities.add(llms.LiveLLMCapability.AUDIO_CONFIG);
    }
    async getEphemeralKey() {
        if (!REALTIME_MODELS.includes(this.model)) {
            throw new Error("Ephemeral key is only supported for gpt-4o-realtime-preview models");
        }
        const response = await fetch(`${this.baseURL}/sessions`, {
            method: "POST",
            headers: {
                Authorization: `Bearer ${this.apiKey}`,
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                model: this.model,
                voice: this.voiceName
            })
        });
        const data = await response.json();
        return data.client_secret.value;
    }
    async getSDPResponse(offer) {
        const sdpResponse = await fetch(`${this.baseURL}?model=${this.model}`, {
            method: "POST",
            body: offer.sdp,
            headers: {
                Authorization: `Bearer ${this.apiKey}`,
                "Content-Type": "application/sdp"
            }
        });
        return sdpResponse;
    }
    async establishSDPConnection(session) {
        const offer = await session.peerConnection.createOffer();
        await session.peerConnection.setLocalDescription(offer);
        if (!offer.sdp) {
            throw new Error("Failed to create SDP offer");
        }
        const sdpResponse = await this.getSDPResponse(offer);
        const answer = {
            sdp: await sdpResponse.text(),
            type: "answer"
        };
        await session.peerConnection.setRemoteDescription(answer);
    }
    async initializeRTCPeerConnectionAndDataChannel(session, config) {
        session.peerConnection = new RTCPeerConnection();
        session.setupAudioTracks(config?.audioConfig);
        session.dataChannel = session.peerConnection.createDataChannel("oai-events");
    }
    async setupWebRTC(session, config) {
        this.initializeRTCPeerConnectionAndDataChannel(session, config);
        await this.establishSDPConnection(session);
    }
    async connect(config) {
        const session = new OpenAILiveSession();
        await this.setupWebRTC(session, config);
        this.setupEventListeners(session, config);
        return session;
    }
    setupEventListeners(session, config) {
        this.messageEventListener(session, config);
        this.openEventListener(session, config);
        this.errorEventListener(session);
    }
    messageEventListener(session, config) {
        session.dataChannel?.addEventListener("message", (event)=>{
            session.handleEvents(JSON.parse(event.data), config?.tools ?? []);
        });
    }
    openEventListener(session, config) {
        session.dataChannel?.addEventListener("open", ()=>{
            const event = {
                type: "session.update",
                session: {
                    voice: this.voiceName,
                    instructions: config?.systemInstruction,
                    tools: config?.tools?.map(toOpenAILiveTool),
                    modalities: config?.responseModality?.map(mapModalityToOpenAIModality)
                }
            };
            session.dataChannel?.send(JSON.stringify(event));
            session.pushEventToQueue({
                type: "open"
            });
        });
    }
    errorEventListener(session) {
        session.dataChannel?.addEventListener("error", (event)=>{
            session.pushEventToQueue({
                type: "error",
                error: event
            });
        });
    }
}

function applyDecs2203RFactory$1() {
    function createAddInitializerMethod(initializers, decoratorFinishedRef) {
        return function addInitializer(initializer) {
            assertNotFinished(decoratorFinishedRef, "addInitializer");
            assertCallable(initializer, "An initializer");
            initializers.push(initializer);
        };
    }
    function memberDec(dec, name, desc, initializers, kind, isStatic, isPrivate, metadata, value) {
        var kindStr;
        switch(kind){
            case 1:
                kindStr = "accessor";
                break;
            case 2:
                kindStr = "method";
                break;
            case 3:
                kindStr = "getter";
                break;
            case 4:
                kindStr = "setter";
                break;
            default:
                kindStr = "field";
        }
        var ctx = {
            kind: kindStr,
            name: isPrivate ? "#" + name : name,
            static: isStatic,
            private: isPrivate,
            metadata: metadata
        };
        var decoratorFinishedRef = {
            v: false
        };
        ctx.addInitializer = createAddInitializerMethod(initializers, decoratorFinishedRef);
        var get, set;
        if (kind === 0) {
            if (isPrivate) {
                get = desc.get;
                set = desc.set;
            } else {
                get = function() {
                    return this[name];
                };
                set = function(v) {
                    this[name] = v;
                };
            }
        } else if (kind === 2) {
            get = function() {
                return desc.value;
            };
        } else {
            if (kind === 1 || kind === 3) {
                get = function() {
                    return desc.get.call(this);
                };
            }
            if (kind === 1 || kind === 4) {
                set = function(v) {
                    desc.set.call(this, v);
                };
            }
        }
        ctx.access = get && set ? {
            get: get,
            set: set
        } : get ? {
            get: get
        } : {
            set: set
        };
        try {
            return dec(value, ctx);
        } finally{
            decoratorFinishedRef.v = true;
        }
    }
    function assertNotFinished(decoratorFinishedRef, fnName) {
        if (decoratorFinishedRef.v) {
            throw new Error("attempted to call " + fnName + " after decoration was finished");
        }
    }
    function assertCallable(fn, hint) {
        if (typeof fn !== "function") {
            throw new TypeError(hint + " must be a function");
        }
    }
    function assertValidReturnValue(kind, value) {
        var type = typeof value;
        if (kind === 1) {
            if (type !== "object" || value === null) {
                throw new TypeError("accessor decorators must return an object with get, set, or init properties or void 0");
            }
            if (value.get !== undefined) {
                assertCallable(value.get, "accessor.get");
            }
            if (value.set !== undefined) {
                assertCallable(value.set, "accessor.set");
            }
            if (value.init !== undefined) {
                assertCallable(value.init, "accessor.init");
            }
        } else if (type !== "function") {
            var hint;
            if (kind === 0) {
                hint = "field";
            } else if (kind === 10) {
                hint = "class";
            } else {
                hint = "method";
            }
            throw new TypeError(hint + " decorators must return a function or void 0");
        }
    }
    function applyMemberDec(ret, base, decInfo, name, kind, isStatic, isPrivate, initializers, metadata) {
        var decs = decInfo[0];
        var desc, init, value;
        if (isPrivate) {
            if (kind === 0 || kind === 1) {
                desc = {
                    get: decInfo[3],
                    set: decInfo[4]
                };
            } else if (kind === 3) {
                desc = {
                    get: decInfo[3]
                };
            } else if (kind === 4) {
                desc = {
                    set: decInfo[3]
                };
            } else {
                desc = {
                    value: decInfo[3]
                };
            }
        } else if (kind !== 0) {
            desc = Object.getOwnPropertyDescriptor(base, name);
        }
        if (kind === 1) {
            value = {
                get: desc.get,
                set: desc.set
            };
        } else if (kind === 2) {
            value = desc.value;
        } else if (kind === 3) {
            value = desc.get;
        } else if (kind === 4) {
            value = desc.set;
        }
        var newValue, get, set;
        if (typeof decs === "function") {
            newValue = memberDec(decs, name, desc, initializers, kind, isStatic, isPrivate, metadata, value);
            if (newValue !== void 0) {
                assertValidReturnValue(kind, newValue);
                if (kind === 0) {
                    init = newValue;
                } else if (kind === 1) {
                    init = newValue.init;
                    get = newValue.get || value.get;
                    set = newValue.set || value.set;
                    value = {
                        get: get,
                        set: set
                    };
                } else {
                    value = newValue;
                }
            }
        } else {
            for(var i = decs.length - 1; i >= 0; i--){
                var dec = decs[i];
                newValue = memberDec(dec, name, desc, initializers, kind, isStatic, isPrivate, metadata, value);
                if (newValue !== void 0) {
                    assertValidReturnValue(kind, newValue);
                    var newInit;
                    if (kind === 0) {
                        newInit = newValue;
                    } else if (kind === 1) {
                        newInit = newValue.init;
                        get = newValue.get || value.get;
                        set = newValue.set || value.set;
                        value = {
                            get: get,
                            set: set
                        };
                    } else {
                        value = newValue;
                    }
                    if (newInit !== void 0) {
                        if (init === void 0) {
                            init = newInit;
                        } else if (typeof init === "function") {
                            init = [
                                init,
                                newInit
                            ];
                        } else {
                            init.push(newInit);
                        }
                    }
                }
            }
        }
        if (kind === 0 || kind === 1) {
            if (init === void 0) {
                init = function(instance, init) {
                    return init;
                };
            } else if (typeof init !== "function") {
                var ownInitializers = init;
                init = function(instance, init) {
                    var value = init;
                    for(var i = 0; i < ownInitializers.length; i++){
                        value = ownInitializers[i].call(instance, value);
                    }
                    return value;
                };
            } else {
                var originalInitializer = init;
                init = function(instance, init) {
                    return originalInitializer.call(instance, init);
                };
            }
            ret.push(init);
        }
        if (kind !== 0) {
            if (kind === 1) {
                desc.get = value.get;
                desc.set = value.set;
            } else if (kind === 2) {
                desc.value = value;
            } else if (kind === 3) {
                desc.get = value;
            } else if (kind === 4) {
                desc.set = value;
            }
            if (isPrivate) {
                if (kind === 1) {
                    ret.push(function(instance, args) {
                        return value.get.call(instance, args);
                    });
                    ret.push(function(instance, args) {
                        return value.set.call(instance, args);
                    });
                } else if (kind === 2) {
                    ret.push(value);
                } else {
                    ret.push(function(instance, args) {
                        return value.call(instance, args);
                    });
                }
            } else {
                Object.defineProperty(base, name, desc);
            }
        }
    }
    function applyMemberDecs(Class, decInfos, metadata) {
        var ret = [];
        var protoInitializers;
        var staticInitializers;
        var existingProtoNonFields = new Map();
        var existingStaticNonFields = new Map();
        for(var i = 0; i < decInfos.length; i++){
            var decInfo = decInfos[i];
            if (!Array.isArray(decInfo)) continue;
            var kind = decInfo[1];
            var name = decInfo[2];
            var isPrivate = decInfo.length > 3;
            var isStatic = kind >= 5;
            var base;
            var initializers;
            if (isStatic) {
                base = Class;
                kind = kind - 5;
                staticInitializers = staticInitializers || [];
                initializers = staticInitializers;
            } else {
                base = Class.prototype;
                protoInitializers = protoInitializers || [];
                initializers = protoInitializers;
            }
            if (kind !== 0 && !isPrivate) {
                var existingNonFields = isStatic ? existingStaticNonFields : existingProtoNonFields;
                var existingKind = existingNonFields.get(name) || 0;
                if (existingKind === true || existingKind === 3 && kind !== 4 || existingKind === 4 && kind !== 3) {
                    throw new Error("Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: " + name);
                } else if (!existingKind && kind > 2) {
                    existingNonFields.set(name, kind);
                } else {
                    existingNonFields.set(name, true);
                }
            }
            applyMemberDec(ret, base, decInfo, name, kind, isStatic, isPrivate, initializers, metadata);
        }
        pushInitializers(ret, protoInitializers);
        pushInitializers(ret, staticInitializers);
        return ret;
    }
    function pushInitializers(ret, initializers) {
        if (initializers) {
            ret.push(function(instance) {
                for(var i = 0; i < initializers.length; i++){
                    initializers[i].call(instance);
                }
                return instance;
            });
        }
    }
    function applyClassDecs(targetClass, classDecs, metadata) {
        if (classDecs.length > 0) {
            var initializers = [];
            var newClass = targetClass;
            var name = targetClass.name;
            for(var i = classDecs.length - 1; i >= 0; i--){
                var decoratorFinishedRef = {
                    v: false
                };
                try {
                    var nextNewClass = classDecs[i](newClass, {
                        kind: "class",
                        name: name,
                        addInitializer: createAddInitializerMethod(initializers, decoratorFinishedRef),
                        metadata
                    });
                } finally{
                    decoratorFinishedRef.v = true;
                }
                if (nextNewClass !== undefined) {
                    assertValidReturnValue(10, nextNewClass);
                    newClass = nextNewClass;
                }
            }
            return [
                defineMetadata(newClass, metadata),
                function() {
                    for(var i = 0; i < initializers.length; i++){
                        initializers[i].call(newClass);
                    }
                }
            ];
        }
    }
    function defineMetadata(Class, metadata) {
        return Object.defineProperty(Class, Symbol.metadata || Symbol.for("Symbol.metadata"), {
            configurable: true,
            enumerable: true,
            value: metadata
        });
    }
    return function applyDecs2203R(targetClass, memberDecs, classDecs, parentClass) {
        if (parentClass !== void 0) {
            var parentMetadata = parentClass[Symbol.metadata || Symbol.for("Symbol.metadata")];
        }
        var metadata = Object.create(parentMetadata === void 0 ? null : parentMetadata);
        var e = applyMemberDecs(targetClass, memberDecs, metadata);
        if (!classDecs.length) defineMetadata(targetClass, metadata);
        return {
            e: e,
            get c () {
                return applyClassDecs(targetClass, classDecs, metadata);
            }
        };
    };
}
function _apply_decs_2203_r$1(targetClass, memberDecs, classDecs, parentClass) {
    return (_apply_decs_2203_r$1 = applyDecs2203RFactory$1())(targetClass, memberDecs, classDecs, parentClass);
}
var _initProto$1;
class OpenAI extends llms.ToolCallLLM {
    static{
        ({ e: [_initProto$1] } = _apply_decs_2203_r$1(this, [
            [
                [
                    decorator.wrapEventCaller,
                    decorator.wrapLLMEvent
                ],
                2,
                "chat"
            ],
            [
                decorator.wrapEventCaller,
                2,
                "streamChat"
            ]
        ], []));
    }
    #session;
    get session() {
        if (!this.#session) {
            this.#session = this.lazySession();
        }
        return this.#session;
    }
    constructor(init){
        super(), // OpenAI session params
        this.apiKey = (_initProto$1(this), undefined), this.baseURL = undefined, this.#session = null;
        this.model = init?.model ?? "gpt-4o";
        this.temperature = init?.temperature ?? 0.1;
        this.reasoningEffort = isReasoningModel(this.model) ? init?.reasoningEffort : undefined;
        this.topP = init?.topP ?? 1;
        this.maxTokens = init?.maxTokens ?? undefined;
        this.maxRetries = init?.maxRetries ?? 10;
        this.timeout = init?.timeout ?? 60 * 1000; // Default is 60 seconds
        this.additionalChatOptions = init?.additionalChatOptions;
        this.additionalSessionOptions = init?.additionalSessionOptions;
        this.apiKey = init?.session?.apiKey ?? init?.apiKey;
        this.baseURL = init?.session?.baseURL ?? init?.baseURL;
        this.voiceName = init?.voiceName;
        this.lazySession = async ()=>init?.session ?? import('openai').then(({ OpenAI })=>{
                return new OpenAI({
                    apiKey: this.apiKey ?? env.getEnv("OPENAI_API_KEY"),
                    baseURL: this.baseURL ?? env.getEnv("OPENAI_BASE_URL"),
                    maxRetries: this.maxRetries,
                    timeout: this.timeout,
                    ...this.additionalSessionOptions
                });
            });
    }
    get supportToolCall() {
        return isFunctionCallingModel(this);
    }
    get live() {
        if (!this._live) {
            this._live = new OpenAILive({
                apiKey: this.apiKey,
                voiceName: this.voiceName,
                model: this.model
            });
        }
        return this._live;
    }
    get metadata() {
        const contextWindow = ALL_AVAILABLE_OPENAI_MODELS[this.model]?.contextWindow ?? 1024;
        return {
            model: this.model,
            temperature: this.temperature,
            topP: this.topP,
            maxTokens: this.maxTokens,
            contextWindow,
            tokenizer: tokenizers.Tokenizers.CL100K_BASE,
            structuredOutput: true
        };
    }
    static toOpenAIRole(messageType) {
        switch(messageType){
            case "user":
                return "user";
            case "assistant":
                return "assistant";
            case "system":
                return "system";
            default:
                return "user";
        }
    }
    static toOpenAIMessage(messages) {
        return messages.map((message)=>{
            const options = message.options ?? {};
            if ("toolResult" in options) {
                return {
                    tool_call_id: options.toolResult.id,
                    role: "tool",
                    content: utils.extractText(message.content)
                };
            } else if ("toolCall" in options) {
                return {
                    role: "assistant",
                    content: utils.extractText(message.content),
                    tool_calls: options.toolCall.map((toolCall)=>{
                        return {
                            id: toolCall.id,
                            type: "function",
                            function: {
                                name: toolCall.name,
                                arguments: typeof toolCall.input === "string" ? toolCall.input : JSON.stringify(toolCall.input)
                            }
                        };
                    })
                };
            } else if (message.role === "user") {
                if (typeof message.content === "string") {
                    return {
                        role: "user",
                        content: message.content
                    };
                }
                return {
                    role: "user",
                    content: message.content.map((item, index)=>{
                        // Handle MessageContentMediaDetail (audio, video, image)
                        if ("data" in item && "mimeType" in item && (item.type === "audio" || item.type === "video" || item.type === "image")) {
                            if (item.type === "audio" || item.type === "video") {
                                throw new Error("Audio and video are not supported");
                            }
                            // Convert image type to file format for OpenAI
                            return {
                                type: "file",
                                file: {
                                    file_data: `data:${item.mimeType};base64,${item.data}`,
                                    filename: `image-${index}.${item.mimeType.split("/")[1] || "png"}`
                                }
                            };
                        }
                        if (item.type === "file") {
                            if (item.mimeType !== "application/pdf") {
                                throw new Error("Only PDF files are supported");
                            }
                            const base64Data = item.data;
                            return {
                                type: "file",
                                file: {
                                    file_data: `data:${item.mimeType};base64,${base64Data}`,
                                    filename: `part-${index}.pdf`
                                }
                            };
                        }
                        // Keep other types as is (text, image_url, etc.)
                        return item;
                    })
                };
            }
            const response = {
                // fixme(alex): type assertion
                role: OpenAI.toOpenAIRole(message.role),
                // fixme: should not extract text, but assert content is string
                content: utils.extractText(message.content)
            };
            return response;
        });
    }
    async chat(params) {
        const { messages, stream, tools, responseFormat, additionalChatOptions } = params;
        const baseRequestParams = {
            model: this.model,
            temperature: this.temperature,
            reasoning_effort: this.reasoningEffort,
            max_tokens: this.maxTokens,
            tools: tools?.map(OpenAI.toTool),
            messages: OpenAI.toOpenAIMessage(messages),
            top_p: this.topP,
            ...Object.assign({}, this.additionalChatOptions, additionalChatOptions)
        };
        if (Array.isArray(baseRequestParams.tools) && baseRequestParams.tools.length === 0) {
            // remove empty tools array to avoid OpenAI error
            delete baseRequestParams.tools;
        }
        if (!isTemperatureSupported(baseRequestParams.model)) delete baseRequestParams.temperature;
        //add response format for the structured output
        if (responseFormat && this.metadata.structuredOutput) {
            // Check if it's a ZodType by looking for its parse and safeParse methods
            if ("parse" in responseFormat && "safeParse" in responseFormat) baseRequestParams.response_format = zod.zodResponseFormat(responseFormat, "response_format");
            else {
                baseRequestParams.response_format = responseFormat;
            }
        }
        // Streaming
        if (stream) {
            return this.streamChat(baseRequestParams);
        }
        // Non-streaming
        const response = await (await this.session).chat.completions.create({
            ...baseRequestParams,
            stream: false
        });
        const content = response.choices[0].message?.content ?? "";
        return {
            raw: response,
            message: {
                content,
                role: response.choices[0].message.role,
                options: response.choices[0].message?.tool_calls ? {
                    toolCall: response.choices[0].message.tool_calls.map((toolCall)=>({
                            id: toolCall.id,
                            name: toolCall.function.name,
                            input: toolCall.function.arguments
                        }))
                } : {}
            }
        };
    }
    // todo: this wrapper is ugly, refactor it
    async *streamChat(baseRequestParams) {
        const stream = await (await this.session).chat.completions.create({
            ...baseRequestParams,
            stream: true
        });
        // TODO: add callback to streamConverter and use streamConverter here
        // this will be used to keep track of the current tool call, make sure input are valid json object.
        let currentToolCall = null;
        const toolCallMap = new Map();
        for await (const part of stream){
            if (part.choices.length === 0) {
                if (part.usage) {
                    yield {
                        raw: part,
                        delta: ""
                    };
                }
                continue;
            }
            const choice = part.choices[0];
            // skip parts that don't have any content
            if (!(choice.delta.content || choice.delta.tool_calls || choice.finish_reason)) continue;
            let shouldEmitToolCall = null;
            if (choice.delta.tool_calls?.[0].id && currentToolCall && choice.delta.tool_calls?.[0].id !== currentToolCall.id) {
                shouldEmitToolCall = {
                    ...currentToolCall,
                    input: JSON.parse(currentToolCall.input)
                };
            }
            if (choice.delta.tool_calls?.[0].id) {
                currentToolCall = {
                    name: choice.delta.tool_calls[0].function.name,
                    id: choice.delta.tool_calls[0].id,
                    input: choice.delta.tool_calls[0].function.arguments
                };
                toolCallMap.set(choice.delta.tool_calls[0].id, currentToolCall);
            } else {
                if (choice.delta.tool_calls?.[0].function?.arguments) {
                    currentToolCall.input += choice.delta.tool_calls[0].function.arguments;
                }
            }
            const isDone = choice.finish_reason !== null;
            if (isDone && currentToolCall) {
                // for the last one, we need to emit the tool call
                shouldEmitToolCall = {
                    ...currentToolCall,
                    input: JSON.parse(currentToolCall.input)
                };
            }
            yield {
                raw: part,
                options: shouldEmitToolCall ? {
                    toolCall: [
                        shouldEmitToolCall
                    ]
                } : currentToolCall ? {
                    toolCall: [
                        currentToolCall
                    ]
                } : {},
                delta: choice.delta.content ?? ""
            };
        }
        toolCallMap.clear();
        return;
    }
    static toTool(tool) {
        return {
            type: "function",
            function: tool.metadata.parameters ? {
                name: tool.metadata.name,
                description: tool.metadata.description,
                parameters: tool.metadata.parameters
            } : {
                name: tool.metadata.name,
                description: tool.metadata.description
            }
        };
    }
}
/**
 * Convenience function to create a new OpenAI instance.
 * @param init - Optional initialization parameters for the OpenAI instance.
 * @returns A new OpenAI instance.
 */ const openai = (init)=>new OpenAI(init);

class OpenAIAgentWorker extends agent.LLMAgentWorker {
}
class OpenAIAgent extends agent.LLMAgent {
    constructor(params){
        const llm = params.llm ?? (global.Settings.llm instanceof OpenAI ? global.Settings.llm : new OpenAI());
        super({
            ...params,
            llm
        });
    }
}

const ALL_OPENAI_EMBEDDING_MODELS = {
    "text-embedding-ada-002": {
        dimensions: 1536,
        maxTokens: 8192,
        tokenizer: tokenizers.Tokenizers.CL100K_BASE
    },
    "text-embedding-3-small": {
        dimensions: 1536,
        dimensionOptions: [
            512,
            1536
        ],
        maxTokens: 8192,
        tokenizer: tokenizers.Tokenizers.CL100K_BASE
    },
    "text-embedding-3-large": {
        dimensions: 3072,
        dimensionOptions: [
            256,
            1024,
            3072
        ],
        maxTokens: 8192,
        tokenizer: tokenizers.Tokenizers.CL100K_BASE
    }
};
class OpenAIEmbedding extends embeddings.BaseEmbedding {
    #session;
    get session() {
        if (!this.#session) {
            this.#session = this.lazySession();
        }
        return this.#session;
    }
    /**
   * OpenAI Embedding
   * @param init - initial parameters
   */ constructor(init){
        super(), // OpenAI session params
        /** api key */ this.apiKey = undefined, /** base url */ this.baseURL = undefined, this.#session = null, /**
   * Get embeddings for a batch of texts
   * @param texts
   */ this.getTextEmbeddings = async (texts)=>{
            return this.getOpenAIEmbedding(texts);
        };
        this.model = init?.model ?? "text-embedding-ada-002";
        this.dimensions = init?.dimensions; // if no dimensions provided, will be undefined/not sent to OpenAI
        this.embedBatchSize = init?.embedBatchSize ?? 10;
        this.maxRetries = init?.maxRetries ?? 10;
        this.timeout = init?.timeout ?? 60 * 1000; // Default is 60 seconds
        this.additionalSessionOptions = init?.additionalSessionOptions;
        // find metadata for model
        const key = Object.keys(ALL_OPENAI_EMBEDDING_MODELS).find((key)=>key === this.model);
        if (key) {
            this.embedInfo = ALL_OPENAI_EMBEDDING_MODELS[key];
        }
        this.apiKey = init?.session?.apiKey ?? init?.apiKey; // Don't fallback to env here, handled in lazySession
        this.baseURL = init?.session?.baseURL ?? init?.baseURL; // Don't fallback to env here, handled in lazySession
        this.lazySession = async ()=>init?.session ?? import('openai').then(({ OpenAI })=>{
                return new OpenAI({
                    apiKey: this.apiKey ?? env.getEnv("OPENAI_API_KEY"),
                    baseURL: this.baseURL ?? env.getEnv("OPENAI_BASE_URL"),
                    maxRetries: this.maxRetries,
                    timeout: this.timeout,
                    ...this.additionalSessionOptions
                });
            });
    }
    /**
   * Get embeddings for a batch of texts
   * @param texts
   * @param options
   */ async getOpenAIEmbedding(input) {
        // TODO: ensure this for every sub class by calling it in the base class
        input = this.truncateMaxTokens(input);
        const { data } = await (await this.session).embeddings.create(this.dimensions ? {
            model: this.model,
            dimensions: this.dimensions,
            input
        } : {
            model: this.model,
            input
        });
        return data.map((d)=>d.embedding);
    }
    /**
   * Get embeddings for a single text
   * @param text
   */ async getTextEmbedding(text) {
        return (await this.getOpenAIEmbedding([
            text
        ]))[0];
    }
}

function applyDecs2203RFactory() {
    function createAddInitializerMethod(initializers, decoratorFinishedRef) {
        return function addInitializer(initializer) {
            assertNotFinished(decoratorFinishedRef, "addInitializer");
            assertCallable(initializer, "An initializer");
            initializers.push(initializer);
        };
    }
    function memberDec(dec, name, desc, initializers, kind, isStatic, isPrivate, metadata, value) {
        var kindStr;
        switch(kind){
            case 1:
                kindStr = "accessor";
                break;
            case 2:
                kindStr = "method";
                break;
            case 3:
                kindStr = "getter";
                break;
            case 4:
                kindStr = "setter";
                break;
            default:
                kindStr = "field";
        }
        var ctx = {
            kind: kindStr,
            name: isPrivate ? "#" + name : name,
            static: isStatic,
            private: isPrivate,
            metadata: metadata
        };
        var decoratorFinishedRef = {
            v: false
        };
        ctx.addInitializer = createAddInitializerMethod(initializers, decoratorFinishedRef);
        var get, set;
        if (kind === 0) {
            if (isPrivate) {
                get = desc.get;
                set = desc.set;
            } else {
                get = function() {
                    return this[name];
                };
                set = function(v) {
                    this[name] = v;
                };
            }
        } else if (kind === 2) {
            get = function() {
                return desc.value;
            };
        } else {
            if (kind === 1 || kind === 3) {
                get = function() {
                    return desc.get.call(this);
                };
            }
            if (kind === 1 || kind === 4) {
                set = function(v) {
                    desc.set.call(this, v);
                };
            }
        }
        ctx.access = get && set ? {
            get: get,
            set: set
        } : get ? {
            get: get
        } : {
            set: set
        };
        try {
            return dec(value, ctx);
        } finally{
            decoratorFinishedRef.v = true;
        }
    }
    function assertNotFinished(decoratorFinishedRef, fnName) {
        if (decoratorFinishedRef.v) {
            throw new Error("attempted to call " + fnName + " after decoration was finished");
        }
    }
    function assertCallable(fn, hint) {
        if (typeof fn !== "function") {
            throw new TypeError(hint + " must be a function");
        }
    }
    function assertValidReturnValue(kind, value) {
        var type = typeof value;
        if (kind === 1) {
            if (type !== "object" || value === null) {
                throw new TypeError("accessor decorators must return an object with get, set, or init properties or void 0");
            }
            if (value.get !== undefined) {
                assertCallable(value.get, "accessor.get");
            }
            if (value.set !== undefined) {
                assertCallable(value.set, "accessor.set");
            }
            if (value.init !== undefined) {
                assertCallable(value.init, "accessor.init");
            }
        } else if (type !== "function") {
            var hint;
            if (kind === 0) {
                hint = "field";
            } else if (kind === 10) {
                hint = "class";
            } else {
                hint = "method";
            }
            throw new TypeError(hint + " decorators must return a function or void 0");
        }
    }
    function applyMemberDec(ret, base, decInfo, name, kind, isStatic, isPrivate, initializers, metadata) {
        var decs = decInfo[0];
        var desc, init, value;
        if (isPrivate) {
            if (kind === 0 || kind === 1) {
                desc = {
                    get: decInfo[3],
                    set: decInfo[4]
                };
            } else if (kind === 3) {
                desc = {
                    get: decInfo[3]
                };
            } else if (kind === 4) {
                desc = {
                    set: decInfo[3]
                };
            } else {
                desc = {
                    value: decInfo[3]
                };
            }
        } else if (kind !== 0) {
            desc = Object.getOwnPropertyDescriptor(base, name);
        }
        if (kind === 1) {
            value = {
                get: desc.get,
                set: desc.set
            };
        } else if (kind === 2) {
            value = desc.value;
        } else if (kind === 3) {
            value = desc.get;
        } else if (kind === 4) {
            value = desc.set;
        }
        var newValue, get, set;
        if (typeof decs === "function") {
            newValue = memberDec(decs, name, desc, initializers, kind, isStatic, isPrivate, metadata, value);
            if (newValue !== void 0) {
                assertValidReturnValue(kind, newValue);
                if (kind === 0) {
                    init = newValue;
                } else if (kind === 1) {
                    init = newValue.init;
                    get = newValue.get || value.get;
                    set = newValue.set || value.set;
                    value = {
                        get: get,
                        set: set
                    };
                } else {
                    value = newValue;
                }
            }
        } else {
            for(var i = decs.length - 1; i >= 0; i--){
                var dec = decs[i];
                newValue = memberDec(dec, name, desc, initializers, kind, isStatic, isPrivate, metadata, value);
                if (newValue !== void 0) {
                    assertValidReturnValue(kind, newValue);
                    var newInit;
                    if (kind === 0) {
                        newInit = newValue;
                    } else if (kind === 1) {
                        newInit = newValue.init;
                        get = newValue.get || value.get;
                        set = newValue.set || value.set;
                        value = {
                            get: get,
                            set: set
                        };
                    } else {
                        value = newValue;
                    }
                    if (newInit !== void 0) {
                        if (init === void 0) {
                            init = newInit;
                        } else if (typeof init === "function") {
                            init = [
                                init,
                                newInit
                            ];
                        } else {
                            init.push(newInit);
                        }
                    }
                }
            }
        }
        if (kind === 0 || kind === 1) {
            if (init === void 0) {
                init = function(instance, init) {
                    return init;
                };
            } else if (typeof init !== "function") {
                var ownInitializers = init;
                init = function(instance, init) {
                    var value = init;
                    for(var i = 0; i < ownInitializers.length; i++){
                        value = ownInitializers[i].call(instance, value);
                    }
                    return value;
                };
            } else {
                var originalInitializer = init;
                init = function(instance, init) {
                    return originalInitializer.call(instance, init);
                };
            }
            ret.push(init);
        }
        if (kind !== 0) {
            if (kind === 1) {
                desc.get = value.get;
                desc.set = value.set;
            } else if (kind === 2) {
                desc.value = value;
            } else if (kind === 3) {
                desc.get = value;
            } else if (kind === 4) {
                desc.set = value;
            }
            if (isPrivate) {
                if (kind === 1) {
                    ret.push(function(instance, args) {
                        return value.get.call(instance, args);
                    });
                    ret.push(function(instance, args) {
                        return value.set.call(instance, args);
                    });
                } else if (kind === 2) {
                    ret.push(value);
                } else {
                    ret.push(function(instance, args) {
                        return value.call(instance, args);
                    });
                }
            } else {
                Object.defineProperty(base, name, desc);
            }
        }
    }
    function applyMemberDecs(Class, decInfos, metadata) {
        var ret = [];
        var protoInitializers;
        var staticInitializers;
        var existingProtoNonFields = new Map();
        var existingStaticNonFields = new Map();
        for(var i = 0; i < decInfos.length; i++){
            var decInfo = decInfos[i];
            if (!Array.isArray(decInfo)) continue;
            var kind = decInfo[1];
            var name = decInfo[2];
            var isPrivate = decInfo.length > 3;
            var isStatic = kind >= 5;
            var base;
            var initializers;
            if (isStatic) {
                base = Class;
                kind = kind - 5;
                staticInitializers = staticInitializers || [];
                initializers = staticInitializers;
            } else {
                base = Class.prototype;
                protoInitializers = protoInitializers || [];
                initializers = protoInitializers;
            }
            if (kind !== 0 && !isPrivate) {
                var existingNonFields = isStatic ? existingStaticNonFields : existingProtoNonFields;
                var existingKind = existingNonFields.get(name) || 0;
                if (existingKind === true || existingKind === 3 && kind !== 4 || existingKind === 4 && kind !== 3) {
                    throw new Error("Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: " + name);
                } else if (!existingKind && kind > 2) {
                    existingNonFields.set(name, kind);
                } else {
                    existingNonFields.set(name, true);
                }
            }
            applyMemberDec(ret, base, decInfo, name, kind, isStatic, isPrivate, initializers, metadata);
        }
        pushInitializers(ret, protoInitializers);
        pushInitializers(ret, staticInitializers);
        return ret;
    }
    function pushInitializers(ret, initializers) {
        if (initializers) {
            ret.push(function(instance) {
                for(var i = 0; i < initializers.length; i++){
                    initializers[i].call(instance);
                }
                return instance;
            });
        }
    }
    function applyClassDecs(targetClass, classDecs, metadata) {
        if (classDecs.length > 0) {
            var initializers = [];
            var newClass = targetClass;
            var name = targetClass.name;
            for(var i = classDecs.length - 1; i >= 0; i--){
                var decoratorFinishedRef = {
                    v: false
                };
                try {
                    var nextNewClass = classDecs[i](newClass, {
                        kind: "class",
                        name: name,
                        addInitializer: createAddInitializerMethod(initializers, decoratorFinishedRef),
                        metadata
                    });
                } finally{
                    decoratorFinishedRef.v = true;
                }
                if (nextNewClass !== undefined) {
                    assertValidReturnValue(10, nextNewClass);
                    newClass = nextNewClass;
                }
            }
            return [
                defineMetadata(newClass, metadata),
                function() {
                    for(var i = 0; i < initializers.length; i++){
                        initializers[i].call(newClass);
                    }
                }
            ];
        }
    }
    function defineMetadata(Class, metadata) {
        return Object.defineProperty(Class, Symbol.metadata || Symbol.for("Symbol.metadata"), {
            configurable: true,
            enumerable: true,
            value: metadata
        });
    }
    return function applyDecs2203R(targetClass, memberDecs, classDecs, parentClass) {
        if (parentClass !== void 0) {
            var parentMetadata = parentClass[Symbol.metadata || Symbol.for("Symbol.metadata")];
        }
        var metadata = Object.create(parentMetadata === void 0 ? null : parentMetadata);
        var e = applyMemberDecs(targetClass, memberDecs, metadata);
        if (!classDecs.length) defineMetadata(targetClass, metadata);
        return {
            e: e,
            get c () {
                return applyClassDecs(targetClass, classDecs, metadata);
            }
        };
    };
}
function _apply_decs_2203_r(targetClass, memberDecs, classDecs, parentClass) {
    return (_apply_decs_2203_r = applyDecs2203RFactory())(targetClass, memberDecs, classDecs, parentClass);
}
var _initProto;
class OpenAIResponses extends llms.ToolCallLLM {
    static{
        ({ e: [_initProto] } = _apply_decs_2203_r(this, [
            [
                decorator.wrapEventCaller,
                2,
                "streamChat"
            ]
        ], []));
    }
    #session;
    constructor(init){
        super(), this.#session = (_initProto(this), null);
        this.model = init?.model ?? "gpt-4o";
        this.temperature = init?.temperature ?? 0.1;
        this.reasoningEffort = isReasoningModel(this.model) ? init?.reasoningEffort : undefined;
        this.topP = init?.topP ?? 1;
        this.maxOutputTokens = init?.maxOutputTokens ?? undefined;
        this.maxRetries = init?.maxRetries ?? 10;
        this.timeout = init?.timeout ?? 60 * 1000;
        this.apiKey = init?.session?.apiKey ?? init?.apiKey ?? env.getEnv("OPENAI_API_KEY");
        this.baseURL = init?.session?.baseURL ?? init?.baseURL ?? env.getEnv("OPENAI_BASE_URL");
        this.additionalSessionOptions = init?.additionalSessionOptions;
        this.additionalChatOptions = init?.additionalChatOptions;
        this.trackPreviousResponses = init?.trackPreviousResponses ?? false;
        this.builtInTools = init?.builtInTools ?? null;
        this.store = init?.store ?? false;
        this.user = init?.user ?? "";
        this.callMetadata = init?.callMetadata ?? {};
        this.strict = init?.strict ?? false;
        this.include = init?.include ?? null;
        this.instructions = init?.instructions ?? "";
        this.previousResponseId = init?.previousResponseId ?? null;
        this.truncation = init?.truncation ?? null;
        this.lazySession = async ()=>init?.session ?? import('openai').then(({ OpenAI })=>{
                return new OpenAI({
                    apiKey: this.apiKey ?? env.getEnv("OPENAI_API_KEY"),
                    baseURL: this.baseURL ?? env.getEnv("OPENAI_BASE_URL"),
                    maxRetries: this.maxRetries,
                    timeout: this.timeout,
                    ...this.additionalSessionOptions
                });
            });
    }
    get session() {
        if (!this.#session) {
            this.#session = this.lazySession();
        }
        return this.#session;
    }
    get supportToolCall() {
        return isFunctionCallingModel(this);
    }
    get metadata() {
        const contextWindow = ALL_AVAILABLE_OPENAI_MODELS[this.model]?.contextWindow ?? 1024;
        return {
            model: this.model,
            temperature: this.temperature,
            topP: this.topP,
            maxTokens: this.maxOutputTokens,
            contextWindow,
            tokenizer: tokenizers.Tokenizers.CL100K_BASE,
            structuredOutput: true
        };
    }
    createInitialMessage() {
        return {
            role: "assistant",
            content: ""
        };
    }
    createInitialOptions() {
        return {
            built_in_tool_calls: []
        };
    }
    isBuiltInToolCall(item) {
        return [
            "file_search_call",
            "computer_call",
            "web_search_call"
        ].includes(item.type);
    }
    isReasoning(item) {
        return item.type === "reasoning";
    }
    isMessageBlock(item) {
        return item.type === "message";
    }
    isFunctionCall(item) {
        return item.type === "function_call";
    }
    isImageGenerationCall(item) {
        return item.type === "image_generation_call" && typeof item.result === "string";
    }
    isResponseCreatedEvent(event) {
        return event.type === "response.created";
    }
    isResponseOutputItemAddedEvent(event) {
        return event.type === "response.output_item.added";
    }
    isToolCallEvent(event) {
        return event.item.type === "function_call";
    }
    isResponseTextDeltaEvent(event) {
        return event.type === "response.output_text.delta";
    }
    isResponseFunctionCallArgumentsDeltaEvent(event) {
        return event.type === "response.function_call_arguments.delta";
    }
    isResponseFunctionCallDoneEvent(event) {
        return event.type === "response.function_call_arguments.done";
    }
    isResponseOutputTextAnnotationAddedEvent(event) {
        return event.type === "response.output_text.annotation.added";
    }
    isResponseFileSearchCallCompletedEvent(event) {
        return event.type === "response.file_search_call.completed";
    }
    isResponseWebSearchCallCompletedEvent(event) {
        return event.type === "response.web_search_call.completed";
    }
    isResponseCompletedEvent(event) {
        return event.type === "response.completed";
    }
    isTextPresent(part) {
        return "text" in part;
    }
    isRefusalPresent(part) {
        return "refusal" in part;
    }
    isAnnotationPresent(part) {
        return "annotations" in part;
    }
    handleResponseOutputMessage(item, options) {
        let outputContent = "";
        for (const part of item.content){
            if (this.isTextPresent(part)) {
                outputContent += part.text;
            }
            if (this.isAnnotationPresent(part)) {
                options.annotations = part.annotations;
            }
            if (this.isRefusalPresent(part)) {
                options.refusal = part.refusal;
            }
        }
        return outputContent;
    }
    extractToolCalls(response) {
        return response.filter((item)=>this.isFunctionCall(item)).map((item)=>{
            return {
                name: item.name,
                id: item.call_id,
                input: item.arguments
            };
        });
    }
    parseResponseOutput(response) {
        const message = this.createInitialMessage();
        const options = this.createInitialOptions();
        const toolCall = this.extractToolCalls(response);
        for (const item of response){
            if (this.isMessageBlock(item)) {
                const outputContent = this.handleResponseOutputMessage(item, options);
                if (outputContent) {
                    llms.addContentPart(message, {
                        type: "text",
                        text: outputContent
                    });
                }
            } else if (this.isImageGenerationCall(item) && item.result) {
                const imagePart = {
                    type: "image",
                    data: item.result,
                    mimeType: "image/png"
                };
                llms.addContentPart(message, imagePart);
            } else if (this.isBuiltInToolCall(item)) {
                options.built_in_tool_calls.push(item);
            } else if (this.isReasoning(item)) {
                options.reasoning = item;
            }
        }
        message.options = {
            ...options,
            toolCall: toolCall
        };
        return message;
    }
    processStreamEvent(event, streamState) {
        switch(true){
            case this.isResponseCreatedEvent(event):
                this.handleResponseCreatedEvent(event, streamState);
                break;
            case this.isResponseOutputItemAddedEvent(event):
                this.handleOutputItemAddedEvent(event, streamState);
                break;
            case this.isResponseTextDeltaEvent(event):
                this.handleTextDeltaEvent(event, streamState);
                break;
            case this.isResponseFunctionCallArgumentsDeltaEvent(event):
                this.handleFunctionCallArgumentsDeltaEvent(event, streamState);
                break;
            case this.isResponseFunctionCallDoneEvent(event):
                this.handleFunctionCallArgumentsDoneEvent(event, streamState);
                break;
            case this.isResponseOutputTextAnnotationAddedEvent(event):
                this.handleOutputTextAnnotationAddedEvent(event, streamState);
                break;
            case this.isResponseFileSearchCallCompletedEvent(event):
            case this.isResponseWebSearchCallCompletedEvent(event):
                this.handleBuiltInToolCallCompletedEvent(event, streamState);
                break;
            case this.isResponseCompletedEvent(event):
                this.handleCompletedEvent(event, streamState);
                break;
        }
    }
    handleResponseCreatedEvent(event, streamState) {
        if (this.trackPreviousResponses) {
            streamState.previousResponseId = event.response.id;
        }
    }
    handleOutputItemAddedEvent(event, streamState) {
        if (this.isToolCallEvent(event)) {
            streamState.currentToolCall = {
                name: event.item.name,
                id: event.item.call_id,
                input: event.item.arguments
            };
        }
    }
    handleTextDeltaEvent(event, streamState) {
        streamState.delta = event.delta;
    }
    handleFunctionCallArgumentsDeltaEvent(event, streamState) {
        if (streamState.currentToolCall) {
            streamState.currentToolCall.input += event.delta;
        }
    }
    handleFunctionCallArgumentsDoneEvent(event, streamState) {
        if (streamState.currentToolCall) {
            streamState.currentToolCall.input = event.arguments;
        }
        streamState.shouldEmitToolCall = {
            ...streamState.currentToolCall,
            input: JSON.parse(streamState.currentToolCall.input)
        };
    }
    handleOutputTextAnnotationAddedEvent(event, streamState) {
        if (!streamState.options.annotations) {
            streamState.options.annotations = [];
        }
        streamState.options.annotations.push(event.annotation);
    }
    handleBuiltInToolCallCompletedEvent(event, streamState) {
        streamState.options.built_in_tool_calls.push(event);
    }
    handleCompletedEvent(event, streamState) {
        if (event.response.usage) {
            streamState.options.usage = event.response.usage;
        }
    }
    createBaseRequestParams(messages, tools, additionalChatOptions) {
        const baseRequestParams = {
            model: this.model,
            include: this.include,
            input: this.toOpenAIResponseMessages(messages),
            tools: this.builtInTools ? [
                ...this.builtInTools
            ] : [],
            instructions: this.instructions,
            max_output_tokens: this.maxOutputTokens,
            previous_response_id: this.previousResponseId,
            store: this.store,
            metadata: this.callMetadata,
            top_p: this.topP,
            truncation: this.truncation,
            user: this.user,
            ...Object.assign({}, this.additionalChatOptions, additionalChatOptions)
        };
        if (tools?.length) {
            if (!baseRequestParams.tools) {
                baseRequestParams.tools = [];
            }
            baseRequestParams.tools.push(...tools.map(this.toResponsesTool.bind(this)));
        }
        return baseRequestParams;
    }
    async chat(params) {
        const { messages, stream, tools, responseFormat, additionalChatOptions } = params;
        const baseRequestParams = this.createBaseRequestParams(messages, tools, additionalChatOptions);
        if (Array.isArray(baseRequestParams.tools) && baseRequestParams.tools.length === 0) {
            // remove empty tools array to avoid OpenAI error
            delete baseRequestParams.tools;
        }
        if (!isTemperatureSupported(baseRequestParams.model)) delete baseRequestParams.temperature;
        if (stream) {
            return this.streamChat(baseRequestParams);
        }
        const response = await (await this.session).responses.create({
            ...baseRequestParams,
            stream: false
        });
        const message = this.parseResponseOutput(response.output);
        return {
            raw: response,
            message
        };
    }
    initalizeStreamState() {
        return {
            delta: "",
            currentToolCall: null,
            shouldEmitToolCall: null,
            options: this.createInitialOptions(),
            previousResponseId: this.previousResponseId
        };
    }
    createResponseChunk(event, state) {
        return {
            raw: event,
            delta: state.delta,
            options: state.shouldEmitToolCall ? {
                toolCall: [
                    state.shouldEmitToolCall
                ]
            } : state.currentToolCall ? {
                toolCall: [
                    state.currentToolCall
                ]
            } : {}
        };
    }
    async *streamChat(baseRequestParams) {
        const streamState = this.initalizeStreamState();
        const stream = await (await this.session).responses.create({
            ...baseRequestParams,
            stream: true
        });
        for await (const event of stream){
            this.processStreamEvent(event, streamState);
            this.handlePreviousResponseId(streamState);
            yield this.createResponseChunk(event, streamState);
        }
    }
    toOpenAIResponsesRole(messageType) {
        switch(messageType){
            case "user":
                return "user";
            case "assistant":
                return "assistant";
            case "system":
                return "system";
            case "developer":
                return "developer";
            default:
                return "user";
        }
    }
    isToolResultPresent(options) {
        return "toolResult" in options;
    }
    isToolCallPresent(options) {
        return "toolCall" in options;
    }
    isUserMessage(message) {
        return message.role === "user";
    }
    handlePreviousResponseId(streamState) {
        if (this.trackPreviousResponses) {
            if (streamState.previousResponseId != this.previousResponseId) {
                this.previousResponseId = streamState.previousResponseId;
            }
        }
    }
    convertToOpenAIToolCallResult(options, content) {
        return {
            type: "function_call_output",
            call_id: options.toolResult.id,
            output: utils.extractText(content)
        };
    }
    convertToOpenAIToolCalls(options) {
        return options.toolCall.map((toolCall)=>{
            return {
                type: "function_call",
                call_id: toolCall.id,
                name: toolCall.name,
                arguments: typeof toolCall.input === "string" ? toolCall.input : JSON.stringify(toolCall.input)
            };
        });
    }
    processMessageContent(content) {
        if (!Array.isArray(content)) {
            return content;
        }
        return content.map((item, index)=>{
            if (item.type === "text") {
                return {
                    type: "input_text",
                    text: item.text
                };
            }
            if (item.type === "image_url") {
                return {
                    type: "input_image",
                    image_url: item.image_url.url,
                    detail: item.detail || "auto"
                };
            }
            if (item.type === "file") {
                if (item.mimeType !== "application/pdf") {
                    throw new Error("Only supports mimeType `application/pdf` for file content.");
                }
                const base64Data = item.data;
                return {
                    type: "input_file",
                    filename: `part-${index}.pdf`,
                    file_data: `data:${item.mimeType};base64,${base64Data}`
                };
            }
            throw new Error("Unsupported content type");
        });
    }
    convertToOpenAIUserMessage(message) {
        const messageContent = this.processMessageContent(message.content);
        return {
            role: "user",
            content: messageContent
        };
    }
    defaultOpenAIResponseMessage(message) {
        const response = {
            role: this.toOpenAIResponsesRole(message.role),
            content: utils.extractText(message.content)
        };
        return response;
    }
    toOpenAIResponseMessage(message) {
        const options = message.options ?? {};
        if (this.isToolResultPresent(options)) {
            return this.convertToOpenAIToolCallResult(options, message.content);
        } else if (this.isToolCallPresent(options)) {
            return this.convertToOpenAIToolCalls(options);
        } else if (this.isUserMessage(message)) {
            return this.convertToOpenAIUserMessage(message);
        }
        return this.defaultOpenAIResponseMessage(message);
    }
    toOpenAIResponseMessages(messages) {
        const finalMessages = [];
        for (const message of messages){
            const processedMessage = this.toOpenAIResponseMessage(message);
            if (Array.isArray(processedMessage)) {
                finalMessages.push(...processedMessage);
            } else {
                finalMessages.push(processedMessage);
            }
        }
        return finalMessages;
    }
    toResponsesTool(tool) {
        return {
            type: "function",
            name: tool.metadata.name,
            description: tool.metadata.description,
            parameters: tool.metadata.parameters ?? {},
            strict: this.strict
        };
    }
}
/**
 * Convenience function to create a new OpenAI instance.
 * @param init - Optional initialization parameters for the OpenAI instance.
 * @returns A new OpenAI instance.
 */ const openaiResponses = (init)=>new OpenAIResponses(init);

exports.ALL_OPENAI_EMBEDDING_MODELS = ALL_OPENAI_EMBEDDING_MODELS;
exports.OpenAI = OpenAI;
exports.OpenAIAgent = OpenAIAgent;
exports.OpenAIAgentWorker = OpenAIAgentWorker;
exports.OpenAIEmbedding = OpenAIEmbedding;
exports.OpenAIResponses = OpenAIResponses;
exports.openai = openai;
exports.openaiResponses = openaiResponses;
