"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Send, Bot, User, Clock, AlertCircle, Wifi, WifiOff } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { MarkdownRenderer } from '@/components/ui/markdown-renderer'

interface Message {
  id: string
  content: string
  sender: 'user' | 'assistant'
  timestamp: Date
  isCode?: boolean
  status?: 'sending' | 'sent' | 'error'
  sources?: Array<{
    document: string
    chunk: string
    relevance: number
  }>
}

interface ChatInterfaceProps {
  messages?: Message[]
  onSendMessage?: (message: string) => void
  isTyping?: boolean
  isConnected?: boolean
  isLoading?: boolean
  placeholder?: string
  maxHeight?: string
}

const TypingIndicator = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="flex items-center space-x-2 px-4 py-3 bg-muted rounded-2xl rounded-bl-md max-w-xs"
    role="status"
    aria-label="Assistant is typing"
  >
    <Bot className="w-4 h-4 text-muted-foreground" />
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-2 h-2 bg-muted-foreground rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: i * 0.2
          }}
        />
      ))}
    </div>
  </motion.div>
)

const MessageBubble = ({ message, index }: { message: Message; index: number }) => {
  const [showTimestamp, setShowTimestamp] = useState(false)
  const isUser = message.sender === 'user'

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.3,
        delay: index * 0.1,
        ease: "easeOut"
      }}
      className={`flex items-end space-x-2 group ${isUser ? 'justify-end' : 'justify-start'}`}
      onMouseEnter={() => setShowTimestamp(true)}
      onMouseLeave={() => setShowTimestamp(false)}
      role="article"
      aria-label={`${message.sender} message`}
    >
      {!isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mb-1">
          <Bot className="w-4 h-4 text-primary" />
        </div>
      )}
      
      <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg`}>
        <motion.div
          className={`px-4 py-3 rounded-2xl relative ${
            isUser
              ? 'bg-primary text-primary-foreground rounded-br-md'
              : 'bg-muted text-foreground rounded-bl-md'
          } ${message.isCode ? 'font-mono text-sm' : ''}`}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          {message.isCode ? (
            <pre className="whitespace-pre-wrap break-words">
              <code>{message.content}</code>
            </pre>
          ) : isUser ? (
            <div className="whitespace-pre-wrap break-words">{message.content}</div>
          ) : (
            <MarkdownRenderer content={message.content} />
          )}
          
          {message.status === 'sending' && (
            <motion.div
              className="absolute -bottom-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
            />
          )}
          
          {message.status === 'error' && (
            <AlertCircle className="absolute -bottom-1 -right-1 w-3 h-3 text-destructive" />
          )}
        </motion.div>

        {/* Sources */}
        {message.sources && message.sources.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            className="mt-2 space-y-1 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg"
          >
            <p className="text-xs font-medium text-muted-foreground">Sources:</p>
            {message.sources.map((source, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-background/50 border border-border rounded-lg p-2"
              >
                <p className="text-xs font-medium">{source.document}</p>
                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                  {source.chunk}
                </p>
                <Badge variant="secondary" className="text-xs mt-1">
                  {Math.round(source.relevance * 100)}% match
                </Badge>
              </motion.div>
            ))}
          </motion.div>
        )}
        
        <AnimatePresence>
          {showTimestamp && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="text-xs text-muted-foreground mt-1 px-2"
            >
              <Clock className="w-3 h-3 inline mr-1" />
              {formatTime(message.timestamp)}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center mb-1">
          <User className="w-4 h-4 text-primary-foreground" />
        </div>
      )}
    </motion.div>
  )
}

const EmptyState = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="flex flex-col items-center justify-center h-64 text-center space-y-4"
  >
    <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
      <Bot className="w-8 h-8 text-muted-foreground" />
    </div>
    <div className="space-y-2">
      <h3 className="text-lg font-semibold text-foreground">Start a conversation</h3>
      <p className="text-sm text-muted-foreground max-w-sm">
        Upload some documents and ask me anything! I'm here to help with your questions.
      </p>
    </div>
  </motion.div>
)

const LoadingState = () => (
  <div className="space-y-4 p-4">
    {[1, 2, 3].map((i) => (
      <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
        <div className="space-y-2">
          <Skeleton className="h-4 w-48" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>
    ))}
  </div>
)

export default function AnimatedChatInterface({
  messages = [],
  onSendMessage = () => {},
  isTyping = false,
  isConnected = true,
  isLoading = false,
  placeholder = "Ask a question about your documents...",
  maxHeight = "600px"
}: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('')
  const [localMessages, setLocalMessages] = useState<Message[]>(messages)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setLocalMessages(messages)
  }, [messages])

  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [localMessages, isTyping, scrollToBottom])

  const handleSendMessage = useCallback(() => {
    if (!inputValue.trim() || !isConnected) return

    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      sender: 'user',
      timestamp: new Date(),
      status: 'sending'
    }

    setLocalMessages(prev => [...prev, newMessage])
    onSendMessage(inputValue.trim())
    setInputValue('')

    setTimeout(() => {
      setLocalMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'sent' }
            : msg
        )
      )
    }, 1000)
  }, [inputValue, isConnected, onSendMessage])

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }, [handleSendMessage])

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  return (
    <Card className="flex flex-col w-full h-full bg-background border-border shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
            <Bot className="w-5 h-5 text-primary" />
          </div>
          <div>
            <h2 className="font-semibold text-foreground">AI Assistant</h2>
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <>
                  <Wifi className="w-3 h-3 text-green-500" />
                  <span className="text-xs text-muted-foreground">Online</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-3 h-3 text-destructive" />
                  <span className="text-xs text-destructive">Disconnected</span>
                </>
              )}
            </div>
          </div>
        </div>
        <Badge variant="secondary" className="text-xs">
          RAG Enabled
        </Badge>
      </div>

      {/* Connection Error Alert */}
      {!isConnected && (
        <Alert className="m-4 mb-0 border-destructive/50 bg-destructive/10">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Connection lost. Please check your internet connection and try again.
          </AlertDescription>
        </Alert>
      )}

      {/* Messages Area */}
      <ScrollArea 
        ref={scrollAreaRef}
        className="flex-1 p-4"
        style={{ maxHeight }}
      >
        {isLoading ? (
          <LoadingState />
        ) : localMessages.length === 0 ? (
          <EmptyState />
        ) : (
          <div className="space-y-4" role="log" aria-label="Chat messages">
            <AnimatePresence>
              {localMessages.map((message, index) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  index={index}
                />
              ))}
              {isTyping && (
                <div className="flex justify-start">
                  <TypingIndicator />
                </div>
              )}
            </AnimatePresence>
          </div>
        )}
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-border">
        <div className="flex space-x-2">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={isConnected ? placeholder : "Reconnecting..."}
            disabled={!isConnected || isLoading}
            className="flex-1"
            aria-label="Message input"
            maxLength={1000}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || !isConnected || isLoading}
            size="icon"
            aria-label="Send message"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <span>{inputValue.length}/1000</span>
        </div>
      </div>
    </Card>
  )
}
