"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-symbol";
exports.ids = ["vendor-chunks/micromark-util-symbol"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/codes.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: () => (/* binding */ codes)\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nconst codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constants: () => (/* binding */ constants)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nconst constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/types.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nconst types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/values.js":
/*!**********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/values.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   values: () => (/* binding */ values)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nconst values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/values.js\n");

/***/ })

};
;