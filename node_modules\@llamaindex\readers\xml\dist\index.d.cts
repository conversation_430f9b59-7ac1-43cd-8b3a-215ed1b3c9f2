import { FileReader, Document } from '@llamaindex/core/schema';

declare class XMLReader extends FileReader<Document> {
    private splitLevel;
    /**
     * @param splitLevel  how deep to split the XML tree
     */
    constructor({ splitLevel }?: {
        splitLevel?: number;
    });
    /** XMLParser */
    loadDataAsContent(fileContent: Uint8Array): Promise<Document[]>;
    /**
     * Internal: split tree into leaf or level-matched nodes
     */
    private _getLeafNodesUpToLevel;
    private _parseElementToDocuments;
}

export { XMLReader };
