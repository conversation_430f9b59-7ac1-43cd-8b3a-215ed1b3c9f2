import * as fs from 'node:fs';
import path from 'node:path';
import { MarkdownReader } from '../../markdown/dist/index.js';

class ObsidianReader {
    constructor(inputDir){
        this.docs = [];
        this.inputDir = inputDir;
    }
    async processPath(file, filepath) {
        if (file.isDirectory() && !file.name.startsWith(".")) {
            await this.readFromPath(filepath);
        } else if (file.isFile() && file.name.endsWith(".md")) {
            await this.convertToDocuments(filepath);
        } else {
            console.log(`Skipping ${filepath}`);
        }
    }
    async readFromPath(dir) {
        const files = await fs.promises.readdir(dir, {
            withFileTypes: true
        });
        for (const file of files){
            const filepath = path.join(dir, file.name);
            await this.processPath(file, filepath);
        }
    }
    async convertToDocuments(filepath) {
        const content = await new MarkdownReader().loadData(filepath);
        this.docs.push(...content);
    }
    async loadData() {
        await this.readFromPath(this.inputDir);
        return this.docs;
    }
}

export { ObsidianReader };
