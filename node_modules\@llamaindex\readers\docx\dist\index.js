import { FileReader, Document } from '@llamaindex/core/schema';
import mammoth from 'mammoth';

class DocxReader extends FileReader {
    /** DocxParser */ async loadDataAsContent(fileContent) {
        // Note: await mammoth.extractRawText({ arrayBuffer: fileContent });  is not working
        // So we need to convert to Buffer first
        const buffer = Buffer.from(fileContent);
        const { value } = await mammoth.extractRawText({
            buffer
        });
        return [
            new Document({
                text: value
            })
        ];
    }
}

export { DocxReader };
