Object.defineProperty(exports, '__esModule', { value: true });

var fs = require('node:fs');
var path = require('node:path');
var index_cjs = require('../../markdown/dist/index.cjs');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var fs__namespace = /*#__PURE__*/_interopNamespace(fs);
var path__default = /*#__PURE__*/_interopDefault(path);

class ObsidianReader {
    constructor(inputDir){
        this.docs = [];
        this.inputDir = inputDir;
    }
    async processPath(file, filepath) {
        if (file.isDirectory() && !file.name.startsWith(".")) {
            await this.readFromPath(filepath);
        } else if (file.isFile() && file.name.endsWith(".md")) {
            await this.convertToDocuments(filepath);
        } else {
            console.log(`Skipping ${filepath}`);
        }
    }
    async readFromPath(dir) {
        const files = await fs__namespace.promises.readdir(dir, {
            withFileTypes: true
        });
        for (const file of files){
            const filepath = path__default.default.join(dir, file.name);
            await this.processPath(file, filepath);
        }
    }
    async convertToDocuments(filepath) {
        const content = await new index_cjs.MarkdownReader().loadData(filepath);
        this.docs.push(...content);
    }
    async loadData() {
        await this.readFromPath(this.inputDir);
        return this.docs;
    }
}

exports.ObsidianReader = ObsidianReader;
