import { FileReader, Document } from '@llamaindex/core/schema';
import { Logger } from '@llamaindex/env';

interface JSONReaderOptions {
    /**
     * The threshold for using streaming mode.
     * Give the approximate size of the JSON data in MB. Estimates character length by calculating: "(streamingThreshold * 1024 * 1024) / 2" and comparing against string.length
     * Streaming mode avoids memory issues when parsing large JSON data. Set "undefined" to disable streaming or "0" to always use streaming.
     *
     * @default 50 MB
     */
    streamingThreshold?: number;
    /**
     * Whether to ensure only ASCII characters.
     * Converts non-ASCII characters to their unicode escape sequence.
     *
     * @default false
     */
    ensureAscii?: boolean;
    /**
     * Whether the JSON is in JSON Lines format.
     * Split into lines, remove empty lines, parse each line as JSON.
     * Note: Uses a custom streaming parser, most likely less robust than json-ext
     *
     * @default false
     */
    isJsonLines?: boolean;
    /**
     * Whether to clean the JSON by filtering out structural characters (`{}, [], and ,`).
     * If set to false, it will just parse the JSON, not removing structural characters.
     *
     * @default true
     */
    cleanJson?: boolean;
    /**
     * Specifies how many levels up the JSON structure to include in the output. cleanJson will be ignored.
     * If set to 0, all levels are included. If undefined, parses the entire JSON and treats each line as an embedding.
     *
     * @default undefined
     */
    levelsBack?: number;
    /**
     * The maximum length of JSON string representation to be collapsed into a single line.
     * Only applicable when `levelsBack` is set.
     *
     * @default undefined
     */
    collapseLength?: number;
    /**
     * A placeholder for a custom logging function.
     *
     * @default consoleLogger
     */
    logger?: Logger;
}
declare class JSONReaderError extends Error {
}
declare class JSONParseError extends JSONReaderError {
}
declare class JSONStringifyError extends JSONReaderError {
}
/**
 * A reader that reads JSON data and returns an array of Document objects.
 * Supports various options to modify the output.
 */
declare class JSONReader extends FileReader<Document> {
    private options;
    constructor(options?: JSONReaderOptions);
    private normalizeOptions;
    /**
     * Loads JSON data and returns an array of Document objects.
     *
     * @param {Uint8Array} content - The JSON data as a Uint8Array.
     * @return {Promise<Document[]>} A Promise that resolves to an array of Document objects.
     */
    loadDataAsContent(content: Uint8Array): Promise<Document[]>;
    private parseJson;
    private createDocument;
    private formatJsonString;
    private prepareDepthFirstYield;
    /**
     * A generator function that determines the next step in traversing the JSON data.
     * If collapseLength is set and the serialized JSON string is not null, it yields the collapsed string.
     * Otherwise it delegates traversal to the `traverseJsonData` function.
     */
    private depthFirstYield;
    /**
     * Traverse the JSON data.
     * If the data is an array, it traverses each item in the array.
     * If the data is an object, it delegates traversal to the `traverseObject` function.
     * If the data is a primitive value, it yields the value with the path.
     *
     */
    private traverseJsonData;
    private traverseObject;
    private serializeAndCollapse;
    private convertToAscii;
}

export { JSONParseError, JSONReader, JSONReaderError, type JSONReaderOptions, JSONStringifyError };
