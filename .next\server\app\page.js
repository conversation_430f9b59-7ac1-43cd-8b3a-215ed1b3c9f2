/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/query-provider.tsx */ \"(rsc)/./src/components/providers/query-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlJTVDJTVDY2hhdGRvYy12MSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDY29kZSU1QyU1Q2NoYXRkb2MtdjElNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDY29kZSU1QyU1Q2NoYXRkb2MtdjElNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDcXVlcnktcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUXVlcnlQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc01BQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJRdWVyeVByb3ZpZGVyXCJdICovIFwiRDpcXFxcY29kZVxcXFxjaGF0ZG9jLXYxXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxxdWVyeS1wcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cchat%5C%5Cchat-interface.tsx%22%2C%22ids%22%3A%5B%22ChatInterface%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Cdocument-upload.tsx%22%2C%22ids%22%3A%5B%22DocumentUpload%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Curl-ingestion.tsx%22%2C%22ids%22%3A%5B%22UrlIngestion%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cchat%5C%5Cchat-interface.tsx%22%2C%22ids%22%3A%5B%22ChatInterface%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Cdocument-upload.tsx%22%2C%22ids%22%3A%5B%22DocumentUpload%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Curl-ingestion.tsx%22%2C%22ids%22%3A%5B%22UrlIngestion%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/chat/chat-interface.tsx */ \"(rsc)/./src/components/chat/chat-interface.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/documents/document-upload.tsx */ \"(rsc)/./src/components/documents/document-upload.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/documents/url-ingestion.tsx */ \"(rsc)/./src/components/documents/url-ingestion.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlJTVDJTVDY2hhdGRvYy12MSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjaGF0JTVDJTVDY2hhdC1pbnRlcmZhY2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQ2hhdEludGVyZmFjZSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDY29kZSU1QyU1Q2NoYXRkb2MtdjElNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDZG9jdW1lbnRzJTVDJTVDZG9jdW1lbnQtdXBsb2FkLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkRvY3VtZW50VXBsb2FkJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlJTVDJTVDY2hhdGRvYy12MSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNkb2N1bWVudHMlNUMlNUN1cmwtaW5nZXN0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlVybEluZ2VzdGlvbiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNExBQXVJO0FBQ3ZJO0FBQ0Esd01BQThJO0FBQzlJO0FBQ0Esb01BQTBJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDaGF0SW50ZXJmYWNlXCJdICovIFwiRDpcXFxcY29kZVxcXFxjaGF0ZG9jLXYxXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGNoYXRcXFxcY2hhdC1pbnRlcmZhY2UudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJEb2N1bWVudFVwbG9hZFwiXSAqLyBcIkQ6XFxcXGNvZGVcXFxcY2hhdGRvYy12MVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxkb2N1bWVudHNcXFxcZG9jdW1lbnQtdXBsb2FkLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVXJsSW5nZXN0aW9uXCJdICovIFwiRDpcXFxcY29kZVxcXFxjaGF0ZG9jLXYxXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGRvY3VtZW50c1xcXFx1cmwtaW5nZXN0aW9uLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cchat%5C%5Cchat-interface.tsx%22%2C%22ids%22%3A%5B%22ChatInterface%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Cdocument-upload.tsx%22%2C%22ids%22%3A%5B%22DocumentUpload%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Curl-ingestion.tsx%22%2C%22ids%22%3A%5B%22UrlIngestion%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxjb2RlXFxjaGF0ZG9jLXYxXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bde9815cf992\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcY29kZVxcY2hhdGRvYy12MVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYmRlOTgxNWNmOTkyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_query_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/query-provider */ \"(rsc)/./src/components/providers/query-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ChatDoc - Chat with Documents\",\n    description: \"Upload documents and chat with them using AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_query_provider__WEBPACK_IMPORTED_MODULE_2__.QueryProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGlCO0FBQytDO0FBSS9ELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsK0VBQWFBOzBCQUNYSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJEOlxcY29kZVxcY2hhdGRvYy12MVxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgeyBRdWVyeVByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9wcm92aWRlcnMvcXVlcnktcHJvdmlkZXJcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJDaGF0RG9jIC0gQ2hhdCB3aXRoIERvY3VtZW50c1wiLFxuICBkZXNjcmlwdGlvbjogXCJVcGxvYWQgZG9jdW1lbnRzIGFuZCBjaGF0IHdpdGggdGhlbSB1c2luZyBBSVwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPFF1ZXJ5UHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L1F1ZXJ5UHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiUXVlcnlQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(rsc)/./src/components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_documents_document_upload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/documents/document-upload */ \"(rsc)/./src/components/documents/document-upload.tsx\");\n/* harmony import */ var _components_documents_url_ingestion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/documents/url-ingestion */ \"(rsc)/./src/components/documents/url-ingestion.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-foreground mb-2\",\n                            children: \"ChatDoc\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground text-lg\",\n                            children: \"Upload documents and chat with them using AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    children: \"Upload Documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 24,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    children: \"Upload .txt or .md files to start chatting\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_documents_document_upload__WEBPACK_IMPORTED_MODULE_2__.DocumentUpload, {}, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_documents_url_ingestion__WEBPACK_IMPORTED_MODULE_3__.UrlIngestion, {}, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"h-[600px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                children: \"Chat with Documents\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Ask questions about your uploaded documents\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_1__.ChatInterface, {}, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ChatInterface = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ChatInterface() from the server but ChatInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\code\\chatdoc-v1\\src\\components\\chat\\chat-interface.tsx",
"ChatInterface",
);

/***/ }),

/***/ "(rsc)/./src/components/documents/document-upload.tsx":
/*!******************************************************!*\
  !*** ./src/components/documents/document-upload.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DocumentUpload: () => (/* binding */ DocumentUpload)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const DocumentUpload = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call DocumentUpload() from the server but DocumentUpload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\code\\chatdoc-v1\\src\\components\\documents\\document-upload.tsx",
"DocumentUpload",
);

/***/ }),

/***/ "(rsc)/./src/components/documents/url-ingestion.tsx":
/*!****************************************************!*\
  !*** ./src/components/documents/url-ingestion.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UrlIngestion: () => (/* binding */ UrlIngestion)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const UrlIngestion = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call UrlIngestion() from the server but UrlIngestion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\code\\chatdoc-v1\\src\\components\\documents\\url-ingestion.tsx",
"UrlIngestion",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/query-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/query-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const QueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\code\\chatdoc-v1\\src\\components\\providers\\query-provider.tsx",
"QueryProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFFRTtBQUVoQyxNQUFNRSxxQkFBT0YsNkNBQWdCLENBRzNCLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCw0REFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkgsS0FBS00sV0FBVyxHQUFHO0FBRW5CLE1BQU1DLDJCQUFhVCw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JJLFdBQVdELFdBQVcsR0FBRztBQUV6QixNQUFNRSwwQkFBWVYsNkNBQWdCLENBR2hDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDSztRQUNDTCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCxzREFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkssVUFBVUYsV0FBVyxHQUFHO0FBRXhCLE1BQU1JLGdDQUFrQlosNkNBQWdCLENBR3RDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDTztRQUNDUCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FBQyxpQ0FBaUNHO1FBQzlDLEdBQUdDLEtBQUs7Ozs7OztBQUdiTyxnQkFBZ0JKLFdBQVcsR0FBRztBQUU5QixNQUFNTSw0QkFBY2QsNkNBQWdCLENBR2xDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUFJRCxLQUFLQTtRQUFLRixXQUFXSCw4Q0FBRUEsQ0FBQyxZQUFZRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUVoRVMsWUFBWU4sV0FBVyxHQUFHO0FBRTFCLE1BQU1PLDJCQUFhZiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLDhCQUE4Qkc7UUFDM0MsR0FBR0MsS0FBSzs7Ozs7O0FBR2JVLFdBQVdQLFdBQVcsR0FBRztBQUV1RCIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXHNyY1xcY29tcG9uZW50c1xcdWlcXGNhcmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgQ2FyZCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJvdW5kZWQtbGcgYm9yZGVyIGJnLWNhcmQgdGV4dC1jYXJkLWZvcmVncm91bmQgc2hhZG93LXNtXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkLmRpc3BsYXlOYW1lID0gXCJDYXJkXCJcblxuY29uc3QgQ2FyZEhlYWRlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJmbGV4IGZsZXgtY29sIHNwYWNlLXktMS41IHAtNlwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkSGVhZGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkSGVhZGVyXCJcblxuY29uc3QgQ2FyZFRpdGxlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxIZWFkaW5nRWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGgzXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBsZWFkaW5nLW5vbmUgdHJhY2tpbmctdGlnaHRcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRUaXRsZS5kaXNwbGF5TmFtZSA9IFwiQ2FyZFRpdGxlXCJcblxuY29uc3QgQ2FyZERlc2NyaXB0aW9uID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxQYXJhZ3JhcGhFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8cFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkRGVzY3JpcHRpb24uZGlzcGxheU5hbWUgPSBcIkNhcmREZXNjcmlwdGlvblwiXG5cbmNvbnN0IENhcmRDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwicC02IHB0LTBcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuKSlcbkNhcmRDb250ZW50LmRpc3BsYXlOYW1lID0gXCJDYXJkQ29udGVudFwiXG5cbmNvbnN0IENhcmRGb290ZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwiZmxleCBpdGVtcy1jZW50ZXIgcC02IHB0LTBcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZEZvb3Rlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEZvb3RlclwiXG5cbmV4cG9ydCB7IENhcmQsIENhcmRIZWFkZXIsIENhcmRGb290ZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkQ29udGVudCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkNhcmQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJkaXYiLCJkaXNwbGF5TmFtZSIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJoMyIsIkNhcmREZXNjcmlwdGlvbiIsInAiLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/query-provider.tsx */ \"(ssr)/./src/components/providers/query-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlJTVDJTVDY2hhdGRvYy12MSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDY29kZSU1QyU1Q2NoYXRkb2MtdjElNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDY29kZSU1QyU1Q2NoYXRkb2MtdjElNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDcXVlcnktcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUXVlcnlQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc01BQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJRdWVyeVByb3ZpZGVyXCJdICovIFwiRDpcXFxcY29kZVxcXFxjaGF0ZG9jLXYxXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxxdWVyeS1wcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cchat%5C%5Cchat-interface.tsx%22%2C%22ids%22%3A%5B%22ChatInterface%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Cdocument-upload.tsx%22%2C%22ids%22%3A%5B%22DocumentUpload%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Curl-ingestion.tsx%22%2C%22ids%22%3A%5B%22UrlIngestion%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cchat%5C%5Cchat-interface.tsx%22%2C%22ids%22%3A%5B%22ChatInterface%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Cdocument-upload.tsx%22%2C%22ids%22%3A%5B%22DocumentUpload%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Curl-ingestion.tsx%22%2C%22ids%22%3A%5B%22UrlIngestion%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/chat/chat-interface.tsx */ \"(ssr)/./src/components/chat/chat-interface.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/documents/document-upload.tsx */ \"(ssr)/./src/components/documents/document-upload.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/documents/url-ingestion.tsx */ \"(ssr)/./src/components/documents/url-ingestion.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlJTVDJTVDY2hhdGRvYy12MSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjaGF0JTVDJTVDY2hhdC1pbnRlcmZhY2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQ2hhdEludGVyZmFjZSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDY29kZSU1QyU1Q2NoYXRkb2MtdjElNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDZG9jdW1lbnRzJTVDJTVDZG9jdW1lbnQtdXBsb2FkLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkRvY3VtZW50VXBsb2FkJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlJTVDJTVDY2hhdGRvYy12MSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNkb2N1bWVudHMlNUMlNUN1cmwtaW5nZXN0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlVybEluZ2VzdGlvbiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNExBQXVJO0FBQ3ZJO0FBQ0Esd01BQThJO0FBQzlJO0FBQ0Esb01BQTBJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDaGF0SW50ZXJmYWNlXCJdICovIFwiRDpcXFxcY29kZVxcXFxjaGF0ZG9jLXYxXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGNoYXRcXFxcY2hhdC1pbnRlcmZhY2UudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJEb2N1bWVudFVwbG9hZFwiXSAqLyBcIkQ6XFxcXGNvZGVcXFxcY2hhdGRvYy12MVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxkb2N1bWVudHNcXFxcZG9jdW1lbnQtdXBsb2FkLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVXJsSW5nZXN0aW9uXCJdICovIFwiRDpcXFxcY29kZVxcXFxjaGF0ZG9jLXYxXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGRvY3VtZW50c1xcXFx1cmwtaW5nZXN0aW9uLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cchat%5C%5Cchat-interface.tsx%22%2C%22ids%22%3A%5B%22ChatInterface%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Cdocument-upload.tsx%22%2C%22ids%22%3A%5B%22DocumentUpload%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode%5C%5Cchatdoc-v1%5C%5Csrc%5C%5Ccomponents%5C%5Cdocuments%5C%5Curl-ingestion.tsx%22%2C%22ids%22%3A%5B%22UrlIngestion%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_animated_chat_interface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/animated-chat-interface */ \"(ssr)/./src/components/ui/animated-chat-interface.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \n\n\nfunction ChatInterface() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSendMessage = async (message)=>{\n        if (!message.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            content: message.trim(),\n            sender: 'user',\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: message.trim(),\n                    history: messages\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get response');\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.response,\n                sender: 'assistant',\n                sources: data.sources,\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error('Chat error:', error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: 'Sorry, I encountered an error while processing your request. Please try again.',\n                sender: 'assistant',\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_chat_interface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        messages: messages,\n        onSendMessage: handleSendMessage,\n        isTyping: isLoading,\n        isConnected: true,\n        isLoading: false,\n        placeholder: \"Ask a question about your documents...\",\n        maxHeight: \"100%\"\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/chat-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/documents/document-upload.tsx":
/*!******************************************************!*\
  !*** ./src/components/documents/document-upload.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentUpload: () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_enhanced_file_upload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/enhanced-file-upload */ \"(ssr)/./src/components/ui/enhanced-file-upload.tsx\");\n/* __next_internal_client_entry_do_not_use__ DocumentUpload auto */ \n\n\nfunction DocumentUpload() {\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch document status on component mount and periodically\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentUpload.useEffect\": ()=>{\n            fetchDocumentStatus();\n            const interval = setInterval(fetchDocumentStatus, 5000); // Check every 5 seconds\n            return ({\n                \"DocumentUpload.useEffect\": ()=>clearInterval(interval)\n            })[\"DocumentUpload.useEffect\"];\n        }\n    }[\"DocumentUpload.useEffect\"], []);\n    const fetchDocumentStatus = async ()=>{\n        try {\n            const response = await fetch('/api/documents/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUploadedFiles(data.documents.map((doc)=>({\n                        id: doc.id,\n                        name: doc.originalName || doc.filename,\n                        size: doc.size,\n                        type: '',\n                        status: doc.status,\n                        errorMessage: doc.errorMessage\n                    })));\n            }\n        } catch (error) {\n            console.error('Failed to fetch document status:', error);\n        }\n    };\n    const handleUpload = async (files)=>{\n        try {\n            for (const file of files){\n                // Create FormData for upload\n                const formData = new FormData();\n                formData.append('file', file);\n                // Upload file to API\n                const response = await fetch('/api/documents/upload', {\n                    method: 'POST',\n                    body: formData\n                });\n                if (!response.ok) {\n                    throw new Error(`Failed to upload ${file.name}`);\n                }\n                const result = await response.json();\n                console.log(`Successfully uploaded: ${file.name}`);\n            }\n            // Refresh document status after upload\n            await fetchDocumentStatus();\n        } catch (error) {\n            console.error('Upload error:', error);\n            throw error; // Let the enhanced file upload component handle the error\n        }\n    };\n    const handleRemoveFile = async (fileId)=>{\n        try {\n            const response = await fetch(`/api/documents/${fileId}`, {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                setUploadedFiles((prev)=>prev.filter((file)=>file.id !== fileId));\n            } else {\n                alert('Failed to remove file');\n            }\n        } catch (error) {\n            console.error('Remove file error:', error);\n            alert('Failed to remove file');\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'indexed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckCircle, {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 16\n                }, this);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Clock, {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertCircle, {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Clock, {\n                    className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'indexed':\n                return 'Ready';\n            case 'processing':\n                return 'Processing...';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Uploading...';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_enhanced_file_upload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        maxFileSize: 10 * 1024 * 1024,\n        maxFiles: 10,\n        acceptedTypes: [\n            '.txt',\n            '.md'\n        ],\n        onUpload: handleUpload,\n        onFileRemove: handleRemoveFile\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/documents/document-upload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/documents/url-ingestion.tsx":
/*!****************************************************!*\
  !*** ./src/components/documents/url-ingestion.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UrlIngestion: () => (/* binding */ UrlIngestion)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_animated_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/animated-form */ \"(ssr)/./src/components/ui/animated-form.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Key,Lock,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Key,Lock,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Key,Lock,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Key,Lock,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Key,Lock,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Key,Lock,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Key,Lock,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ UrlIngestion auto */ \n\n\n\n\n\n\nfunction UrlIngestion({ onSuccess }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        url: \"\",\n        authType: \"none\",\n        username: \"\",\n        password: \"\",\n        customHeaders: \"\"\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openSections, setOpenSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        url: true,\n        auth: false,\n        headers: false\n    });\n    const [validations, setValidations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Validation functions\n    const validateUrl = (url)=>{\n        if (!url) return {\n            isValid: false,\n            message: \"URL is required\"\n        };\n        try {\n            new URL(url);\n            return {\n                isValid: true,\n                message: \"\"\n            };\n        } catch  {\n            return {\n                isValid: false,\n                message: \"Please enter a valid URL\"\n            };\n        }\n    };\n    const validateAuth = ()=>{\n        if (formData.authType === \"none\") return {\n            isValid: true,\n            message: \"\"\n        };\n        if (formData.authType === \"basic\") {\n            if (!formData.username || !formData.password) {\n                return {\n                    isValid: false,\n                    message: \"Username and password are required\"\n                };\n            }\n        }\n        return {\n            isValid: true,\n            message: \"\"\n        };\n    };\n    // Update validations when form data changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"UrlIngestion.useEffect\": ()=>{\n            setValidations({\n                url: validateUrl(formData.url),\n                auth: validateAuth()\n            });\n        }\n    }[\"UrlIngestion.useEffect\"], [\n        formData\n    ]);\n    const toggleSection = (section)=>{\n        setOpenSections((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.url.trim() || isLoading) return;\n        setIsLoading(true);\n        setResult(null);\n        try {\n            // Parse custom headers if provided\n            let parsedHeaders = {};\n            if (formData.customHeaders.trim()) {\n                try {\n                    const lines = formData.customHeaders.split('\\n').filter((line)=>line.trim());\n                    for (const line of lines){\n                        const [key, ...valueParts] = line.split(':');\n                        if (key && valueParts.length > 0) {\n                            parsedHeaders[key.trim()] = valueParts.join(':').trim();\n                        }\n                    }\n                } catch (headerError) {\n                    setResult({\n                        type: 'error',\n                        message: 'Invalid custom headers format',\n                        details: 'Please use format: \"Header-Name: Header-Value\" (one per line)'\n                    });\n                    setIsLoading(false);\n                    return;\n                }\n            }\n            const response = await fetch('/api/documents/ingest-url', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    url: formData.url.trim(),\n                    username: formData.username.trim() || undefined,\n                    password: formData.password.trim() || undefined,\n                    customHeaders: Object.keys(parsedHeaders).length > 0 ? parsedHeaders : undefined\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                setResult({\n                    type: 'success',\n                    message: 'Document successfully ingested from URL!',\n                    details: `Filename: ${data.filename}`\n                });\n                // Clear form\n                setFormData({\n                    url: \"\",\n                    authType: \"none\",\n                    username: \"\",\n                    password: \"\",\n                    customHeaders: \"\"\n                });\n                setOpenSections({\n                    url: true,\n                    auth: false,\n                    headers: false\n                });\n                // Call success callback\n                if (onSuccess) {\n                    onSuccess({\n                        documentId: data.documentId,\n                        filename: data.filename,\n                        contentPreview: data.contentPreview\n                    });\n                }\n            } else {\n                setResult({\n                    type: 'error',\n                    message: 'Failed to ingest document from URL',\n                    details: data.error || 'Unknown error occurred'\n                });\n            }\n        } catch (error) {\n            console.error('URL ingestion error:', error);\n            setResult({\n                type: 'error',\n                message: 'Network error occurred',\n                details: 'Please check your internet connection and try again'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const isFormValid = validations.url?.isValid && validations.auth?.isValid;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ingest from URL\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"Fetch and process documents from web URLs\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_form__WEBPACK_IMPORTED_MODULE_4__.CollapsibleSection, {\n                                title: \"URL Configuration\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 19\n                                }, void 0),\n                                isOpen: openSections.url,\n                                onToggle: ()=>toggleSection(\"url\"),\n                                badge: \"Required\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_form__WEBPACK_IMPORTED_MODULE_4__.AnimatedInput, {\n                                    label: \"Source URL\",\n                                    type: \"url\",\n                                    value: formData.url,\n                                    onChange: (value)=>updateFormData(\"url\", value),\n                                    placeholder: \"https://example.com/article\",\n                                    validation: validations.url,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_form__WEBPACK_IMPORTED_MODULE_4__.CollapsibleSection, {\n                                title: \"Authentication\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 19\n                                }, void 0),\n                                isOpen: openSections.auth,\n                                onToggle: ()=>toggleSection(\"auth\"),\n                                badge: formData.authType !== \"none\" ? formData.authType.toUpperCase() : \"None\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"Authentication Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.authType,\n                                                    onChange: (e)=>updateFormData(\"authType\", e.target.value),\n                                                    className: \"w-full h-12 px-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"none\",\n                                                            children: \"No Authentication\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"basic\",\n                                                            children: \"Basic Auth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.authType === \"basic\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_form__WEBPACK_IMPORTED_MODULE_4__.AnimatedInput, {\n                                                    label: \"Username\",\n                                                    value: formData.username,\n                                                    onChange: (value)=>updateFormData(\"username\", value),\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_form__WEBPACK_IMPORTED_MODULE_4__.AnimatedInput, {\n                                                    label: \"Password\",\n                                                    type: \"password\",\n                                                    value: formData.password,\n                                                    onChange: (value)=>updateFormData(\"password\", value),\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        validations.auth && !validations.auth.isValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            initial: {\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                opacity: 1\n                                            },\n                                            className: \"flex items-center gap-2 text-sm text-destructive\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 14\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                validations.auth.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                type: \"submit\",\n                                disabled: !isFormValid || isLoading,\n                                className: `w-full h-12 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 ${isFormValid && !isLoading ? \"bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl\" : \"bg-muted text-muted-foreground cursor-not-allowed\"}`,\n                                whileHover: isFormValid && !isLoading ? {\n                                    scale: 1.02\n                                } : {},\n                                whileTap: isFormValid && !isLoading ? {\n                                    scale: 0.98\n                                } : {},\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            animate: {\n                                                rotate: 360\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                repeat: Infinity,\n                                                ease: \"linear\"\n                                            },\n                                            className: \"w-4 h-4 border-2 border-current border-t-transparent rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Processing...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Ingest URL\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                                className: result.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50',\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        result.type === 'success' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-600 mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Key_Lock_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-600 mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                                                className: result.type === 'success' ? 'text-green-800' : 'text-red-800',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: result.message\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    result.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm mt-1 opacity-90\",\n                                                        children: result.details\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-muted rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium mb-2\",\n                                children: \"Supported Content:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-xs text-muted-foreground space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• HTML web pages (articles, blogs, documentation)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• HTTP and HTTPS protocols only\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Basic authentication supported\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Custom headers for API access\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\url-ingestion.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/documents/url-ingestion.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/query-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/query-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"QueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 60 * 1000,\n                        refetchOnWindowFocus: false\n                    }\n                }\n            })\n    }[\"QueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\providers\\\\query-provider.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvcXVlcnktcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXlFO0FBQ3hDO0FBRTFCLFNBQVNHLGNBQWMsRUFBRUMsUUFBUSxFQUFpQztJQUN2RSxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBO2tDQUM1QixJQUNFLElBQUlGLDhEQUFXQSxDQUFDO2dCQUNkTSxnQkFBZ0I7b0JBQ2RDLFNBQVM7d0JBQ1BDLFdBQVcsS0FBSzt3QkFDaEJDLHNCQUFzQjtvQkFDeEI7Z0JBQ0Y7WUFDRjs7SUFHSixxQkFDRSw4REFBQ1Isc0VBQW1CQTtRQUFDUyxRQUFRTDtrQkFDMUJEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXHNyY1xcY29tcG9uZW50c1xccHJvdmlkZXJzXFxxdWVyeS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IFF1ZXJ5Q2xpZW50LCBRdWVyeUNsaWVudFByb3ZpZGVyIH0gZnJvbSBcIkB0YW5zdGFjay9yZWFjdC1xdWVyeVwiO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIFF1ZXJ5UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoXG4gICAgKCkgPT5cbiAgICAgIG5ldyBRdWVyeUNsaWVudCh7XG4gICAgICAgIGRlZmF1bHRPcHRpb25zOiB7XG4gICAgICAgICAgcXVlcmllczoge1xuICAgICAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgICAgICByZWZldGNoT25XaW5kb3dGb2N1czogZmFsc2UsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0pXG4gICk7XG5cbiAgcmV0dXJuIChcbiAgICA8UXVlcnlDbGllbnRQcm92aWRlciBjbGllbnQ9e3F1ZXJ5Q2xpZW50fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwidXNlU3RhdGUiLCJRdWVyeVByb3ZpZGVyIiwiY2hpbGRyZW4iLCJxdWVyeUNsaWVudCIsImRlZmF1bHRPcHRpb25zIiwicXVlcmllcyIsInN0YWxlVGltZSIsInJlZmV0Y2hPbldpbmRvd0ZvY3VzIiwiY2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/query-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/animated-chat-interface.tsx":
/*!*******************************************************!*\
  !*** ./src/components/ui/animated-chat-interface.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnimatedChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Clock,Send,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Clock,Send,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Clock,Send,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Clock,Send,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Clock,Send,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Clock,Send,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Clock,Send,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_markdown_renderer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/markdown-renderer */ \"(ssr)/./src/components/ui/markdown-renderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nconst TypingIndicator = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"flex items-center space-x-2 px-4 py-3 bg-muted rounded-2xl rounded-bl-md max-w-xs\",\n        role: \"status\",\n        \"aria-label\": \"Assistant is typing\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"w-4 h-4 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 48,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-1\",\n                children: [\n                    0,\n                    1,\n                    2\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"w-2 h-2 bg-muted-foreground rounded-full\",\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            opacity: [\n                                0.5,\n                                1,\n                                0.5\n                            ]\n                        },\n                        transition: {\n                            duration: 1.5,\n                            repeat: Infinity,\n                            delay: i * 0.2\n                        }\n                    }, i, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 49,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n        lineNumber: 40,\n        columnNumber: 3\n    }, undefined);\nconst MessageBubble = ({ message, index })=>{\n    const [showTimestamp, setShowTimestamp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isUser = message.sender === 'user';\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        transition: {\n            duration: 0.3,\n            delay: index * 0.1,\n            ease: \"easeOut\"\n        },\n        className: `flex items-end space-x-2 group ${isUser ? 'justify-end' : 'justify-start'}`,\n        onMouseEnter: ()=>setShowTimestamp(true),\n        onMouseLeave: ()=>setShowTimestamp(false),\n        role: \"article\",\n        \"aria-label\": `${message.sender} message`,\n        children: [\n            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mb-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex flex-col ${isUser ? 'items-end' : 'items-start'} max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: `px-4 py-3 rounded-2xl relative ${isUser ? 'bg-primary text-primary-foreground rounded-br-md' : 'bg-muted text-foreground rounded-bl-md'} ${message.isCode ? 'font-mono text-sm' : ''}`,\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            message.isCode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"whitespace-pre-wrap break-words\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined) : isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap break-words\",\n                                children: message.content\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_markdown_renderer__WEBPACK_IMPORTED_MODULE_9__.MarkdownRenderer, {\n                                content: message.content\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined),\n                            message.status === 'sending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full\",\n                                animate: {\n                                    scale: [\n                                        1,\n                                        1.2,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    duration: 1,\n                                    repeat: Infinity\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined),\n                            message.status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"absolute -bottom-1 -right-1 w-3 h-3 text-destructive\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        className: \"mt-2 space-y-1 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs font-medium text-muted-foreground\",\n                                children: \"Sources:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined),\n                            message.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    className: \"bg-background/50 border border-border rounded-lg p-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-medium\",\n                                            children: source.document\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground mt-1 line-clamp-2\",\n                                            children: source.chunk\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs mt-1\",\n                                            children: [\n                                                Math.round(source.relevance * 100),\n                                                \"% match\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                        children: showTimestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            exit: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            className: \"text-xs text-muted-foreground mt-1 px-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-3 h-3 inline mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, undefined),\n                                formatTime(message.timestamp)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center mb-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"w-4 h-4 text-primary-foreground\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\nconst EmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"flex flex-col items-center justify-center h-64 text-center space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-16 h-16 rounded-full bg-muted flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-8 h-8 text-muted-foreground\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 189,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-foreground\",\n                        children: \"Start a conversation\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground max-w-sm\",\n                        children: \"Upload some documents and ask me anything! I'm here to help with your questions.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 192,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n        lineNumber: 184,\n        columnNumber: 3\n    }, undefined);\nconst LoadingState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 p-4\",\n        children: [\n            1,\n            2,\n            3\n        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                            className: \"h-4 w-48\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                            className: \"h-4 w-32\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, undefined)\n            }, i, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n        lineNumber: 202,\n        columnNumber: 3\n    }, undefined);\nfunction AnimatedChatInterface({ messages = [], onSendMessage = ()=>{}, isTyping = false, isConnected = true, isLoading = false, placeholder = \"Ask a question about your documents...\", maxHeight = \"600px\" }) {\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [localMessages, setLocalMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(messages);\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedChatInterface.useEffect\": ()=>{\n            setLocalMessages(messages);\n        }\n    }[\"AnimatedChatInterface.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnimatedChatInterface.useCallback[scrollToBottom]\": ()=>{\n            if (scrollAreaRef.current) {\n                const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');\n                if (scrollContainer) {\n                    scrollContainer.scrollTop = scrollContainer.scrollHeight;\n                }\n            }\n        }\n    }[\"AnimatedChatInterface.useCallback[scrollToBottom]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"AnimatedChatInterface.useEffect\"], [\n        localMessages,\n        isTyping,\n        scrollToBottom\n    ]);\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnimatedChatInterface.useCallback[handleSendMessage]\": ()=>{\n            if (!inputValue.trim() || !isConnected) return;\n            const newMessage = {\n                id: Date.now().toString(),\n                content: inputValue.trim(),\n                sender: 'user',\n                timestamp: new Date(),\n                status: 'sending'\n            };\n            setLocalMessages({\n                \"AnimatedChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                        ...prev,\n                        newMessage\n                    ]\n            }[\"AnimatedChatInterface.useCallback[handleSendMessage]\"]);\n            onSendMessage(inputValue.trim());\n            setInputValue('');\n            setTimeout({\n                \"AnimatedChatInterface.useCallback[handleSendMessage]\": ()=>{\n                    setLocalMessages({\n                        \"AnimatedChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"AnimatedChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === newMessage.id ? {\n                                        ...msg,\n                                        status: 'sent'\n                                    } : msg\n                            }[\"AnimatedChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"AnimatedChatInterface.useCallback[handleSendMessage]\"]);\n                }\n            }[\"AnimatedChatInterface.useCallback[handleSendMessage]\"], 1000);\n        }\n    }[\"AnimatedChatInterface.useCallback[handleSendMessage]\"], [\n        inputValue,\n        isConnected,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnimatedChatInterface.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"AnimatedChatInterface.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedChatInterface.useEffect\": ()=>{\n            if (inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"AnimatedChatInterface.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"flex flex-col w-full h-full bg-background border-border shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-5 h-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-foreground\",\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-3 h-3 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-3 h-3 text-destructive\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-destructive\",\n                                                    children: \"Disconnected\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"secondary\",\n                        className: \"text-xs\",\n                        children: \"RAG Enabled\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                className: \"m-4 mb-0 border-destructive/50 bg-destructive/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                        children: \"Connection lost. Please check your internet connection and try again.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                ref: scrollAreaRef,\n                className: \"flex-1 p-4\",\n                style: {\n                    maxHeight\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingState, {}, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 11\n                }, this) : localMessages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmptyState, {}, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    role: \"log\",\n                    \"aria-label\": \"Chat messages\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                        children: [\n                            localMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBubble, {\n                                    message: message,\n                                    index: index\n                                }, message.id, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, this)),\n                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                ref: inputRef,\n                                value: inputValue,\n                                onChange: (e)=>setInputValue(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: isConnected ? placeholder : \"Reconnecting...\",\n                                disabled: !isConnected || isLoading,\n                                className: \"flex-1\",\n                                \"aria-label\": \"Message input\",\n                                maxLength: 1000\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleSendMessage,\n                                disabled: !inputValue.trim() || !isConnected || isLoading,\n                                size: \"icon\",\n                                \"aria-label\": \"Send message\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Clock_Send_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Press Enter to send, Shift+Enter for new line\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    inputValue.length,\n                                    \"/1000\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-chat-interface.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/animated-chat-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/animated-form.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/animated-form.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedInput: () => (/* binding */ AnimatedInput),\n/* harmony export */   CollapsibleSection: () => (/* binding */ CollapsibleSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AnimatedInput,CollapsibleSection auto */ \n\n\n\n\n\nconst AnimatedInput = ({ label, type = \"text\", value, onChange, placeholder, validation, icon, required = false })=>{\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isPassword = type === \"password\";\n    const inputType = isPassword ? showPassword ? \"text\" : \"password\" : type;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"relative\",\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.label, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute left-3 transition-all duration-200 pointer-events-none\", isFocused || value ? \"top-2 text-xs text-primary\" : \"top-1/2 -translate-y-1/2 text-sm text-muted-foreground\"),\n                        animate: {\n                            fontSize: isFocused || value ? \"0.75rem\" : \"0.875rem\",\n                            y: isFocused || value ? 0 : \"-50%\",\n                            top: isFocused || value ? \"0.5rem\" : \"50%\"\n                        },\n                        children: [\n                            label,\n                            \" \",\n                            required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-destructive\",\n                                children: \"*\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 32\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: inputRef,\n                                type: inputType,\n                                value: value,\n                                onChange: (e)=>onChange(e.target.value),\n                                onFocus: ()=>setIsFocused(true),\n                                onBlur: ()=>setIsFocused(false),\n                                placeholder: isFocused ? placeholder : \"\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full h-12 px-3 pt-6 pb-2 border rounded-lg bg-background transition-all duration-200\", \"focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary\", icon ? \"pl-10\" : \"\", isPassword ? \"pr-10\" : \"\", validation?.isValid === false ? \"border-destructive\" : \"border-border\", \"hover:border-primary/50\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            isPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setShowPassword(!showPassword),\n                                className: \"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground\",\n                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 31\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 54\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: [\n                    validation && !validation.isValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        className: \"flex items-center gap-2 mt-2 text-sm text-destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined),\n                            validation.message\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined),\n                    validation && validation.isValid && value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        className: \"flex items-center gap-2 mt-2 text-sm text-green-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Valid\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\nconst CollapsibleSection = ({ title, icon, isOpen, onToggle, children, badge })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"border border-border rounded-lg overflow-hidden bg-card\",\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                type: \"button\",\n                onClick: onToggle,\n                className: \"w-full p-4 flex items-center justify-between hover:bg-accent transition-colors\",\n                whileHover: {\n                    backgroundColor: \"hsl(var(--accent))\"\n                },\n                whileTap: {\n                    scale: 0.98\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            animate: {\n                                rotate: isOpen ? 90 : 0\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-foreground\",\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-foreground\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-2 py-1 text-xs bg-primary/10 text-primary rounded-full\",\n                            children: badge\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        height: 0,\n                        opacity: 0\n                    },\n                    animate: {\n                        height: \"auto\",\n                        opacity: 1\n                    },\n                    exit: {\n                        height: 0,\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeInOut\"\n                    },\n                    className: \"overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 pt-0 border-t border-border\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\animated-form.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, undefined);\n};\n// Export components for use in other files\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/animated-form.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwwUkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXHNyY1xcY29tcG9uZW50c1xcdWlcXGJ1dHRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFNsb3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNsb3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0gY3ZhKFxuICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3aGl0ZXNwYWNlLW5vd3JhcCByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gcmluZy1vZmZzZXQtYmFja2dyb3VuZCB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OiBcImJnLXByaW1hcnkgdGV4dC1wcmltYXJ5LWZvcmVncm91bmQgaG92ZXI6YmctcHJpbWFyeS85MFwiLFxuICAgICAgICBkZXN0cnVjdGl2ZTpcbiAgICAgICAgICBcImJnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBob3ZlcjpiZy1kZXN0cnVjdGl2ZS85MFwiLFxuICAgICAgICBvdXRsaW5lOlxuICAgICAgICAgIFwiYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIHNlY29uZGFyeTpcbiAgICAgICAgICBcImJnLXNlY29uZGFyeSB0ZXh0LXNlY29uZGFyeS1mb3JlZ3JvdW5kIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxuICAgICAgICBnaG9zdDogXCJob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBsaW5rOiBcInRleHQtcHJpbWFyeSB1bmRlcmxpbmUtb2Zmc2V0LTQgaG92ZXI6dW5kZXJsaW5lXCIsXG4gICAgICB9LFxuICAgICAgc2l6ZToge1xuICAgICAgICBkZWZhdWx0OiBcImgtMTAgcHgtNCBweS0yXCIsXG4gICAgICAgIHNtOiBcImgtOSByb3VuZGVkLW1kIHB4LTNcIixcbiAgICAgICAgbGc6IFwiaC0xMSByb3VuZGVkLW1kIHB4LThcIixcbiAgICAgICAgaWNvbjogXCJoLTEwIHctMTBcIixcbiAgICAgIH0sXG4gICAgfSxcbiAgICBkZWZhdWx0VmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6IFwiZGVmYXVsdFwiLFxuICAgICAgc2l6ZTogXCJkZWZhdWx0XCIsXG4gICAgfSxcbiAgfVxuKVxuXG5leHBvcnQgaW50ZXJmYWNlIEJ1dHRvblByb3BzXG4gIGV4dGVuZHMgUmVhY3QuQnV0dG9uSFRNTEF0dHJpYnV0ZXM8SFRNTEJ1dHRvbkVsZW1lbnQ+LFxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgYnV0dG9uVmFyaWFudHM+IHtcbiAgYXNDaGlsZD86IGJvb2xlYW5cbn1cblxuY29uc3QgQnV0dG9uID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MQnV0dG9uRWxlbWVudCwgQnV0dG9uUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHZhcmlhbnQsIHNpemUsIGFzQ2hpbGQgPSBmYWxzZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgY29uc3QgQ29tcCA9IGFzQ2hpbGQgPyBTbG90IDogXCJidXR0b25cIlxuICAgIHJldHVybiAoXG4gICAgICA8Q29tcFxuICAgICAgICBjbGFzc05hbWU9e2NuKGJ1dHRvblZhcmlhbnRzKHsgdmFyaWFudCwgc2l6ZSwgY2xhc3NOYW1lIH0pKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5CdXR0b24uZGlzcGxheU5hbWUgPSBcIkJ1dHRvblwiXG5cbmV4cG9ydCB7IEJ1dHRvbiwgYnV0dG9uVmFyaWFudHMgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2xvdCIsImN2YSIsImNuIiwiYnV0dG9uVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJsaW5rIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJwcm9wcyIsInJlZiIsIkNvbXAiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/enhanced-file-upload.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/enhanced-file-upload.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Image,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Image,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Image,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Image,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Image,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Image,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Image,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Image,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(ssr)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n\n\n\n\n\n\n\n\nconst EnhancedFileUpload = ({ maxFileSize = 10 * 1024 * 1024, maxFiles = 10, acceptedTypes = [\n    '.txt',\n    '.md'\n], onUpload, onFileRemove, className = '' })=>{\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [globalError, setGlobalError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previewFile, setPreviewFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dropZoneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const getFileIcon = (file)=>{\n        if (file.type.startsWith('image/')) return _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n        if (file.type === 'application/pdf') return _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n        if (file.type.includes('document') || file.type.includes('text')) return _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n        return _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const validateFile = (file)=>{\n        if (file.size > maxFileSize) {\n            return `File size exceeds ${formatFileSize(maxFileSize)}`;\n        }\n        const isValidType = acceptedTypes.some((type)=>{\n            if (type.includes('*')) {\n                return file.type.startsWith(type.replace('*', ''));\n            }\n            return file.type === type || file.name.toLowerCase().endsWith(type);\n        });\n        if (!isValidType) {\n            return 'File type not supported';\n        }\n        return null;\n    };\n    const createFilePreview = (file)=>{\n        return new Promise((resolve)=>{\n            if (file.type.startsWith('image/')) {\n                const reader = new FileReader();\n                reader.onload = (e)=>resolve(e.target?.result);\n                reader.onerror = ()=>resolve(undefined);\n                reader.readAsDataURL(file);\n            } else {\n                resolve(undefined);\n            }\n        });\n    };\n    const addFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedFileUpload.useCallback[addFiles]\": async (newFiles)=>{\n            setGlobalError('');\n            if (files.length + newFiles.length > maxFiles) {\n                setGlobalError(`Maximum ${maxFiles} files allowed`);\n                return;\n            }\n            const validFiles = [];\n            for (const file of newFiles){\n                const error = validateFile(file);\n                const preview = await createFilePreview(file);\n                validFiles.push({\n                    id: Math.random().toString(36).substr(2, 9),\n                    file,\n                    status: error ? 'error' : 'pending',\n                    progress: 0,\n                    error,\n                    preview\n                });\n            }\n            setFiles({\n                \"EnhancedFileUpload.useCallback[addFiles]\": (prev)=>[\n                        ...prev,\n                        ...validFiles\n                    ]\n            }[\"EnhancedFileUpload.useCallback[addFiles]\"]);\n            // Auto-upload valid files\n            const filesToUpload = validFiles.filter({\n                \"EnhancedFileUpload.useCallback[addFiles].filesToUpload\": (f)=>f.status === 'pending'\n            }[\"EnhancedFileUpload.useCallback[addFiles].filesToUpload\"]);\n            if (filesToUpload.length > 0 && onUpload) {\n                uploadFiles(filesToUpload);\n            }\n        }\n    }[\"EnhancedFileUpload.useCallback[addFiles]\"], [\n        files.length,\n        maxFiles,\n        onUpload\n    ]);\n    const uploadFiles = async (filesToUpload)=>{\n        for (const fileItem of filesToUpload){\n            setFiles((prev)=>prev.map((f)=>f.id === fileItem.id ? {\n                        ...f,\n                        status: 'uploading'\n                    } : f));\n            try {\n                // Simulate upload progress\n                for(let progress = 0; progress <= 100; progress += 10){\n                    await new Promise((resolve)=>setTimeout(resolve, 100));\n                    setFiles((prev)=>prev.map((f)=>f.id === fileItem.id ? {\n                                ...f,\n                                progress\n                            } : f));\n                }\n                if (onUpload) {\n                    await onUpload([\n                        fileItem.file\n                    ]);\n                }\n                setFiles((prev)=>prev.map((f)=>f.id === fileItem.id ? {\n                            ...f,\n                            status: 'success',\n                            progress: 100\n                        } : f));\n            } catch (error) {\n                setFiles((prev)=>prev.map((f)=>f.id === fileItem.id ? {\n                            ...f,\n                            status: 'error',\n                            error: 'Upload failed. Please try again.'\n                        } : f));\n            }\n        }\n    };\n    const removeFile = (fileId)=>{\n        setFiles((prev)=>prev.filter((f)=>f.id !== fileId));\n        onFileRemove?.(fileId);\n    };\n    const retryUpload = (fileId)=>{\n        const fileToRetry = files.find((f)=>f.id === fileId);\n        if (fileToRetry && onUpload) {\n            setFiles((prev)=>prev.map((f)=>f.id === fileId ? {\n                        ...f,\n                        status: 'pending',\n                        error: undefined,\n                        progress: 0\n                    } : f));\n            uploadFiles([\n                fileToRetry\n            ]);\n        }\n    };\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedFileUpload.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragOver(true);\n        }\n    }[\"EnhancedFileUpload.useCallback[handleDragOver]\"], []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedFileUpload.useCallback[handleDragLeave]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (!dropZoneRef.current?.contains(e.relatedTarget)) {\n                setIsDragOver(false);\n            }\n        }\n    }[\"EnhancedFileUpload.useCallback[handleDragLeave]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedFileUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragOver(false);\n            const droppedFiles = Array.from(e.dataTransfer.files);\n            addFiles(droppedFiles);\n        }\n    }[\"EnhancedFileUpload.useCallback[handleDrop]\"], [\n        addFiles\n    ]);\n    const handleFileSelect = (e)=>{\n        const selectedFiles = Array.from(e.target.files || []);\n        addFiles(selectedFiles);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' || e.key === ' ') {\n            e.preventDefault();\n            fileInputRef.current?.click();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedFileUpload.useEffect\": ()=>{\n            const handleGlobalDragOver = {\n                \"EnhancedFileUpload.useEffect.handleGlobalDragOver\": (e)=>{\n                    e.preventDefault();\n                }\n            }[\"EnhancedFileUpload.useEffect.handleGlobalDragOver\"];\n            const handleGlobalDrop = {\n                \"EnhancedFileUpload.useEffect.handleGlobalDrop\": (e)=>{\n                    e.preventDefault();\n                }\n            }[\"EnhancedFileUpload.useEffect.handleGlobalDrop\"];\n            document.addEventListener('dragover', handleGlobalDragOver);\n            document.addEventListener('drop', handleGlobalDrop);\n            return ({\n                \"EnhancedFileUpload.useEffect\": ()=>{\n                    document.removeEventListener('dragover', handleGlobalDragOver);\n                    document.removeEventListener('drop', handleGlobalDrop);\n                }\n            })[\"EnhancedFileUpload.useEffect\"];\n        }\n    }[\"EnhancedFileUpload.useEffect\"], []);\n    const successCount = files.filter((f)=>f.status === 'success').length;\n    const errorCount = files.filter((f)=>f.status === 'error').length;\n    const uploadingCount = files.filter((f)=>f.status === 'uploading').length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `w-full space-y-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                ref: dropZoneRef,\n                className: `\n          relative border-2 border-dashed transition-all duration-200 cursor-pointer\n          ${isDragOver ? 'border-primary bg-primary/5 scale-[1.02]' : 'border-border hover:border-primary/50 hover:bg-muted/50'}\n        `,\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>fileInputRef.current?.click(),\n                onKeyDown: handleKeyDown,\n                tabIndex: 0,\n                role: \"button\",\n                \"aria-label\": \"Upload files\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `\n            mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-colors\n            ${isDragOver ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}\n          `,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: isDragOver ? 'Drop files here' : 'Upload your documents'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-4\",\n                                children: \"Drag and drop files here, or click to browse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2 text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Max file size: \",\n                                            formatFileSize(maxFileSize)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Max files: \",\n                                            maxFiles\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                type: \"button\",\n                                children: \"Choose Files\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        className: \"hidden\",\n                        accept: acceptedTypes.join(','),\n                        onChange: handleFileSelect,\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, undefined),\n            globalError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                        children: globalError\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, undefined),\n            files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-4 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"secondary\",\n                        children: [\n                            \"Total: \",\n                            files.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, undefined),\n                    successCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"default\",\n                        className: \"bg-green-100 text-green-800 hover:bg-green-100\",\n                        children: [\n                            \"Success: \",\n                            successCount\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 13\n                    }, undefined),\n                    uploadingCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"default\",\n                        className: \"bg-blue-100 text-blue-800 hover:bg-blue-100\",\n                        children: [\n                            \"Uploading: \",\n                            uploadingCount\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 13\n                    }, undefined),\n                    errorCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"destructive\",\n                        children: [\n                            \"Errors: \",\n                            errorCount\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, undefined),\n            files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: files.map((fileItem)=>{\n                    const IconComponent = getFileIcon(fileItem.file);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: fileItem.preview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-lg overflow-hidden bg-muted\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: fileItem.preview,\n                                            alt: fileItem.file.name,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 23\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-lg bg-muted flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"w-6 h-6 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 23\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium truncate\",\n                                                    children: fileItem.file.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                fileItem.status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-600 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                fileItem.status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-red-600 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                fileItem.status === 'uploading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 text-blue-600 animate-spin flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-2\",\n                                            children: formatFileSize(fileItem.file.size)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        fileItem.status === 'uploading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                    value: fileItem.progress,\n                                                    className: \"h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        fileItem.progress,\n                                                        \"% uploaded\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        fileItem.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: fileItem.error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 flex-shrink-0\",\n                                    children: [\n                                        fileItem.status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                retryUpload(fileItem.id);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                removeFile(fileItem.id);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 17\n                        }, undefined)\n                    }, fileItem.id, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 15\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, undefined),\n            files.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Image_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No files uploaded yet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n                lineNumber: 419,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\enhanced-file-upload.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedFileUpload);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/enhanced-file-upload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXHNyY1xcY29tcG9uZW50c1xcdWlcXGlucHV0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/markdown-renderer.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/markdown-renderer.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownRenderer: () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-highlight */ \"(ssr)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarkdownRenderer auto */ \n\n\n\n\nfunction MarkdownRenderer({ content, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"prose prose-sm max-w-none\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_2__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n            ],\n            rehypePlugins: [\n                rehype_highlight__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n            ],\n            components: {\n                // Headings\n                h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-foreground mb-3 mt-4 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 13\n                    }, void 0),\n                h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-foreground mb-2 mt-3 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 13\n                    }, void 0),\n                h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-semibold text-foreground mb-2 mt-3 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, void 0),\n                h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-foreground mb-1 mt-2 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 13\n                    }, void 0),\n                h5: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-foreground mb-1 mt-2 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0),\n                h6: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        className: \"text-xs font-medium text-foreground mb-1 mt-2 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 13\n                    }, void 0),\n                // Paragraphs\n                p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-foreground mb-3 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 13\n                    }, void 0),\n                // Lists\n                ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-3 space-y-1 text-sm text-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, void 0),\n                ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-3 space-y-1 text-sm text-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, void 0),\n                li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm text-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, void 0),\n                // Links\n                a: ({ href, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"text-primary hover:text-primary/80 underline underline-offset-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, void 0),\n                // Code\n                code: ({ inline, className, children })=>{\n                    if (inline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono text-foreground\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"block bg-muted p-3 rounded-md text-xs font-mono overflow-x-auto\", className),\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Pre (code blocks)\n                pre: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-muted p-3 rounded-md mb-3 overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, void 0),\n                // Blockquotes\n                blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted-foreground/20 pl-4 mb-3 italic text-muted-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, void 0),\n                // Tables\n                table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border-collapse border border-border\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, void 0),\n                thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-muted\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 13\n                    }, void 0),\n                tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 13\n                    }, void 0),\n                tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: \"border-b border-border\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 13\n                    }, void 0),\n                th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"border border-border px-3 py-2 text-left text-sm font-semibold text-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, void 0),\n                td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"border border-border px-3 py-2 text-sm text-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, void 0),\n                // Horizontal rule\n                hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-t border-border my-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 13\n                    }, void 0),\n                // Strong and emphasis\n                strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, void 0),\n                em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, void 0),\n                // Images\n                img: ({ src, alt })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: src,\n                        alt: alt,\n                        className: \"max-w-full h-auto rounded-md mb-3\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, void 0)\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\markdown-renderer.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/markdown-renderer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/progress.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/progress.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-progress */ \"(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Progress auto */ \n\n\n\nfunction Progress({ className, value, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"progress\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            \"data-slot\": \"progress-indicator\",\n            className: \"bg-primary h-full w-full flex-1 transition-all\",\n            style: {\n                transform: `translateX(-${100 - (value || 0)}%)`\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\progress.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\progress.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9wcm9ncmVzcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDK0I7QUFFN0I7QUFFaEMsU0FBU0csU0FBUyxFQUNoQkMsU0FBUyxFQUNUQyxLQUFLLEVBQ0wsR0FBR0MsT0FDaUQ7SUFDcEQscUJBQ0UsOERBQUNMLDBEQUFzQjtRQUNyQk8sYUFBVTtRQUNWSixXQUFXRiw4Q0FBRUEsQ0FDWCxrRUFDQUU7UUFFRCxHQUFHRSxLQUFLO2tCQUVULDRFQUFDTCwrREFBMkI7WUFDMUJPLGFBQVU7WUFDVkosV0FBVTtZQUNWTSxPQUFPO2dCQUFFQyxXQUFXLENBQUMsWUFBWSxFQUFFLE1BQU9OLENBQUFBLFNBQVMsR0FBRyxFQUFFLENBQUM7WUFBQzs7Ozs7Ozs7Ozs7QUFJbEU7QUFFbUIiLCJzb3VyY2VzIjpbIkQ6XFxjb2RlXFxjaGF0ZG9jLXYxXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxwcm9ncmVzcy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIFByb2dyZXNzUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJvZ3Jlc3NcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmZ1bmN0aW9uIFByb2dyZXNzKHtcbiAgY2xhc3NOYW1lLFxuICB2YWx1ZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBQcm9ncmVzc1ByaW1pdGl2ZS5Sb290Pikge1xuICByZXR1cm4gKFxuICAgIDxQcm9ncmVzc1ByaW1pdGl2ZS5Sb290XG4gICAgICBkYXRhLXNsb3Q9XCJwcm9ncmVzc1wiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImJnLXByaW1hcnkvMjAgcmVsYXRpdmUgaC0yIHctZnVsbCBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC1mdWxsXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICA+XG4gICAgICA8UHJvZ3Jlc3NQcmltaXRpdmUuSW5kaWNhdG9yXG4gICAgICAgIGRhdGEtc2xvdD1cInByb2dyZXNzLWluZGljYXRvclwiXG4gICAgICAgIGNsYXNzTmFtZT1cImJnLXByaW1hcnkgaC1mdWxsIHctZnVsbCBmbGV4LTEgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICBzdHlsZT17eyB0cmFuc2Zvcm06IGB0cmFuc2xhdGVYKC0kezEwMCAtICh2YWx1ZSB8fCAwKX0lKWAgfX1cbiAgICAgIC8+XG4gICAgPC9Qcm9ncmVzc1ByaW1pdGl2ZS5Sb290PlxuICApXG59XG5cbmV4cG9ydCB7IFByb2dyZXNzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlByb2dyZXNzUHJpbWl0aXZlIiwiY24iLCJQcm9ncmVzcyIsImNsYXNzTmFtZSIsInZhbHVlIiwicHJvcHMiLCJSb290IiwiZGF0YS1zbG90IiwiSW5kaWNhdG9yIiwic3R5bGUiLCJ0cmFuc2Zvcm0iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/progress.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/scroll-area.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/scroll-area.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nfunction ScrollArea({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"scroll-area\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                \"data-slot\": \"scroll-area-viewport\",\n                className: \"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction ScrollBar({ className, orientation = \"vertical\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        \"data-slot\": \"scroll-area-scrollbar\",\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none p-px transition-colors select-none\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            \"data-slot\": \"scroll-area-thumb\",\n            className: \"bg-border relative flex-1 rounded-full\"\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"skeleton\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"bg-accent animate-pulse rounded-md\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFFaEMsU0FBU0MsU0FBUyxFQUFFQyxTQUFTLEVBQUUsR0FBR0MsT0FBb0M7SUFDcEUscUJBQ0UsOERBQUNDO1FBQ0NDLGFBQVU7UUFDVkgsV0FBV0YsOENBQUVBLENBQUMsc0NBQXNDRTtRQUNuRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVtQiIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXHNyY1xcY29tcG9uZW50c1xcdWlcXHNrZWxldG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmZ1bmN0aW9uIFNrZWxldG9uKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtc2xvdD1cInNrZWxldG9uXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXCJiZy1hY2NlbnQgYW5pbWF0ZS1wdWxzZSByb3VuZGVkLW1kXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBTa2VsZXRvbiB9XG4iXSwibmFtZXMiOlsiY24iLCJTa2VsZXRvbiIsImNsYXNzTmFtZSIsInByb3BzIiwiZGl2IiwiZGF0YS1zbG90Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/highlight.js","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/@radix-ui","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/tailwind-merge","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/lowlight","vendor-chunks/clsx","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/unist-util-find-after","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-gfm","vendor-chunks/rehype-highlight","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-text","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/hast-util-is-element","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/class-variance-authority","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();