Object.defineProperty(exports, '__esModule', { value: true });

var csvParse = require('csv-parse');
var schema = require('@llamaindex/core/schema');

/**
 * CSV parser
 */ class CSVReader extends schema.FileReader {
    /**
   * Constructs a new instance of the class.
   * @param concatRows - Whether to concatenate all rows into one document.If set to False, a Document will be created for each row. `True` by default.
   * @param colJoiner - Separator to use for joining cols per row. Set to ", " by default.
   * @param rowJoiner - Separator to use for joining each row.Only used when `concat_rows=True`.Set to "\n" by default.
   */ constructor(concatRows = true, colJoiner = ", ", rowJoiner = "\n", config){
        super();
        this.concatRows = concatRows;
        this.colJoiner = colJoiner;
        this.rowJoiner = rowJoiner;
        this.config = config ?? {};
    }
    /**
   * Loads data from csv files
   * @param fileContent - The content of the file.
   * @returns An array of Documents.
   */ async loadDataAsContent(fileContent) {
        const decoder = new TextDecoder("utf-8");
        const fileContentString = decoder.decode(fileContent);
        const parser = CSVReader.parse(fileContentString, this.config);
        const textList = [];
        for await (const record of parser){
            textList.push(record.map((v)=>`${v}`).join(this.colJoiner));
        }
        if (this.concatRows) {
            return [
                new schema.Document({
                    text: textList.join(this.rowJoiner)
                })
            ];
        } else {
            return textList.map((text)=>new schema.Document({
                    text
                }));
        }
    }
}

CSVReader.parse = csvParse.parse;

exports.CSVReader = CSVReader;
