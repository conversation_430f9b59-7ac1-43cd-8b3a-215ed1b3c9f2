"use client";

import * as React from "react";
import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Globe, 
  Lock, 
  User, 
  Key, 
  ChevronDown, 
  CheckCircle, 
  AlertCircle, 
  Eye, 
  EyeOff,
  Send
} from "lucide-react";
import { cn } from "@/lib/utils";

// Form validation types
interface ValidationState {
  isValid: boolean;
  message: string;
}

interface FormData {
  url: string;
  authType: "none" | "basic" | "bearer" | "apikey";
  username: string;
  password: string;
  token: string;
  apiKey: string;
  headers: { key: string; value: string }[];
}

// Animated Input Component
interface AnimatedInputProps {
  label: string;
  type?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  validation?: ValidationState;
  icon?: React.ReactNode;
  required?: boolean;
}

const AnimatedInput: React.FC<AnimatedInputProps> = ({
  label,
  type = "text",
  value,
  onChange,
  placeholder,
  validation,
  icon,
  required = false
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const isPassword = type === "password";
  const inputType = isPassword ? (showPassword ? "text" : "password") : type;

  return (
    <motion.div
      className="relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="relative">
        <motion.label
          className={cn(
            "absolute left-3 transition-all duration-200 pointer-events-none",
            isFocused || value
              ? "top-2 text-xs text-primary"
              : "top-1/2 -translate-y-1/2 text-sm text-muted-foreground"
          )}
          animate={{
            fontSize: isFocused || value ? "0.75rem" : "0.875rem",
            y: isFocused || value ? 0 : "-50%",
            top: isFocused || value ? "0.5rem" : "50%"
          }}
        >
          {label} {required && <span className="text-destructive">*</span>}
        </motion.label>
        
        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {icon}
            </div>
          )}
          
          <input
            ref={inputRef}
            type={inputType}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={isFocused ? placeholder : ""}
            className={cn(
              "w-full h-12 px-3 pt-6 pb-2 border rounded-lg bg-background transition-all duration-200",
              "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary",
              icon ? "pl-10" : "",
              isPassword ? "pr-10" : "",
              validation?.isValid === false ? "border-destructive" : "border-border",
              "hover:border-primary/50"
            )}
          />
          
          {isPassword && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          )}
        </div>
      </div>
      
      <AnimatePresence>
        {validation && !validation.isValid && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="flex items-center gap-2 mt-2 text-sm text-destructive"
          >
            <AlertCircle size={14} />
            {validation.message}
          </motion.div>
        )}
        {validation && validation.isValid && value && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="flex items-center gap-2 mt-2 text-sm text-green-600"
          >
            <CheckCircle size={14} />
            Valid
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// Collapsible Section Component
interface CollapsibleSectionProps {
  title: string;
  icon: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  badge?: string;
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  icon,
  isOpen,
  onToggle,
  children,
  badge
}) => {
  return (
    <motion.div
      className="border border-border rounded-lg overflow-hidden bg-card"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.button
        type="button"
        onClick={onToggle}
        className="w-full p-4 flex items-center justify-between hover:bg-accent transition-colors"
        whileHover={{ backgroundColor: "hsl(var(--accent))" }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-center gap-3">
          <motion.div
            animate={{ rotate: isOpen ? 90 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown size={20} />
          </motion.div>
          <div className="text-foreground">{icon}</div>
          <span className="font-medium text-foreground">{title}</span>
          {badge && (
            <span className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-full">
              {badge}
            </span>
          )}
        </div>
      </motion.button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0 border-t border-border">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// Export components for use in other files
export { AnimatedInput, CollapsibleSection };
export type { ValidationState, FormData, AnimatedInputProps, CollapsibleSectionProps };
