import { FileReader, Document } from '@llamaindex/core/schema';

/**
 * Read the text of a PDF
 */ class PDFReader extends FileReader {
    async loadDataAsContent(content) {
        // XXX: create a new Uint8Array to prevent "Please provide binary data as `Uint8Array`, rather than `<PERSON>uffer`." error if a <PERSON>uff<PERSON> passed
        if (content instanceof Buffer) {
            content = new Uint8Array(content);
        }
        const { totalPages, text } = await readPDF(content);
        return text.map((text, page)=>{
            const metadata = {
                page_number: page + 1,
                total_pages: totalPages
            };
            return new Document({
                text,
                metadata
            });
        });
    }
}
async function readPDF(data) {
    const { extractText } = await import('unpdf');
    return await extractText(data);
}

export { PDFReader };
