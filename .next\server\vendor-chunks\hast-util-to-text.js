"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-text";
exports.ids = ["vendor-chunks/hast-util-to-text"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-text/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-to-text/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toText: () => (/* binding */ toText)\n/* harmony export */ });\n/* harmony import */ var unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-find-after */ \"(ssr)/./node_modules/unist-util-find-after/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-is-element/lib/index.js\");\n/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast-util-is-element').TestFunction} TestFunction\n */\n\n/**\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n *\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n *\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n *\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n *\n * @typedef {BreakNumber | BreakValue | undefined} BreakBefore\n *   Any value for a break before.\n *\n * @typedef {BreakForce | BreakNumber | BreakValue | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use (default: `'normal'`).\n */\n\n\n\n\nconst searchLineFeeds = /\\n/g\nconst searchTabOrSpaces = /[\\t ]+/g\n\nconst br = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('br')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(isCell)\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst row = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\n\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  // List from: <https://html.spec.whatwg.org/multipage/rendering.html#hidden-elements>\n  'datalist',\n  'head',\n  'noembed',\n  'noframes',\n  'noscript', // Act as if we support scripting.\n  'rp',\n  'script',\n  'style',\n  'template',\n  'title',\n  // Hidden attribute.\n  hidden,\n  // From: <https://html.spec.whatwg.org/multipage/rendering.html#flow-content-3>\n  closedDialog\n])\n\n// See: <https://html.spec.whatwg.org/multipage/rendering.html#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'address', // Flow content\n  'article', // Sections and headings\n  'aside', // Sections and headings\n  'blockquote', // Flow content\n  'body', // Page\n  'caption', // `table-caption`\n  'center', // Flow content (legacy)\n  'dd', // Lists\n  'dialog', // Flow content\n  'dir', // Lists (legacy)\n  'dl', // Lists\n  'dt', // Lists\n  'div', // Flow content\n  'figure', // Flow content\n  'figcaption', // Flow content\n  'footer', // Flow content\n  'form,', // Flow content\n  'h1', // Sections and headings\n  'h2', // Sections and headings\n  'h3', // Sections and headings\n  'h4', // Sections and headings\n  'h5', // Sections and headings\n  'h6', // Sections and headings\n  'header', // Flow content\n  'hgroup', // Sections and headings\n  'hr', // Flow content\n  'html', // Page\n  'legend', // Flow content\n  'li', // Lists (as `display: list-item`)\n  'listing', // Flow content (legacy)\n  'main', // Flow content\n  'menu', // Lists\n  'nav', // Sections and headings\n  'ol', // Lists\n  'p', // Flow content\n  'plaintext', // Flow content (legacy)\n  'pre', // Flow content\n  'section', // Sections and headings\n  'ul', // Lists\n  'xmp' // Flow content (legacy)\n])\n\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Nodes} tree\n *   Tree to turn into text.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */\nfunction toText(tree, options) {\n  const options_ = options || {}\n  const children = 'children' in tree ? tree.children : []\n  const block = blockOrCaption(tree)\n  const whitespace = inferWhitespace(tree, {\n    whitespace: options_.whitespace || 'normal',\n    breakBefore: false,\n    breakAfter: false\n  })\n\n  /** @type {Array<BreakNumber | string>} */\n  const results = []\n\n  // Treat `text` and `comment` as having normal white-space.\n  // This deviates from the spec as in the DOM the node’s `.data` has to be\n  // returned.\n  // If you want that behavior use `hast-util-to-string`.\n  // All other nodes are later handled as if they are `element`s (so the\n  // algorithm also works on a `root`).\n  // Nodes without children are treated as a void element, so `doctype` is thus\n  // ignored.\n  if (tree.type === 'text' || tree.type === 'comment') {\n    results.push(\n      ...collectText(tree, {\n        whitespace,\n        breakBefore: true,\n        breakAfter: true\n      })\n    )\n  }\n\n  // 1.  If this element is not being rendered, or if the user agent is a\n  //     non-CSS user agent, then return the same value as the textContent IDL\n  //     attribute on this element.\n  //\n  //     Note: we’re not supporting stylesheets so we’re acting as if the node\n  //     is rendered.\n  //\n  //     If you want that behavior use `hast-util-to-string`.\n  //     Important: we’ll have to account for this later though.\n\n  // 2.  Let results be a new empty list.\n  let index = -1\n\n  // 3.  For each child node node of this element:\n  while (++index < children.length) {\n    // 3.1. Let current be the list resulting in running the inner text\n    //      collection steps with node.\n    //      Each item in results will either be a JavaScript string or a\n    //      positive integer (a required line break count).\n    // 3.2. For each item item in current, append item to results.\n    results.push(\n      ...renderedTextCollection(\n        children[index],\n        // @ts-expect-error: `tree` is a parent if we’re here.\n        tree,\n        {\n          whitespace,\n          breakBefore: index ? undefined : block,\n          breakAfter:\n            index < children.length - 1 ? br(children[index + 1]) : block\n        }\n      )\n    )\n  }\n\n  // 4.  Remove any items from results that are the empty string.\n  // 5.  Remove any runs of consecutive required line break count items at the\n  //     start or end of results.\n  // 6.  Replace each remaining run of consecutive required line break count\n  //     items with a string consisting of as many U+000A LINE FEED (LF)\n  //     characters as the maximum of the values in the required line break\n  //     count items.\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {number | undefined} */\n  let count\n\n  index = -1\n\n  while (++index < results.length) {\n    const value = results[index]\n\n    if (typeof value === 'number') {\n      if (count !== undefined && value > count) count = value\n    } else if (value) {\n      if (count !== undefined && count > -1) {\n        result.push('\\n'.repeat(count) || ' ')\n      }\n\n      count = -1\n      result.push(value)\n    }\n  }\n\n  // 7.  Return the concatenation of the string items in results.\n  return result.join('')\n}\n\n/**\n * <https://html.spec.whatwg.org/multipage/dom.html#rendered-text-collection-steps>\n *\n * @param {Nodes} node\n * @param {Parents} parent\n * @param {CollectionInfo} info\n * @returns {Array<BreakNumber | string>}\n */\nfunction renderedTextCollection(node, parent, info) {\n  if (node.type === 'element') {\n    return collectElement(node, parent, info)\n  }\n\n  if (node.type === 'text') {\n    return info.whitespace === 'normal'\n      ? collectText(node, info)\n      : collectPreText(node)\n  }\n\n  return []\n}\n\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parents} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n */\nfunction collectElement(node, parent, info) {\n  // First we infer the `white-space` property.\n  const whitespace = inferWhitespace(node, info)\n  const children = node.children || []\n  let index = -1\n  /** @type {Array<BreakNumber | string>} */\n  let items = []\n\n  // We’re ignoring point 3, and exiting without any content here, because we\n  // deviated from the spec in `toText` at step 3.\n  if (notRendered(node)) {\n    return items\n  }\n\n  /** @type {BreakNumber | undefined} */\n  let prefix\n  /** @type {BreakForce | BreakNumber | undefined} */\n  let suffix\n  // Note: we first detect if there is going to be a break before or after the\n  // contents, as that changes the white-space handling.\n\n  // 2.  If node’s computed value of `visibility` is not `visible`, then return\n  //     items.\n  //\n  //     Note: Ignored, as everything is visible by default user agent styles.\n\n  // 3.  If node is not being rendered, then return items. [...]\n  //\n  //     Note: We already did this above.\n\n  // See `collectText` for step 4.\n\n  // 5.  If node is a `<br>` element, then append a string containing a single\n  //     U+000A LINE FEED (LF) character to items.\n  if (br(node)) {\n    suffix = '\\n'\n  }\n\n  // 7.  If node’s computed value of `display` is `table-row`, and node’s CSS\n  //     box is not the last `table-row` box of the nearest ancestor `table`\n  //     box, then append a string containing a single U+000A LINE FEED (LF)\n  //     character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  //     Note: needs further investigation as this does not account for implicit\n  //     rows.\n  else if (\n    row(node) &&\n    // @ts-expect-error: something up with types of parents.\n    (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, row)\n  ) {\n    suffix = '\\n'\n  }\n\n  // 8.  If node is a `<p>` element, then append 2 (a required line break count)\n  //     at the beginning and end of items.\n  else if (p(node)) {\n    prefix = 2\n    suffix = 2\n  }\n\n  // 9.  If node’s used value of `display` is block-level or `table-caption`,\n  //     then append 1 (a required line break count) at the beginning and end of\n  //     items.\n  else if (blockOrCaption(node)) {\n    prefix = 1\n    suffix = 1\n  }\n\n  // 1.  Let items be the result of running the inner text collection steps with\n  //     each child node of node in tree order, and then concatenating the\n  //     results to a single list.\n  while (++index < children.length) {\n    items = items.concat(\n      renderedTextCollection(children[index], node, {\n        whitespace,\n        breakBefore: index ? undefined : prefix,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : suffix\n      })\n    )\n  }\n\n  // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n  //     box is not the last `table-cell` box of its enclosing `table-row` box,\n  //     then append a string containing a single U+0009 CHARACTER TABULATION\n  //     (tab) character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  if (\n    cell(node) &&\n    // @ts-expect-error: something up with types of parents.\n    (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, cell)\n  ) {\n    items.push('\\t')\n  }\n\n  // Add the pre- and suffix.\n  if (prefix) items.unshift(prefix)\n  if (suffix) items.push(suffix)\n\n  return items\n}\n\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Comment | Text} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectText(node, info) {\n  const value = String(node.value)\n  /** @type {Array<string>} */\n  const lines = []\n  /** @type {Array<BreakNumber | string>} */\n  const result = []\n  let start = 0\n\n  while (start <= value.length) {\n    searchLineFeeds.lastIndex = start\n\n    const match = searchLineFeeds.exec(value)\n    const end = match && 'index' in match ? match.index : value.length\n\n    lines.push(\n      // Any sequence of collapsible spaces and tabs immediately preceding or\n      // following a segment break is removed.\n      trimAndCollapseSpacesAndTabs(\n        // […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value\n          .slice(start, end)\n          .replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, ''),\n        start === 0 ? info.breakBefore : true,\n        end === value.length ? info.breakAfter : true\n      )\n    )\n\n    start = end + 1\n  }\n\n  // Collapsible segment breaks are transformed for rendering according to the\n  // segment break transformation rules.\n  // So here we jump to 4.1.2 of [CSSTEXT]:\n  // Any collapsible segment break immediately following another collapsible\n  // segment break is removed\n  let index = -1\n  /** @type {BreakNumber | undefined} */\n  let join\n\n  while (++index < lines.length) {\n    // *   If the character immediately before or immediately after the segment\n    //     break is the zero-width space character (U+200B), then the break is\n    //     removed, leaving behind the zero-width space.\n    if (\n      lines[index].charCodeAt(lines[index].length - 1) === 0x20_0b /* ZWSP */ ||\n      (index < lines.length - 1 &&\n        lines[index + 1].charCodeAt(0) === 0x20_0b) /* ZWSP */\n    ) {\n      result.push(lines[index])\n      join = undefined\n    }\n\n    // *   Otherwise, if the East Asian Width property [UAX11] of both the\n    //     character before and after the segment break is Fullwidth, Wide, or\n    //     Halfwidth (not Ambiguous), and neither side is Hangul, then the\n    //     segment break is removed.\n    //\n    //     Note: ignored.\n    // *   Otherwise, if the writing system of the segment break is Chinese,\n    //     Japanese, or Yi, and the character before or after the segment break\n    //     is punctuation or a symbol (Unicode general category P* or S*) and\n    //     has an East Asian Width property of Ambiguous, and the character on\n    //     the other side of the segment break is Fullwidth, Wide, or Halfwidth,\n    //     and not Hangul, then the segment break is removed.\n    //\n    //     Note: ignored.\n\n    // *   Otherwise, the segment break is converted to a space (U+0020).\n    else if (lines[index]) {\n      if (typeof join === 'number') result.push(join)\n      result.push(lines[index])\n      join = 0\n    } else if (index === 0 || index === lines.length - 1) {\n      // If this line is empty, and it’s the first or last, add a space.\n      // Note that this function is only called in normal whitespace, so we\n      // don’t worry about `pre`.\n      result.push(0)\n    }\n  }\n\n  return result\n}\n\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectPreText(node) {\n  return [String(node.value)]\n}\n\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */\nfunction trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  /** @type {number | undefined} */\n  let end\n\n  while (start < value.length) {\n    searchTabOrSpaces.lastIndex = start\n    const match = searchTabOrSpaces.exec(value)\n    end = match ? match.index : value.length\n\n    // If we’re not directly after a segment break, but there was white space,\n    // add an empty value that will be turned into a space.\n    if (!start && !end && match && !breakBefore) {\n      result.push('')\n    }\n\n    if (start !== end) {\n      result.push(value.slice(start, end))\n    }\n\n    start = match ? end + match[0].length : end\n  }\n\n  // If we reached the end, there was trailing white space, and there’s no\n  // segment break after this node, add an empty value that will be turned\n  // into a space.\n  if (start !== end && !breakAfter) {\n    result.push('')\n  }\n\n  return result.join(' ')\n}\n\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Nodes} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */\nfunction inferWhitespace(node, info) {\n  if (node.type === 'element') {\n    const properties = node.properties || {}\n    switch (node.tagName) {\n      case 'listing':\n      case 'plaintext':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return properties.noWrap ? 'nowrap' : info.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return info.whitespace\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {properties: {hidden: true}}}\n */\nfunction hidden(node) {\n  return Boolean((node.properties || {}).hidden)\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {tagName: 'td' | 'th'}}\n */\nfunction isCell(node) {\n  return node.tagName === 'td' || node.tagName === 'th'\n}\n\n/**\n * @type {TestFunction}\n */\nfunction closedDialog(node) {\n  return node.tagName === 'dialog' && !(node.properties || {}).open\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-text/lib/index.js\n");

/***/ })

};
;