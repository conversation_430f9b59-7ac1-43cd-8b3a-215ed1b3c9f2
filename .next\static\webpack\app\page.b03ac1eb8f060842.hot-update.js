"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/documents/document-upload.tsx":
/*!******************************************************!*\
  !*** ./src/components/documents/document-upload.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentUpload: () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,File,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ DocumentUpload auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DocumentUpload() {\n    _s();\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fetch document status on component mount and periodically\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentUpload.useEffect\": ()=>{\n            fetchDocumentStatus();\n            const interval = setInterval(fetchDocumentStatus, 5000); // Check every 5 seconds\n            return ({\n                \"DocumentUpload.useEffect\": ()=>clearInterval(interval)\n            })[\"DocumentUpload.useEffect\"];\n        }\n    }[\"DocumentUpload.useEffect\"], []);\n    const fetchDocumentStatus = async ()=>{\n        try {\n            const response = await fetch('/api/documents/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUploadedFiles(data.documents.map((doc)=>({\n                        id: doc.id,\n                        name: doc.originalName || doc.filename,\n                        size: doc.size,\n                        type: '',\n                        status: doc.status,\n                        errorMessage: doc.errorMessage\n                    })));\n            }\n        } catch (error) {\n            console.error('Failed to fetch document status:', error);\n        }\n    };\n    const handleUpload = async (files)=>{\n        try {\n            for (const file of files){\n                // Create FormData for upload\n                const formData = new FormData();\n                formData.append('file', file);\n                // Upload file to API\n                const response = await fetch('/api/documents/upload', {\n                    method: 'POST',\n                    body: formData\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to upload \".concat(file.name));\n                }\n                const result = await response.json();\n                console.log(\"Successfully uploaded: \".concat(file.name));\n            }\n            // Refresh document status after upload\n            await fetchDocumentStatus();\n        } catch (error) {\n            console.error('Upload error:', error);\n            throw error; // Let the enhanced file upload component handle the error\n        }\n    };\n    const handleRemoveFile = async (fileId)=>{\n        try {\n            const response = await fetch(\"/api/documents/\".concat(fileId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                setUploadedFiles((prev)=>prev.filter((file)=>file.id !== fileId));\n            } else {\n                alert('Failed to remove file');\n            }\n        } catch (error) {\n            console.error('Remove file error:', error);\n            alert('Failed to remove file');\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'uploading':\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 16\n                }, this);\n            case 'indexed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'uploading':\n                return 'Uploading...';\n            case 'processing':\n                return 'Processing...';\n            case 'indexed':\n                return 'Ready';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Ready';\n        }\n    };\n    const handleFileSelect = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        setIsUploading(true);\n        try {\n            await handleUpload(Array.from(files));\n        } catch (error) {\n            console.error('Upload failed:', error);\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            setIsUploading(true);\n            try {\n                await handleUpload(Array.from(files));\n            } catch (error) {\n                console.error('Upload failed:', error);\n            } finally{\n                setIsUploading(false);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-2 border-dashed transition-all duration-300 cursor-pointer group \".concat(isDragOver ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg' : 'border-border hover:border-primary/50 hover:bg-muted/30 hover:shadow-md'),\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-300 \".concat(isDragOver ? 'bg-primary text-primary-foreground scale-110' : 'bg-gradient-to-br from-muted to-muted/50 text-muted-foreground group-hover:scale-105 group-hover:from-primary/20 group-hover:to-primary/10'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 transition-transform duration-300 \".concat(isDragOver ? 'scale-110' : 'group-hover:scale-110')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2 transition-colors duration-300 \".concat(isDragOver ? 'text-primary' : 'text-foreground'),\n                                children: isDragOver ? '✨ Drop files here' : 'Upload your documents'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-4\",\n                                children: \"Drag and drop files here, or click to browse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2 text-sm text-muted-foreground mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-muted/50 rounded-full\",\n                                        children: \"Max file size: 10 MB\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-muted/50 rounded-full\",\n                                        children: \"Max files: 10\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                disabled: isUploading,\n                                className: \"transition-all duration-300 hover:scale-105 hover:shadow-md\",\n                                children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Uploading...\"\n                                    ]\n                                }, void 0, true) : 'Choose Files'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground mt-2\",\n                                children: \"Supported formats: .txt, .md\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        className: \"hidden\",\n                        accept: \".txt,.md\",\n                        onChange: handleFileSelect\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            uploadedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-5 rounded bg-primary/10 flex items-center justify-center\",\n                                children: \"\\uD83D\\uDCC1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            \"Uploaded Documents (\",\n                            uploadedFiles.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    uploadedFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"p-4 transition-all duration-200 hover:shadow-md hover:scale-[1.01] group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-lg bg-muted/50 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium group-hover:text-primary transition-colors\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-0.5 bg-muted/50 rounded-full\",\n                                                                children: formatFileSize(file.size)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    getStatusIcon(file.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium \".concat(file.status === 'indexed' ? 'text-green-600' : file.status === 'error' ? 'text-red-600' : 'text-blue-600'),\n                                                                        children: getStatusText(file.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    file.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 p-2 bg-red-50 border border-red-200 rounded-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-600\",\n                                                            children: file.errorMessage\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleRemoveFile(file.id),\n                                        disabled: file.status === 'processing',\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-50 hover:text-red-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this)\n                        }, file.id, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this),\n            uploadedFiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-full bg-muted/30 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-8 h-8 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: \"No files uploaded yet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs mt-1 opacity-75\",\n                        children: \"Upload documents to start chatting\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\components\\\\documents\\\\document-upload.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"z9LA/xowQ39FNkw5vGmX3b76ZvI=\");\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/documents/document-upload.tsx\n"));

/***/ })

});