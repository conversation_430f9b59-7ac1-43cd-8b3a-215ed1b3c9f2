import { FileReader, Document } from '@llamaindex/core/schema';

/**
 * Read a .txt file
 */ class TextFileReader extends FileReader {
    async loadDataAsContent(fileContent) {
        const decoder = new TextDecoder("utf-8");
        const dataBuffer = decoder.decode(fileContent);
        return [
            new Document({
                text: dataBuffer
            })
        ];
    }
}

export { TextFileReader };
