import { FileReader, Document } from '@llamaindex/core/schema';
import { parse, Options } from 'csv-parse';

/**
 * CSV parser
 */
declare class CSVReader extends FileReader<Document> {
    static parse: typeof parse;
    private concatRows;
    private colJoiner;
    private rowJoiner;
    private config;
    /**
     * Constructs a new instance of the class.
     * @param concatRows - Whether to concatenate all rows into one document.If set to False, a Document will be created for each row. `True` by default.
     * @param colJoiner - Separator to use for joining cols per row. Set to ", " by default.
     * @param rowJoiner - Separator to use for joining each row.Only used when `concat_rows=True`.Set to "\n" by default.
     */
    constructor(concatRows?: boolean, colJoiner?: string, rowJoiner?: string, config?: Options);
    /**
     * Loads data from csv files
     * @param fileContent - The content of the file.
     * @returns An array of Documents.
     */
    loadDataAsContent(fileContent: Uint8Array): Promise<Document[]>;
}

export { CSVReader };
