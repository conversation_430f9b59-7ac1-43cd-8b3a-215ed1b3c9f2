import { FileReader, Document } from '@llamaindex/core/schema';
import { DOMParser, XMLSerializer } from '@xmldom/xmldom';

class XMLReader extends FileReader {
    /**
   * @param splitLevel  how deep to split the XML tree
   */ constructor({ splitLevel } = {}){
        super();
        this.splitLevel = splitLevel ?? 0;
    }
    /** XMLParser */ async loadDataAsContent(fileContent) {
        const xmlStr = new TextDecoder().decode(fileContent);
        const doc = new DOMParser().parseFromString(xmlStr, "application/xml");
        if (!doc || !doc.documentElement) {
            throw new Error("Invalid XML: unable to parse document");
        }
        const root = doc.documentElement;
        return this._parseElementToDocuments(root);
    }
    /**
   * Internal: split tree into leaf or level-matched nodes
   */ _getLeafNodesUpToLevel(root, level) {
        const result = [];
        const traverse = (node, currLevel)=>{
            const children = Array.from(node.childNodes).filter((n)=>n.nodeType === n.ELEMENT_NODE);
            if (children.length === 0 || currLevel === level) {
                result.push(node);
            } else {
                for (const child of children){
                    traverse(child, currLevel + 1);
                }
            }
        };
        traverse(root, 0);
        return result;
    }
    _parseElementToDocuments(root) {
        const nodes = this._getLeafNodesUpToLevel(root, this.splitLevel);
        const serializer = new XMLSerializer();
        return nodes.map((node)=>new Document({
                text: serializer.serializeToString(node).trim()
            }));
    }
}

export { XMLReader };
